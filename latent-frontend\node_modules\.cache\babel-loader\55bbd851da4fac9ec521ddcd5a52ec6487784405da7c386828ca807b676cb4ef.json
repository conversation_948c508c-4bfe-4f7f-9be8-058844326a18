{"ast": null, "code": "import axios from 'axios';\nimport supabase from '../supabase';\n\n// Create axios instance with default config\nconst api = axios.create({\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add request interceptor for auth token\napi.interceptors.request.use(async config => {\n  const {\n    data: {\n      session\n    }\n  } = await supabase.auth.getSession();\n  const token = session === null || session === void 0 ? void 0 : session.access_token;\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => Promise.reject(error));\n\n// Add response interceptor to handle 401 errors\napi.interceptors.response.use(async response => {\n  // Check for 200 status\n  if (response.status === 200) {\n    return response;\n  }\n  return response;\n}, async error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    console.log('Unauthorized access detected, logging out user');\n    await supabase.auth.signOut();\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\n\n// API service methods\nconst apiService = {\n  // GET request\n  get: async url => {\n    try {\n      const response = await api.get(url);\n      return response.data;\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('API GET Error:', error);\n      let errorMessage = 'API GET request failed.';\n      if (error !== null && error !== void 0 && (_error$response2 = error.response) !== null && _error$response2 !== void 0 && (_error$response2$data = _error$response2.data) !== null && _error$response2$data !== void 0 && _error$response2$data.detail) {\n        var _error$response3, _error$response3$data;\n        errorMessage = error === null || error === void 0 ? void 0 : (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail;\n      }\n      const errorToThrow = new Error(errorMessage);\n      errorToThrow.response = error.response;\n      throw errorToThrow;\n    }\n  },\n  // POST request\n  post: async (url, data) => {\n    try {\n      const response = await api.post(url, data);\n      return response.data;\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      console.error('API POST Error:', error);\n      let errorMessage = 'API POST request failed.';\n      if (error !== null && error !== void 0 && (_error$response4 = error.response) !== null && _error$response4 !== void 0 && (_error$response4$data = _error$response4.data) !== null && _error$response4$data !== void 0 && _error$response4$data.detail) {\n        var _error$response5, _error$response5$data;\n        errorMessage = error === null || error === void 0 ? void 0 : (_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail;\n      }\n      const errorToThrow = new Error(errorMessage);\n      errorToThrow.response = error.response;\n      throw errorToThrow;\n    }\n  },\n  // PUT request\n  put: async (url, data) => {\n    try {\n      const response = await api.put(url, data);\n      return response.data;\n    } catch (error) {\n      var _error$response6, _error$response6$data;\n      console.error('API PUT Error:', error);\n      let errorMessage = 'API PUT request failed.';\n      if (error !== null && error !== void 0 && (_error$response6 = error.response) !== null && _error$response6 !== void 0 && (_error$response6$data = _error$response6.data) !== null && _error$response6$data !== void 0 && _error$response6$data.detail) {\n        var _error$response7, _error$response7$data;\n        errorMessage = error === null || error === void 0 ? void 0 : (_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.detail;\n      }\n      const errorToThrow = new Error(errorMessage);\n      errorToThrow.response = error.response;\n      throw errorToThrow;\n    }\n  },\n  // DELETE request\n  delete: async url => {\n    try {\n      const response = await api.delete(url);\n      return response.data;\n    } catch (error) {\n      var _error$response8, _error$response8$data;\n      console.error('API DELETE Error:', error);\n      let errorMessage = 'API DELETE request failed.';\n      if (error !== null && error !== void 0 && (_error$response8 = error.response) !== null && _error$response8 !== void 0 && (_error$response8$data = _error$response8.data) !== null && _error$response8$data !== void 0 && _error$response8$data.detail) {\n        var _error$response9, _error$response9$data;\n        errorMessage = error === null || error === void 0 ? void 0 : (_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : (_error$response9$data = _error$response9.data) === null || _error$response9$data === void 0 ? void 0 : _error$response9$data.detail;\n      }\n      const errorToThrow = new Error(errorMessage);\n      errorToThrow.response = error.response;\n      throw errorToThrow;\n    }\n  },\n  // PATCH request - updated to use the api instance with interceptors\n  patch: async (url, data) => {\n    try {\n      const response = await api.patch(url, data);\n      return response.data;\n    } catch (error) {\n      var _error$response10, _error$response10$dat;\n      console.error(`Error making PATCH request to ${url}:`, error);\n      let errorMessage = 'API PATCH request failed.';\n      if (error !== null && error !== void 0 && (_error$response10 = error.response) !== null && _error$response10 !== void 0 && (_error$response10$dat = _error$response10.data) !== null && _error$response10$dat !== void 0 && _error$response10$dat.detail) {\n        var _error$response11, _error$response11$dat;\n        errorMessage = error === null || error === void 0 ? void 0 : (_error$response11 = error.response) === null || _error$response11 === void 0 ? void 0 : (_error$response11$dat = _error$response11.data) === null || _error$response11$dat === void 0 ? void 0 : _error$response11$dat.detail;\n      }\n      const errorToThrow = new Error(errorMessage);\n      errorToThrow.response = error.response;\n      throw errorToThrow;\n    }\n  }\n};\nexport default apiService;", "map": {"version": 3, "names": ["axios", "supabase", "api", "create", "headers", "interceptors", "request", "use", "config", "data", "session", "auth", "getSession", "token", "access_token", "Authorization", "error", "Promise", "reject", "response", "status", "_error$response", "console", "log", "signOut", "window", "location", "href", "apiService", "get", "url", "_error$response2", "_error$response2$data", "errorMessage", "detail", "_error$response3", "_error$response3$data", "errorToThrow", "Error", "post", "_error$response4", "_error$response4$data", "_error$response5", "_error$response5$data", "put", "_error$response6", "_error$response6$data", "_error$response7", "_error$response7$data", "delete", "_error$response8", "_error$response8$data", "_error$response9", "_error$response9$data", "patch", "_error$response10", "_error$response10$dat", "_error$response11", "_error$response11$dat"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\r\nimport supabase from '../supabase';\r\n\r\n// Create axios instance with default config\r\nconst api = axios.create({\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n});\r\n\r\n// Add request interceptor for auth token\r\napi.interceptors.request.use(\r\n  async (config) => {\r\n\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n    const token = session?.access_token;\r\n    if (token) {\r\n\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => Promise.reject(error)\r\n);\r\n\r\n// Add response interceptor to handle 401 errors\r\napi.interceptors.response.use(\r\n  async (response) => {\r\n    // Check for 200 status\r\n    if (response.status === 200) {\r\n      return response;\r\n    }\r\n    return response;\r\n  },\r\n  async (error) => {\r\n    if (error.response?.status === 401) {\r\n      console.log('Unauthorized access detected, logging out user');\r\n      await supabase.auth.signOut();\r\n      window.location.href = '/login';\r\n    }\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// API service methods\r\nconst apiService = {\r\n  // GET request\r\n  get: async (url) => {\r\n    try {\r\n      const response = await api.get(url);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('API GET Error:', error);\r\n      \r\n      let errorMessage = 'API GET request failed.';\r\n      if (error?.response?.data?.detail) {\r\n        errorMessage = error?.response?.data?.detail;\r\n      }\r\n      \r\n      const errorToThrow = new Error(errorMessage);\r\n      errorToThrow.response = error.response;\r\n      throw errorToThrow;\r\n    }\r\n  },\r\n\r\n  // POST request\r\n  post: async (url, data) => {\r\n    try {\r\n      const response = await api.post(url, data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('API POST Error:', error);\r\n      \r\n      let errorMessage = 'API POST request failed.';\r\n      if (error?.response?.data?.detail) {\r\n        errorMessage = error?.response?.data?.detail;\r\n      }\r\n      \r\n      const errorToThrow = new Error(errorMessage);\r\n      errorToThrow.response = error.response;\r\n      throw errorToThrow;\r\n    }\r\n  },\r\n\r\n  // PUT request\r\n  put: async (url, data) => {\r\n    try {\r\n      const response = await api.put(url, data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('API PUT Error:', error);\r\n      \r\n      let errorMessage = 'API PUT request failed.';\r\n      if (error?.response?.data?.detail) {\r\n        errorMessage = error?.response?.data?.detail;\r\n      }\r\n      \r\n      const errorToThrow = new Error(errorMessage);\r\n      errorToThrow.response = error.response;\r\n      throw errorToThrow;\r\n    }\r\n  },\r\n\r\n  // DELETE request\r\n  delete: async (url) => {\r\n    try {\r\n      const response = await api.delete(url);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('API DELETE Error:', error);\r\n      \r\n      let errorMessage = 'API DELETE request failed.';\r\n      if (error?.response?.data?.detail) {\r\n        errorMessage = error?.response?.data?.detail;\r\n      }\r\n      \r\n      const errorToThrow = new Error(errorMessage);\r\n      errorToThrow.response = error.response;\r\n      throw errorToThrow;\r\n    }\r\n  },\r\n\r\n  // PATCH request - updated to use the api instance with interceptors\r\n  patch: async (url, data) => {\r\n    try {\r\n      const response = await api.patch(url, data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error making PATCH request to ${url}:`, error);\r\n      \r\n      let errorMessage = 'API PATCH request failed.';\r\n      if (error?.response?.data?.detail) {\r\n        errorMessage = error?.response?.data?.detail;\r\n      }\r\n      \r\n      const errorToThrow = new Error(errorMessage);\r\n      errorToThrow.response = error.response;\r\n      throw errorToThrow;\r\n    }\r\n  }\r\n};\r\n\r\nexport default apiService; "], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,aAAa;;AAElC;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAF,GAAG,CAACG,YAAY,CAACC,OAAO,CAACC,GAAG,CAC1B,MAAOC,MAAM,IAAK;EAEhB,MAAM;IAAEC,IAAI,EAAE;MAAEC;IAAQ;EAAE,CAAC,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAACC,UAAU,CAAC,CAAC;EAC9D,MAAMC,KAAK,GAAGH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,YAAY;EACnC,IAAID,KAAK,EAAE;IAETL,MAAM,CAACJ,OAAO,CAACW,aAAa,GAAG,UAAUF,KAAK,EAAE;EAClD;EACA,OAAOL,MAAM;AACf,CAAC,EACAQ,KAAK,IAAKC,OAAO,CAACC,MAAM,CAACF,KAAK,CACjC,CAAC;;AAED;AACAd,GAAG,CAACG,YAAY,CAACc,QAAQ,CAACZ,GAAG,CAC3B,MAAOY,QAAQ,IAAK;EAClB;EACA,IAAIA,QAAQ,CAACC,MAAM,KAAK,GAAG,EAAE;IAC3B,OAAOD,QAAQ;EACjB;EACA,OAAOA,QAAQ;AACjB,CAAC,EACD,MAAOH,KAAK,IAAK;EAAA,IAAAK,eAAA;EACf,IAAI,EAAAA,eAAA,GAAAL,KAAK,CAACG,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBD,MAAM,MAAK,GAAG,EAAE;IAClCE,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;IAC7D,MAAMtB,QAAQ,CAACU,IAAI,CAACa,OAAO,CAAC,CAAC;IAC7BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOV,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,MAAMY,UAAU,GAAG;EACjB;EACAC,GAAG,EAAE,MAAOC,GAAG,IAAK;IAClB,IAAI;MACF,MAAMX,QAAQ,GAAG,MAAMjB,GAAG,CAAC2B,GAAG,CAACC,GAAG,CAAC;MACnC,OAAOX,QAAQ,CAACV,IAAI;IACtB,CAAC,CAAC,OAAOO,KAAK,EAAE;MAAA,IAAAe,gBAAA,EAAAC,qBAAA;MACdV,OAAO,CAACN,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MAEtC,IAAIiB,YAAY,GAAG,yBAAyB;MAC5C,IAAIjB,KAAK,aAALA,KAAK,gBAAAe,gBAAA,GAALf,KAAK,CAAEG,QAAQ,cAAAY,gBAAA,gBAAAC,qBAAA,GAAfD,gBAAA,CAAiBtB,IAAI,cAAAuB,qBAAA,eAArBA,qBAAA,CAAuBE,MAAM,EAAE;QAAA,IAAAC,gBAAA,EAAAC,qBAAA;QACjCH,YAAY,GAAGjB,KAAK,aAALA,KAAK,wBAAAmB,gBAAA,GAALnB,KAAK,CAAEG,QAAQ,cAAAgB,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiB1B,IAAI,cAAA2B,qBAAA,uBAArBA,qBAAA,CAAuBF,MAAM;MAC9C;MAEA,MAAMG,YAAY,GAAG,IAAIC,KAAK,CAACL,YAAY,CAAC;MAC5CI,YAAY,CAAClB,QAAQ,GAAGH,KAAK,CAACG,QAAQ;MACtC,MAAMkB,YAAY;IACpB;EACF,CAAC;EAED;EACAE,IAAI,EAAE,MAAAA,CAAOT,GAAG,EAAErB,IAAI,KAAK;IACzB,IAAI;MACF,MAAMU,QAAQ,GAAG,MAAMjB,GAAG,CAACqC,IAAI,CAACT,GAAG,EAAErB,IAAI,CAAC;MAC1C,OAAOU,QAAQ,CAACV,IAAI;IACtB,CAAC,CAAC,OAAOO,KAAK,EAAE;MAAA,IAAAwB,gBAAA,EAAAC,qBAAA;MACdnB,OAAO,CAACN,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MAEvC,IAAIiB,YAAY,GAAG,0BAA0B;MAC7C,IAAIjB,KAAK,aAALA,KAAK,gBAAAwB,gBAAA,GAALxB,KAAK,CAAEG,QAAQ,cAAAqB,gBAAA,gBAAAC,qBAAA,GAAfD,gBAAA,CAAiB/B,IAAI,cAAAgC,qBAAA,eAArBA,qBAAA,CAAuBP,MAAM,EAAE;QAAA,IAAAQ,gBAAA,EAAAC,qBAAA;QACjCV,YAAY,GAAGjB,KAAK,aAALA,KAAK,wBAAA0B,gBAAA,GAAL1B,KAAK,CAAEG,QAAQ,cAAAuB,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBjC,IAAI,cAAAkC,qBAAA,uBAArBA,qBAAA,CAAuBT,MAAM;MAC9C;MAEA,MAAMG,YAAY,GAAG,IAAIC,KAAK,CAACL,YAAY,CAAC;MAC5CI,YAAY,CAAClB,QAAQ,GAAGH,KAAK,CAACG,QAAQ;MACtC,MAAMkB,YAAY;IACpB;EACF,CAAC;EAED;EACAO,GAAG,EAAE,MAAAA,CAAOd,GAAG,EAAErB,IAAI,KAAK;IACxB,IAAI;MACF,MAAMU,QAAQ,GAAG,MAAMjB,GAAG,CAAC0C,GAAG,CAACd,GAAG,EAAErB,IAAI,CAAC;MACzC,OAAOU,QAAQ,CAACV,IAAI;IACtB,CAAC,CAAC,OAAOO,KAAK,EAAE;MAAA,IAAA6B,gBAAA,EAAAC,qBAAA;MACdxB,OAAO,CAACN,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MAEtC,IAAIiB,YAAY,GAAG,yBAAyB;MAC5C,IAAIjB,KAAK,aAALA,KAAK,gBAAA6B,gBAAA,GAAL7B,KAAK,CAAEG,QAAQ,cAAA0B,gBAAA,gBAAAC,qBAAA,GAAfD,gBAAA,CAAiBpC,IAAI,cAAAqC,qBAAA,eAArBA,qBAAA,CAAuBZ,MAAM,EAAE;QAAA,IAAAa,gBAAA,EAAAC,qBAAA;QACjCf,YAAY,GAAGjB,KAAK,aAALA,KAAK,wBAAA+B,gBAAA,GAAL/B,KAAK,CAAEG,QAAQ,cAAA4B,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBtC,IAAI,cAAAuC,qBAAA,uBAArBA,qBAAA,CAAuBd,MAAM;MAC9C;MAEA,MAAMG,YAAY,GAAG,IAAIC,KAAK,CAACL,YAAY,CAAC;MAC5CI,YAAY,CAAClB,QAAQ,GAAGH,KAAK,CAACG,QAAQ;MACtC,MAAMkB,YAAY;IACpB;EACF,CAAC;EAED;EACAY,MAAM,EAAE,MAAOnB,GAAG,IAAK;IACrB,IAAI;MACF,MAAMX,QAAQ,GAAG,MAAMjB,GAAG,CAAC+C,MAAM,CAACnB,GAAG,CAAC;MACtC,OAAOX,QAAQ,CAACV,IAAI;IACtB,CAAC,CAAC,OAAOO,KAAK,EAAE;MAAA,IAAAkC,gBAAA,EAAAC,qBAAA;MACd7B,OAAO,CAACN,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MAEzC,IAAIiB,YAAY,GAAG,4BAA4B;MAC/C,IAAIjB,KAAK,aAALA,KAAK,gBAAAkC,gBAAA,GAALlC,KAAK,CAAEG,QAAQ,cAAA+B,gBAAA,gBAAAC,qBAAA,GAAfD,gBAAA,CAAiBzC,IAAI,cAAA0C,qBAAA,eAArBA,qBAAA,CAAuBjB,MAAM,EAAE;QAAA,IAAAkB,gBAAA,EAAAC,qBAAA;QACjCpB,YAAY,GAAGjB,KAAK,aAALA,KAAK,wBAAAoC,gBAAA,GAALpC,KAAK,CAAEG,QAAQ,cAAAiC,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiB3C,IAAI,cAAA4C,qBAAA,uBAArBA,qBAAA,CAAuBnB,MAAM;MAC9C;MAEA,MAAMG,YAAY,GAAG,IAAIC,KAAK,CAACL,YAAY,CAAC;MAC5CI,YAAY,CAAClB,QAAQ,GAAGH,KAAK,CAACG,QAAQ;MACtC,MAAMkB,YAAY;IACpB;EACF,CAAC;EAED;EACAiB,KAAK,EAAE,MAAAA,CAAOxB,GAAG,EAAErB,IAAI,KAAK;IAC1B,IAAI;MACF,MAAMU,QAAQ,GAAG,MAAMjB,GAAG,CAACoD,KAAK,CAACxB,GAAG,EAAErB,IAAI,CAAC;MAC3C,OAAOU,QAAQ,CAACV,IAAI;IACtB,CAAC,CAAC,OAAOO,KAAK,EAAE;MAAA,IAAAuC,iBAAA,EAAAC,qBAAA;MACdlC,OAAO,CAACN,KAAK,CAAC,iCAAiCc,GAAG,GAAG,EAAEd,KAAK,CAAC;MAE7D,IAAIiB,YAAY,GAAG,2BAA2B;MAC9C,IAAIjB,KAAK,aAALA,KAAK,gBAAAuC,iBAAA,GAALvC,KAAK,CAAEG,QAAQ,cAAAoC,iBAAA,gBAAAC,qBAAA,GAAfD,iBAAA,CAAiB9C,IAAI,cAAA+C,qBAAA,eAArBA,qBAAA,CAAuBtB,MAAM,EAAE;QAAA,IAAAuB,iBAAA,EAAAC,qBAAA;QACjCzB,YAAY,GAAGjB,KAAK,aAALA,KAAK,wBAAAyC,iBAAA,GAALzC,KAAK,CAAEG,QAAQ,cAAAsC,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiBhD,IAAI,cAAAiD,qBAAA,uBAArBA,qBAAA,CAAuBxB,MAAM;MAC9C;MAEA,MAAMG,YAAY,GAAG,IAAIC,KAAK,CAACL,YAAY,CAAC;MAC5CI,YAAY,CAAClB,QAAQ,GAAGH,KAAK,CAACG,QAAQ;MACtC,MAAMkB,YAAY;IACpB;EACF;AACF,CAAC;AAED,eAAeT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}