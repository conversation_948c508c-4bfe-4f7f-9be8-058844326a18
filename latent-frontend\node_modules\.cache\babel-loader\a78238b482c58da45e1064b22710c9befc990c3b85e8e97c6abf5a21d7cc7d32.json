{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\zipp\\\\latent-frontend\\\\src\\\\components\\\\common\\\\DepartmentFilters\\\\DepartmentFilters.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './DepartmentFilters.css';\nimport departmentService from '../../../services/departmentService';\nimport LoadingSpinner from '../LoadingSpinner/LoadingSpinner';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DepartmentFilters = ({\n  onDepartmentChange,\n  defaultSelected = 'All'\n}) => {\n  _s();\n  const [selectedDepartment, setSelectedDepartment] = useState(defaultSelected);\n  const [departments, setDepartments] = useState(['All']);\n  const [loading, setLoading] = useState(true);\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\n  const [toShow, setToShow] = useState(false);\n  const dropdownRef = useRef(null);\n\n  // Fetch departments from API when component mounts\n  useEffect(() => {\n    const fetchDepartments = async () => {\n      try {\n        setLoading(true);\n        // Get departments from the service\n        const response = await departmentService.getAllDepartments();\n\n        // Combine \"All\" with the departments from the API\n        const allDepartments = ['All', ...response.departments];\n        setDepartments(allDepartments);\n        if (response.departments.length > 0) {\n          setToShow(true);\n        }\n      } catch (error) {\n        var _error$response, _error$response$data;\n        console.error('Error fetching departments:', error);\n        let errorMessage = 'Failed to fetch departments.';\n        if (error !== null && error !== void 0 && (_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.detail) {\n          var _error$response2, _error$response2$data;\n          errorMessage = error === null || error === void 0 ? void 0 : (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail;\n        }\n\n        // Fallback to default departments if API fails\n        setDepartments([\"All\"]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchDepartments();\n  }, []);\n  const handleDepartmentClick = dept => {\n    setSelectedDepartment(dept);\n    if (onDepartmentChange) {\n      onDepartmentChange(dept);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: toShow ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"department-filters-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"filter-heading\",\n        children: \"Category Filters\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 7\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"departments-loading\",\n        children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"department-drawer-container\",\n        ref: dropdownRef,\n        onMouseEnter: () => setIsDropdownOpen(true),\n        onMouseLeave: () => setIsDropdownOpen(false),\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"selected-department filter-btn active\",\n          children: selectedDepartment\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `department-drawer ${isDropdownOpen ? 'open' : ''}`,\n          children: departments.filter(dept => dept !== selectedDepartment).map((dept, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"filter-btn drawer-item\",\n            onClick: () => handleDepartmentClick(dept),\n            children: dept\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 17\n    }, this) : ''\n  }, void 0, false);\n};\n_s(DepartmentFilters, \"AOYZDNCZf01WcQW8mswd35UiYSg=\");\n_c = DepartmentFilters;\nexport default DepartmentFilters;\nvar _c;\n$RefreshReg$(_c, \"DepartmentFilters\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "departmentService", "LoadingSpinner", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DepartmentFilters", "onDepartmentChange", "defaultSelected", "_s", "selectedDepartment", "setSelectedDepartment", "departments", "setDepartments", "loading", "setLoading", "isDropdownOpen", "setIsDropdownOpen", "toShow", "setToShow", "dropdownRef", "fetchDepartments", "response", "getAllDepartments", "allDepartments", "length", "error", "_error$response", "_error$response$data", "console", "errorMessage", "data", "detail", "_error$response2", "_error$response2$data", "handleDepartmentClick", "dept", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "ref", "onMouseEnter", "onMouseLeave", "filter", "map", "index", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/components/common/DepartmentFilters/DepartmentFilters.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport './DepartmentFilters.css';\r\nimport departmentService from '../../../services/departmentService';\r\nimport LoadingSpinner from '../LoadingSpinner/LoadingSpinner';\r\n\r\n\r\nconst DepartmentFilters = ({ \r\n  onDepartmentChange,\r\n  defaultSelected = 'All'\r\n}) => {\r\n  const [selectedDepartment, setSelectedDepartment] = useState(defaultSelected);\r\n  const [departments, setDepartments] = useState(['All']);\r\n  const [loading, setLoading] = useState(true);\r\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\r\n  const [toShow, setToShow] = useState(false);  \r\n  const dropdownRef = useRef(null);\r\n\r\n  // Fetch departments from API when component mounts\r\n  useEffect(() => {\r\n    const fetchDepartments = async () => {\r\n      try {\r\n        setLoading(true);\r\n        // Get departments from the service\r\n        const response = await departmentService.getAllDepartments();\r\n        \r\n        // Combine \"All\" with the departments from the API\r\n        const allDepartments = ['All', ...response.departments];\r\n        \r\n        setDepartments(allDepartments);\r\n        if(response.departments.length > 0){\r\n          setToShow(true);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error fetching departments:', error);\r\n        \r\n        let errorMessage = 'Failed to fetch departments.';\r\n        if (error?.response?.data?.detail) {\r\n          errorMessage = error?.response?.data?.detail;\r\n        }\r\n        \r\n        // Fallback to default departments if API fails\r\n        setDepartments([\r\n          \"All\",\r\n        ]);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchDepartments();\r\n  }, []);\r\n\r\n  const handleDepartmentClick = (dept) => {\r\n    setSelectedDepartment(dept);\r\n    if (onDepartmentChange) {\r\n      onDepartmentChange(dept);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>{toShow?( <div className=\"department-filters-container\">\r\n      <h2 className=\"filter-heading\">Category Filters</h2>\r\n      \r\n      {loading ? (\r\n        <div className=\"departments-loading\">\r\n          <LoadingSpinner size=\"small\" />\r\n        </div>\r\n      ) : (\r\n        <div \r\n          className=\"department-drawer-container\" \r\n          ref={dropdownRef}\r\n          onMouseEnter={() => setIsDropdownOpen(true)}\r\n          onMouseLeave={() => setIsDropdownOpen(false)}\r\n        >\r\n          <button className=\"selected-department filter-btn active\">\r\n            {selectedDepartment}\r\n          </button>\r\n          \r\n          <div className={`department-drawer ${isDropdownOpen ? 'open' : ''}`}>\r\n            {departments\r\n              .filter(dept => dept !== selectedDepartment)\r\n              .map((dept, index) => (\r\n                <button\r\n                  key={index}\r\n                  className=\"filter-btn drawer-item\"\r\n                  onClick={() => handleDepartmentClick(dept)}\r\n                >\r\n                  {dept}\r\n                </button>\r\n              ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>):('')}</>\r\n   \r\n  );\r\n};\r\n\r\nexport default DepartmentFilters;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,yBAAyB;AAChC,OAAOC,iBAAiB,MAAM,qCAAqC;AACnE,OAAOC,cAAc,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAG9D,MAAMC,iBAAiB,GAAGA,CAAC;EACzBC,kBAAkB;EAClBC,eAAe,GAAG;AACpB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGd,QAAQ,CAACW,eAAe,CAAC;EAC7E,MAAM,CAACI,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC;EACvD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,cAAc,EAAEC,iBAAiB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACqB,MAAM,EAAEC,SAAS,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAMuB,WAAW,GAAGrB,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACAD,SAAS,CAAC,MAAM;IACd,MAAMuB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACFN,UAAU,CAAC,IAAI,CAAC;QAChB;QACA,MAAMO,QAAQ,GAAG,MAAMtB,iBAAiB,CAACuB,iBAAiB,CAAC,CAAC;;QAE5D;QACA,MAAMC,cAAc,GAAG,CAAC,KAAK,EAAE,GAAGF,QAAQ,CAACV,WAAW,CAAC;QAEvDC,cAAc,CAACW,cAAc,CAAC;QAC9B,IAAGF,QAAQ,CAACV,WAAW,CAACa,MAAM,GAAG,CAAC,EAAC;UACjCN,SAAS,CAAC,IAAI,CAAC;QACjB;MACF,CAAC,CAAC,OAAOO,KAAK,EAAE;QAAA,IAAAC,eAAA,EAAAC,oBAAA;QACdC,OAAO,CAACH,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QAEnD,IAAII,YAAY,GAAG,8BAA8B;QACjD,IAAIJ,KAAK,aAALA,KAAK,gBAAAC,eAAA,GAALD,KAAK,CAAEJ,QAAQ,cAAAK,eAAA,gBAAAC,oBAAA,GAAfD,eAAA,CAAiBI,IAAI,cAAAH,oBAAA,eAArBA,oBAAA,CAAuBI,MAAM,EAAE;UAAA,IAAAC,gBAAA,EAAAC,qBAAA;UACjCJ,YAAY,GAAGJ,KAAK,aAALA,KAAK,wBAAAO,gBAAA,GAALP,KAAK,CAAEJ,QAAQ,cAAAW,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBF,IAAI,cAAAG,qBAAA,uBAArBA,qBAAA,CAAuBF,MAAM;QAC9C;;QAEA;QACAnB,cAAc,CAAC,CACb,KAAK,CACN,CAAC;MACJ,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDM,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMc,qBAAqB,GAAIC,IAAI,IAAK;IACtCzB,qBAAqB,CAACyB,IAAI,CAAC;IAC3B,IAAI7B,kBAAkB,EAAE;MACtBA,kBAAkB,CAAC6B,IAAI,CAAC;IAC1B;EACF,CAAC;EAED,oBACEjC,OAAA,CAAAE,SAAA;IAAAgC,QAAA,EAAGnB,MAAM,gBAAGf,OAAA;MAAKmC,SAAS,EAAC,8BAA8B;MAAAD,QAAA,gBACvDlC,OAAA;QAAImC,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAEnD5B,OAAO,gBACNX,OAAA;QAAKmC,SAAS,EAAC,qBAAqB;QAAAD,QAAA,eAClClC,OAAA,CAACF,cAAc;UAAC0C,IAAI,EAAC;QAAO;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,gBAENvC,OAAA;QACEmC,SAAS,EAAC,6BAA6B;QACvCM,GAAG,EAAExB,WAAY;QACjByB,YAAY,EAAEA,CAAA,KAAM5B,iBAAiB,CAAC,IAAI,CAAE;QAC5C6B,YAAY,EAAEA,CAAA,KAAM7B,iBAAiB,CAAC,KAAK,CAAE;QAAAoB,QAAA,gBAE7ClC,OAAA;UAAQmC,SAAS,EAAC,uCAAuC;UAAAD,QAAA,EACtD3B;QAAkB;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eAETvC,OAAA;UAAKmC,SAAS,EAAE,qBAAqBtB,cAAc,GAAG,MAAM,GAAG,EAAE,EAAG;UAAAqB,QAAA,EACjEzB,WAAW,CACTmC,MAAM,CAACX,IAAI,IAAIA,IAAI,KAAK1B,kBAAkB,CAAC,CAC3CsC,GAAG,CAAC,CAACZ,IAAI,EAAEa,KAAK,kBACf9C,OAAA;YAEEmC,SAAS,EAAC,wBAAwB;YAClCY,OAAO,EAAEA,CAAA,KAAMf,qBAAqB,CAACC,IAAI,CAAE;YAAAC,QAAA,EAE1CD;UAAI,GAJAa,KAAK;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,GAAG;EAAG,gBAAG,CAAC;AAGpB,CAAC;AAACjC,EAAA,CA1FIH,iBAAiB;AAAA6C,EAAA,GAAjB7C,iBAAiB;AA4FvB,eAAeA,iBAAiB;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}