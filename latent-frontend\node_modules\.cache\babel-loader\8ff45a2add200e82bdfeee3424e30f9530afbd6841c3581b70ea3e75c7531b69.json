{"ast": null, "code": "import API_URLS from \"../config/apiUrls\";\nimport apiService from \"./api\";\nexport const queryAPI = async (query, userId = null, filterTitles = null, chatHistory = [], blobToTitleMapping = null) => {\n  try {\n    const requestBody = {\n      query,\n      user_id: userId,\n      filter_titles: [],\n      chat_history: []\n    };\n\n    // Add filter titles if provided\n    if (filterTitles && filterTitles.length > 0) {\n      requestBody.filter_titles = filterTitles;\n    }\n\n    // Add chat history if provided\n    if (chatHistory && chatHistory.length > 0) {\n      requestBody.chat_history = chatHistory;\n    }\n\n    // Add blob to title mapping if provided\n    if (blobToTitleMapping) {\n      requestBody.blob_to_title_mapping = blobToTitleMapping;\n    }\n\n    //console.log(\"Making request to:\", API_URLS.CHAT.QUERY);\n    //console.log(\"With body:\", requestBody);\n    const response = await apiService.post(API_URLS.CHAT.QUERY, requestBody);\n    //console.log(\"Got response:\", response);\n    return response;\n  } catch (error) {\n    console.error(\"API Error:\", error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["API_URLS", "apiService", "queryAPI", "query", "userId", "filterTitles", "chatHistory", "blobToTitleMapping", "requestBody", "user_id", "filter_titles", "chat_history", "length", "blob_to_title_mapping", "response", "post", "CHAT", "QUERY", "error", "console"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/services/queryAPI.js"], "sourcesContent": ["import API_URLS from \"../config/apiUrls\";\r\nimport apiService from \"./api\";\r\n\r\nexport const queryAPI = async (\r\n  query,\r\n  userId = null,\r\n  filterTitles = null,\r\n  chatHistory = [],\r\n  blobToTitleMapping = null\r\n) => {\r\n  try {\r\n    const requestBody = {\r\n      query,\r\n      user_id: userId,\r\n      filter_titles: [],\r\n      chat_history: [],\r\n    };\r\n\r\n    // Add filter titles if provided\r\n    if (filterTitles && filterTitles.length > 0) {\r\n      requestBody.filter_titles = filterTitles;\r\n    }\r\n\r\n    // Add chat history if provided\r\n    if (chatHistory && chatHistory.length > 0) {\r\n      requestBody.chat_history = chatHistory;\r\n    }\r\n\r\n    // Add blob to title mapping if provided\r\n    if (blobToTitleMapping) {\r\n      requestBody.blob_to_title_mapping = blobToTitleMapping;\r\n    }\r\n\r\n    //console.log(\"Making request to:\", API_URLS.CHAT.QUERY);\r\n    //console.log(\"With body:\", requestBody);\r\n    const response = await apiService.post(API_URLS.CHAT.QUERY, requestBody);\r\n    //console.log(\"Got response:\", response);\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"API Error:\", error);\r\n    throw error;\r\n  }\r\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,mBAAmB;AACxC,OAAOC,UAAU,MAAM,OAAO;AAE9B,OAAO,MAAMC,QAAQ,GAAG,MAAAA,CACtBC,KAAK,EACLC,MAAM,GAAG,IAAI,EACbC,YAAY,GAAG,IAAI,EACnBC,WAAW,GAAG,EAAE,EAChBC,kBAAkB,GAAG,IAAI,KACtB;EACH,IAAI;IACF,MAAMC,WAAW,GAAG;MAClBL,KAAK;MACLM,OAAO,EAAEL,MAAM;MACfM,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE;IAChB,CAAC;;IAED;IACA,IAAIN,YAAY,IAAIA,YAAY,CAACO,MAAM,GAAG,CAAC,EAAE;MAC3CJ,WAAW,CAACE,aAAa,GAAGL,YAAY;IAC1C;;IAEA;IACA,IAAIC,WAAW,IAAIA,WAAW,CAACM,MAAM,GAAG,CAAC,EAAE;MACzCJ,WAAW,CAACG,YAAY,GAAGL,WAAW;IACxC;;IAEA;IACA,IAAIC,kBAAkB,EAAE;MACtBC,WAAW,CAACK,qBAAqB,GAAGN,kBAAkB;IACxD;;IAEA;IACA;IACA,MAAMO,QAAQ,GAAG,MAAMb,UAAU,CAACc,IAAI,CAACf,QAAQ,CAACgB,IAAI,CAACC,KAAK,EAAET,WAAW,CAAC;IACxE;IACA,OAAOM,QAAQ;EACjB,CAAC,CAAC,OAAOI,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}