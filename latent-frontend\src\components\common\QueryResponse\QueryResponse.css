.query-response-container {
    padding: 0.5rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    width: 100%;
    box-sizing: border-box;
    overflow-wrap: break-word;
    padding-bottom: 5rem;
  }
  
  .chat-entry {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    width: 100%;
  }

  .chat-entry-user .chat-content {
  text-align: right;
  margin-left: auto;  
}

.chat-entry-user .chat-timestamp {
  justify-content: flex-end;  
}

   

    
  
   .chat-entry-user {
    justify-content: flex-end;  
    flex-direction: row-reverse; 
}

.chat-entry-ai {
    justify-content: flex-start; 
    flex-direction: row;  
}

.chat-entry-ai .chat-content {
    text-align: left;  
}
  
  .chat-avatar {
    width: 2rem;
    height: 2rem;
    border-radius: 9999px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
  
  .user-avatar {
    background-color: var(--primary-blue-light);
  }
  
  .ai-avatar {
      width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: white;
  padding: 0;
  }

  .ai-logo {
  width: 80%;
  height: 80%;
  object-fit: contain;
  display: block;
}
  
  .chat-icon {
    width: 1rem;
    height: 1rem;
  }
  
  .user-icon {
    color: var(--primary-blue);
  }
  
  .ai-icon {
    color: var(--primary-blue);
  }
  
  
  .chat-bubble {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    background-color: white;
    max-width: 100%; 
  }
  
  .user-bubble {
    background-color: white;
    border-color: #e5e7eb;
  }
  
  .ai-bubble {
    background-color: var(--primary-blue-light);
    border-color: var(--primary-blue);
  }
  
  .response-heading {
    font-weight: 600;
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
  }
  
  .response-text {
    color: #1f2937;
    white-space: pre-wrap;
    line-height: 1.6;
  }
  
  .chat-timestamp {
    margin-top: 0.25rem;
    font-size: 0.75rem;
    color: #505153;
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }
  
  .timestamp-icon {
    width: 0.75rem;
    height: 0.75rem;
  }
  
   /* for the sources section  which has been commented in the js*/


  /* .sources-section {
    margin-top: 1rem;
  }
  
  .sources-heading {
    font-weight: 600;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
  }
  
  .source-icon {
    width: 1rem;
    height: 1rem;
  }
  
  .source-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .source-entry {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    transition: background-color 0.2s ease;
  }
  
  .source-entry:hover {
    background-color: #f3f4f6;
  }
  
  .source-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .source-title {
    font-weight: 500;
    color: #111827;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
  }
  
  .source-score {
    background-color: var(--primary-blue-light);
    color: var(--primary-blue);
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
  }
  
  */