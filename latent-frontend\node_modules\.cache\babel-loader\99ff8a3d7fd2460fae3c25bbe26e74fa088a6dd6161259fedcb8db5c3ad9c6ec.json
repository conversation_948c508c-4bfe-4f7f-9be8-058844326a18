{"ast": null, "code": "var _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport supabase from '../../../supabase';\nconst URLParamChecker = ({\n  children\n}) => {\n  _s();\n  const location = useLocation();\n  const navigate = useNavigate();\n  useEffect(() => {\n    console.log('=== URL PARAM CHECKER DEBUG ===');\n    console.log('Full URL:', window.location.href);\n    console.log('Search:', location.search);\n    console.log('Hash:', location.hash);\n    console.log('Referrer:', document.referrer);\n    console.log('Current pathname:', location.pathname);\n    const urlParams = new URLSearchParams(location.search);\n    const token = urlParams.get('token');\n    const type = urlParams.get('type');\n    console.log('Search params - token:', token, 'type:', type);\n\n    // Check URL hash for access_token (format: #access_token=...)\n    const hash = location.hash;\n    const hasAccessToken = hash && hash.includes('access_token');\n    console.log('Hash check - hasAccessToken:', hasAccessToken);\n\n    // Check if URL comes from Supabase confirmation email (sendibt3.com redirect)\n    const referrer = document.referrer;\n    const isSupabaseConfirmation = referrer && referrer.includes('sendibt3.com');\n    console.log('Referrer check - isSupabaseConfirmation:', isSupabaseConfirmation);\n\n    // Check for confirmation email indicators in current URL\n    const currentUrl = window.location.href;\n    const isConfirmationEmail = currentUrl.includes('sendibt3.com') || currentUrl.includes('confirmation') || currentUrl.includes('verify');\n    console.log('URL pattern check - isConfirmationEmail:', isConfirmationEmail);\n\n    // Check if user landed directly on dashboard (common with confirmation emails)\n    const isDashboardAccess = location.pathname === '/dashboard';\n\n    // Check various suspicious patterns for dashboard access\n    const isDashboardWithoutReferrer = isDashboardAccess && !document.referrer;\n    const isDashboardFromExternal = isDashboardAccess && document.referrer && !document.referrer.includes(window.location.origin);\n\n    // Check if this is a fresh session (no previous app navigation)\n    const lastAppNavigation = sessionStorage.getItem('last_app_navigation');\n    const isFreshSession = !lastAppNavigation;\n\n    // Strong indicators of confirmation email access\n    const isSuspiciousDashboardAccess = isDashboardAccess && (isDashboardWithoutReferrer || isDashboardFromExternal || isFreshSession && isDashboardAccess);\n    console.log('  - isSuspiciousDashboardAccess:', isSuspiciousDashboardAccess);\n\n    // If any confirmation indicators are present, clear session and redirect to login\n    if (token || type || hasAccessToken || isSupabaseConfirmation || isConfirmationEmail || isSuspiciousDashboardAccess) {\n      console.log('URL PARAM CHECKER - CONFIRMATION/SUSPICIOUS ACCESS DETECTED, CLEARING SESSION');\n\n      // Set flag to indicate confirmation email access\n      localStorage.setItem('confirmation_email_access', 'true');\n\n      // Clear the session before redirecting\n      const clearSessionAndRedirect = async () => {\n        try {\n          // Sign out from Supabase\n          await supabase.auth.signOut();\n          console.log('Session cleared successfully');\n        } catch (error) {\n          console.error('Error clearing session:', error);\n          // Continue with redirect even if signOut fails\n        } finally {\n          // Force a page reload to clear any cached state and redirect to login\n          window.location.href = '/login';\n        }\n      };\n      clearSessionAndRedirect();\n    } else {\n      console.log('No confirmation email indicators detected');\n      // Mark this as legitimate app navigation\n      if (location.pathname !== '/login') {\n        sessionStorage.setItem('last_app_navigation', Date.now().toString());\n      }\n    }\n    console.log('=== END URL PARAM CHECKER DEBUG ===');\n  }, [location.search, location.hash, location.pathname, navigate]);\n  return children;\n};\n_s(URLParamChecker, \"fPjCxODVr6jVvib6o2c9VLE4s84=\", false, function () {\n  return [useLocation, useNavigate];\n});\n_c = URLParamChecker;\nexport default URLParamChecker;\nvar _c;\n$RefreshReg$(_c, \"URLParamChecker\");", "map": {"version": 3, "names": ["React", "useEffect", "useLocation", "useNavigate", "supabase", "URLParamChecker", "children", "_s", "location", "navigate", "console", "log", "window", "href", "search", "hash", "document", "referrer", "pathname", "urlParams", "URLSearchParams", "token", "get", "type", "hasAccessToken", "includes", "isSupabaseConfirmation", "currentUrl", "isConfirmationEmail", "isDashboardAccess", "isDashboardWithoutReferrer", "isDashboardFromExternal", "origin", "lastAppNavigation", "sessionStorage", "getItem", "isFreshSession", "isSuspiciousDashboardAccess", "localStorage", "setItem", "clearSessionAndRedirect", "auth", "signOut", "error", "Date", "now", "toString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/components/common/URLParamChecker/URLParamChecker.js"], "sourcesContent": ["import React, { useEffect } from 'react';\r\nimport { useLocation, useNavigate } from 'react-router-dom';\r\nimport supabase from '../../../supabase';\r\n\r\nconst URLParamChecker = ({ children }) => {\r\n  const location = useLocation();\r\n  const navigate = useNavigate();\r\n\r\n  useEffect(() => {\r\n    console.log('=== URL PARAM CHECKER DEBUG ===');\r\n    console.log('Full URL:', window.location.href);\r\n    console.log('Search:', location.search);\r\n    console.log('Hash:', location.hash);\r\n    console.log('Referrer:', document.referrer);\r\n    console.log('Current pathname:', location.pathname);\r\n    \r\n    const urlParams = new URLSearchParams(location.search);\r\n    const token = urlParams.get('token');\r\n    const type = urlParams.get('type');\r\n    \r\n    console.log('Search params - token:', token, 'type:', type);\r\n    \r\n    // Check URL hash for access_token (format: #access_token=...)\r\n    const hash = location.hash;\r\n    const hasAccessToken = hash && hash.includes('access_token');\r\n    \r\n    console.log('Hash check - hasAccessToken:', hasAccessToken);\r\n    \r\n    // Check if URL comes from Supabase confirmation email (sendibt3.com redirect)\r\n    const referrer = document.referrer;\r\n    const isSupabaseConfirmation = referrer && referrer.includes('sendibt3.com');\r\n    \r\n    console.log('Referrer check - isSupabaseConfirmation:', isSupabaseConfirmation);\r\n    \r\n    // Check for confirmation email indicators in current URL\r\n    const currentUrl = window.location.href;\r\n    const isConfirmationEmail = currentUrl.includes('sendibt3.com') || \r\n                               currentUrl.includes('confirmation') ||\r\n                               currentUrl.includes('verify');\r\n\r\n    console.log('URL pattern check - isConfirmationEmail:', isConfirmationEmail);\r\n    \r\n    // Check if user landed directly on dashboard (common with confirmation emails)\r\n    const isDashboardAccess = location.pathname === '/dashboard';\r\n    \r\n    // Check various suspicious patterns for dashboard access\r\n    const isDashboardWithoutReferrer = isDashboardAccess && !document.referrer;\r\n    const isDashboardFromExternal = isDashboardAccess && \r\n                                   document.referrer && \r\n                                   !document.referrer.includes(window.location.origin);\r\n    \r\n    // Check if this is a fresh session (no previous app navigation)\r\n    const lastAppNavigation = sessionStorage.getItem('last_app_navigation');\r\n    const isFreshSession = !lastAppNavigation;\r\n   \r\n    \r\n    // Strong indicators of confirmation email access\r\n    const isSuspiciousDashboardAccess = isDashboardAccess && (\r\n      isDashboardWithoutReferrer || \r\n      isDashboardFromExternal || \r\n      (isFreshSession && isDashboardAccess)\r\n    );\r\n    \r\n    console.log('  - isSuspiciousDashboardAccess:', isSuspiciousDashboardAccess);\r\n\r\n    // If any confirmation indicators are present, clear session and redirect to login\r\n    if (token || type || hasAccessToken || isSupabaseConfirmation || isConfirmationEmail || isSuspiciousDashboardAccess) {\r\n      console.log('URL PARAM CHECKER - CONFIRMATION/SUSPICIOUS ACCESS DETECTED, CLEARING SESSION');\r\n      \r\n      // Set flag to indicate confirmation email access\r\n      localStorage.setItem('confirmation_email_access', 'true');\r\n      \r\n      // Clear the session before redirecting\r\n      const clearSessionAndRedirect = async () => {\r\n        try {\r\n          // Sign out from Supabase\r\n          await supabase.auth.signOut();\r\n          console.log('Session cleared successfully');\r\n        } catch (error) {\r\n          console.error('Error clearing session:', error);\r\n          // Continue with redirect even if signOut fails\r\n        } finally {\r\n          // Force a page reload to clear any cached state and redirect to login\r\n          window.location.href = '/login';\r\n        }\r\n      };\r\n\r\n      clearSessionAndRedirect();\r\n    } else {\r\n      console.log('No confirmation email indicators detected');\r\n      // Mark this as legitimate app navigation\r\n      if (location.pathname !== '/login') {\r\n        sessionStorage.setItem('last_app_navigation', Date.now().toString());\r\n      }\r\n    }\r\n    \r\n    console.log('=== END URL PARAM CHECKER DEBUG ===');\r\n  }, [location.search, location.hash, location.pathname, navigate]);\r\n\r\n  return children;\r\n};\r\n\r\nexport default URLParamChecker; "], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,QAAQ,MAAM,mBAAmB;AAExC,MAAMC,eAAe,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAMO,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACdS,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9CD,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEC,MAAM,CAACJ,QAAQ,CAACK,IAAI,CAAC;IAC9CH,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEH,QAAQ,CAACM,MAAM,CAAC;IACvCJ,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEH,QAAQ,CAACO,IAAI,CAAC;IACnCL,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEK,QAAQ,CAACC,QAAQ,CAAC;IAC3CP,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEH,QAAQ,CAACU,QAAQ,CAAC;IAEnD,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAACZ,QAAQ,CAACM,MAAM,CAAC;IACtD,MAAMO,KAAK,GAAGF,SAAS,CAACG,GAAG,CAAC,OAAO,CAAC;IACpC,MAAMC,IAAI,GAAGJ,SAAS,CAACG,GAAG,CAAC,MAAM,CAAC;IAElCZ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEU,KAAK,EAAE,OAAO,EAAEE,IAAI,CAAC;;IAE3D;IACA,MAAMR,IAAI,GAAGP,QAAQ,CAACO,IAAI;IAC1B,MAAMS,cAAc,GAAGT,IAAI,IAAIA,IAAI,CAACU,QAAQ,CAAC,cAAc,CAAC;IAE5Df,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEa,cAAc,CAAC;;IAE3D;IACA,MAAMP,QAAQ,GAAGD,QAAQ,CAACC,QAAQ;IAClC,MAAMS,sBAAsB,GAAGT,QAAQ,IAAIA,QAAQ,CAACQ,QAAQ,CAAC,cAAc,CAAC;IAE5Ef,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEe,sBAAsB,CAAC;;IAE/E;IACA,MAAMC,UAAU,GAAGf,MAAM,CAACJ,QAAQ,CAACK,IAAI;IACvC,MAAMe,mBAAmB,GAAGD,UAAU,CAACF,QAAQ,CAAC,cAAc,CAAC,IACpCE,UAAU,CAACF,QAAQ,CAAC,cAAc,CAAC,IACnCE,UAAU,CAACF,QAAQ,CAAC,QAAQ,CAAC;IAExDf,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEiB,mBAAmB,CAAC;;IAE5E;IACA,MAAMC,iBAAiB,GAAGrB,QAAQ,CAACU,QAAQ,KAAK,YAAY;;IAE5D;IACA,MAAMY,0BAA0B,GAAGD,iBAAiB,IAAI,CAACb,QAAQ,CAACC,QAAQ;IAC1E,MAAMc,uBAAuB,GAAGF,iBAAiB,IAClBb,QAAQ,CAACC,QAAQ,IACjB,CAACD,QAAQ,CAACC,QAAQ,CAACQ,QAAQ,CAACb,MAAM,CAACJ,QAAQ,CAACwB,MAAM,CAAC;;IAElF;IACA,MAAMC,iBAAiB,GAAGC,cAAc,CAACC,OAAO,CAAC,qBAAqB,CAAC;IACvE,MAAMC,cAAc,GAAG,CAACH,iBAAiB;;IAGzC;IACA,MAAMI,2BAA2B,GAAGR,iBAAiB,KACnDC,0BAA0B,IAC1BC,uBAAuB,IACtBK,cAAc,IAAIP,iBAAkB,CACtC;IAEDnB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE0B,2BAA2B,CAAC;;IAE5E;IACA,IAAIhB,KAAK,IAAIE,IAAI,IAAIC,cAAc,IAAIE,sBAAsB,IAAIE,mBAAmB,IAAIS,2BAA2B,EAAE;MACnH3B,OAAO,CAACC,GAAG,CAAC,+EAA+E,CAAC;;MAE5F;MACA2B,YAAY,CAACC,OAAO,CAAC,2BAA2B,EAAE,MAAM,CAAC;;MAEzD;MACA,MAAMC,uBAAuB,GAAG,MAAAA,CAAA,KAAY;QAC1C,IAAI;UACF;UACA,MAAMpC,QAAQ,CAACqC,IAAI,CAACC,OAAO,CAAC,CAAC;UAC7BhC,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC7C,CAAC,CAAC,OAAOgC,KAAK,EAAE;UACdjC,OAAO,CAACiC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C;QACF,CAAC,SAAS;UACR;UACA/B,MAAM,CAACJ,QAAQ,CAACK,IAAI,GAAG,QAAQ;QACjC;MACF,CAAC;MAED2B,uBAAuB,CAAC,CAAC;IAC3B,CAAC,MAAM;MACL9B,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD;MACA,IAAIH,QAAQ,CAACU,QAAQ,KAAK,QAAQ,EAAE;QAClCgB,cAAc,CAACK,OAAO,CAAC,qBAAqB,EAAEK,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;MACtE;IACF;IAEApC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;EACpD,CAAC,EAAE,CAACH,QAAQ,CAACM,MAAM,EAAEN,QAAQ,CAACO,IAAI,EAAEP,QAAQ,CAACU,QAAQ,EAAET,QAAQ,CAAC,CAAC;EAEjE,OAAOH,QAAQ;AACjB,CAAC;AAACC,EAAA,CAhGIF,eAAe;EAAA,QACFH,WAAW,EACXC,WAAW;AAAA;AAAA4C,EAAA,GAFxB1C,eAAe;AAkGrB,eAAeA,eAAe;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}