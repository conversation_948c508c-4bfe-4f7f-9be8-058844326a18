{"ast": null, "code": "import apiService from './api';\nimport API_URLS from '../config/apiUrls';\nconst departmentService = {\n  // Get all departments\n  getAllDepartments: async () => {\n    try {\n      // Call the API endpoint to get departments\n      const response = await apiService.get(API_URLS.DEPARTMENTS.LIST);\n      return response;\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error fetching departments:', error);\n      let errorMessage = 'Failed to fetch departments.';\n      if (error !== null && error !== void 0 && (_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.detail) {\n        var _error$response2, _error$response2$data;\n        errorMessage = error === null || error === void 0 ? void 0 : (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail;\n      }\n\n      // Return a mock list if the API fails\n      return {\n        departments: [\"All\"]\n      };\n    }\n  }\n};\nexport default departmentService;", "map": {"version": 3, "names": ["apiService", "API_URLS", "departmentService", "getAllDepartments", "response", "get", "DEPARTMENTS", "LIST", "error", "_error$response", "_error$response$data", "console", "errorMessage", "data", "detail", "_error$response2", "_error$response2$data", "departments"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/services/departmentService.js"], "sourcesContent": ["import apiService from './api';\r\nimport API_URLS from '../config/apiUrls';\r\n\r\nconst departmentService = {\r\n  // Get all departments\r\n  getAllDepartments: async () => {\r\n    try {\r\n      // Call the API endpoint to get departments\r\n      const response = await apiService.get(API_URLS.DEPARTMENTS.LIST);\r\n      return response;\r\n    } catch (error) {\r\n      console.error('Error fetching departments:', error);\r\n      \r\n      let errorMessage = 'Failed to fetch departments.';\r\n      if (error?.response?.data?.detail) {\r\n        errorMessage = error?.response?.data?.detail;\r\n      }\r\n      \r\n      // Return a mock list if the API fails\r\n      return {\r\n        departments: [\r\n        \"All\"\r\n        ]\r\n      };\r\n\r\n    }\r\n  }\r\n};\r\n\r\nexport default departmentService;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,mBAAmB;AAExC,MAAMC,iBAAiB,GAAG;EACxB;EACAC,iBAAiB,EAAE,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF;MACA,MAAMC,QAAQ,GAAG,MAAMJ,UAAU,CAACK,GAAG,CAACJ,QAAQ,CAACK,WAAW,CAACC,IAAI,CAAC;MAChE,OAAOH,QAAQ;IACjB,CAAC,CAAC,OAAOI,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACH,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MAEnD,IAAII,YAAY,GAAG,8BAA8B;MACjD,IAAIJ,KAAK,aAALA,KAAK,gBAAAC,eAAA,GAALD,KAAK,CAAEJ,QAAQ,cAAAK,eAAA,gBAAAC,oBAAA,GAAfD,eAAA,CAAiBI,IAAI,cAAAH,oBAAA,eAArBA,oBAAA,CAAuBI,MAAM,EAAE;QAAA,IAAAC,gBAAA,EAAAC,qBAAA;QACjCJ,YAAY,GAAGJ,KAAK,aAALA,KAAK,wBAAAO,gBAAA,GAALP,KAAK,CAAEJ,QAAQ,cAAAW,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBF,IAAI,cAAAG,qBAAA,uBAArBA,qBAAA,CAAuBF,MAAM;MAC9C;;MAEA;MACA,OAAO;QACLG,WAAW,EAAE,CACb,KAAK;MAEP,CAAC;IAEH;EACF;AACF,CAAC;AAED,eAAef,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}