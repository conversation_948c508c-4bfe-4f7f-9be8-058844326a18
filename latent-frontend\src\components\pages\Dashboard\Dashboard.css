.dashboard {
  min-height: 100vh;
  background-color: var(--bg-white);
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  border-bottom: 1px solid var(--border-slate-200);
}

.dashboard-header h1 {
  font-size: 20px;
  color: var(--text-slate-900);
  margin: 0;
}

.dashboard-nav {
  padding: 0 2rem;
  border-bottom: 1px solid var(--border-slate-200);
}

.dashboard-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
}

.dashboard-nav li {
  padding: 1rem 0;
  color: var(--text-slate-600);
  cursor: pointer;
  font-size: 14px;
}

.nav-separator {
  color: var(--text-slate-400);
  margin: 0 1.5rem;
  font-size: 25px;
  line-height: 1;
}

.dashboard-nav li.active {
  color: var(--text-slate-900);
  border-bottom: 2px solid var(--text-slate-900);
}

.dashboard-content {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  margin-bottom: 2rem;
}

.welcome-section h2 {
  font-size: 24px;
  color: var(--text-slate-900);
  margin-bottom: 0.5rem;
}

.welcome-section p {
  color: var(--text-slate-600);
  font-size: 14px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background-color: var(--stat-blue-bg);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
}

.stat-card h3 {
  font-size: 16px;
  color: var(--text-slate-900);
  margin: 0 0 0.5rem 0;
}

.stat-card p {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
}

.recent-updates {
  background-color: var(--bg-white);
}

.recent-updates h3 {
  font-size: 18px;
  color: var(--text-slate-900);
  margin-bottom: 1rem;
}

.updates-list {
  display: flex;
  flex-direction: column;
}

.update-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid var(--border-slate-200);
}

.update-item:last-child {
  border-bottom: none;
}

.update-text {
  color: var(--text-slate-900);
  font-size: 14px;
}

.update-time {
  color: var(--text-slate-600);
  font-size: 14px;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .update-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

.user-profile {
  display: flex;
  align-items: center;
}

.profile-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  cursor: pointer;
}

/* Activity loading and empty states */
.activity-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 2rem 0;
  color: var(--text-slate-600);
  font-size: 14px;
}

.no-activities {
  padding: 2rem 0;
  text-align: center;
}

.no-activities .update-text {
  color: var(--text-slate-500);
  font-style: italic;
}