from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Optional, Dict
import logging
import os
from datetime import datetime
from dotenv import load_dotenv
load_dotenv()

from scripts.chat_log_handler import setup_logging

from app.services.chatbot_services.azure_search_service import AzureSearchService
from app.services.chatbot_services.openai_service import OpenAIService
from app.models.chatbot_models import QueryRequest, QueryResponse, DocumentChunk, ChatTurn

 
logger = setup_logging(name="app")

class QueryController:
    def __init__(self):
        self.search_service = AzureSearchService()
        self.openai_service = OpenAIService()
    
    async def process_query(self, request: QueryRequest) -> QueryResponse:
        try:
             
            logger.info(f"Processing query: {request.query}")
            
            # Step 1: Generate embeddings
            logger.info("Step 1: Generating query embeddings...")
            query_vector = await self.openai_service.get_embeddings(request.query)
            logger.info(f"Query embeddings generated successfully: {query_vector[:5]}... (total {len(query_vector)})")
            
            user_id = request.user_id if request.user_id is not None else ""
            titles = request.filter_titles if request.filter_titles is not None else []
            
            # Step 2: Search documents
            logger.info("Step 2: Searching for relevant documents...")
            logger.info(f"Search parameters: user_id={user_id}, top_k=3, titles_filter_count={len(titles)}")
            documents = await self.search_service.search_documents(
                query_vector=query_vector,
                user_id=user_id,
                top_k=4,
                titles=titles,
                blob_to_title_mapping=request.blob_to_title_mapping
            )
            logger.info(f"Document search completed. Found {len(documents) if documents else 0} documents")
            
            if not documents:
                logger.warning("No relevant documents found. Returning fallback response.")
                return QueryResponse(
                    answer="I couldn't find any relevant documents to answer your question.",
                    sources=[],
                    query=request.query,
                    total_sources=0
                )
            
            # Step 3: Prepare context from documents
            logger.info("Step 3: Preparing context from retrieved documents...")
            
            context = "\n\n".join([
                f"Document: {doc.doc_name}\nContent: {doc.content}" for doc in documents
            ])
            logger.info(f"Combined context preview: {context[:300]}...")
            
            # Step 4: Generate response using OpenAI
            logger.info("Step 4: Generating AI response...")
            chat_history_log = ""
            if request.chat_history and len(request.chat_history) > 0:
                logger.info(f"Including chat history with {len(request.chat_history)} previous turns")
                for i, turn in enumerate(request.chat_history):
                    chat_history_log += f"Turn {i+1}: User: {turn.user_query[:50]}...\n"
                    chat_history_log += f"Turn {i+1}: Bot: {turn.bot_answer[:50]}...\n"
                logger.info(f"Chat history summary:\n{chat_history_log}")
                
            answer = await self.openai_service.generate_response(
                context, 
                request.query, 
                chat_history=request.chat_history
            )
            logger.info(f"LLM response generated successfully: {answer[:200]}...")
            
            # Step 5: Prepare and return response
            logger.info("Step 5: Preparing final response...")
            response = QueryResponse(
                answer=answer,
                sources=documents,
                query=request.query,
                total_sources=len(documents)
            )
            logger.info(f"Response prepared successfully with {len(documents)} sources")
            return response
            
        except Exception as e:
            logger.error(f"========== ERROR IN QUERY PROCESSING ==========")
            raise HTTPException(status_code=500, detail=f"Failed to process query: {str(e)}")

query_controller = QueryController()

# --- FastAPI Router ---
router = APIRouter()

@router.post("/query", response_model=QueryResponse)
async def process_query(request: QueryRequest):
    """Process user query and return AI response with sources"""
    request_time = datetime.now().isoformat()
    user_id = request.user_id if hasattr(request, "user_id") and request.user_id else "unknown"
    logger.info(f"========== API REQUEST [{request_time}] [User: {user_id}] ==========")
    logger.info(f"========== API REQUEST [{request_time}] ==========")
    logger.info(f"Request query: {request.query}")
    logger.info(f"Filters applied: {len(request.filter_titles) if request.filter_titles else 0} title filters")
    
    try:
        logger.info(f"Delegating to QueryController.process_query()")
        response = await query_controller.process_query(request)
        logger.info(f"Response status: Success")
        logger.info(f"Response size: {len(response.answer)} characters")
        return response
    except Exception as e:
        logger.error(f"Error details: {str(e)}", exc_info=True)
        logger.error(f"Stack trace:", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to process query: {str(e)}")