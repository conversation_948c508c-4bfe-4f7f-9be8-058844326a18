{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\zipp\\\\latent-frontend\\\\src\\\\components\\\\pages\\\\Login\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useLocation } from 'react-router-dom';\nimport supabase from '../../../supabase';\nimport './Login.css';\nimport API_URLS from '../../../config/apiUrls';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const location = useLocation();\n  const [activeTab, setActiveTab] = useState('login');\n  const [formData, setFormData] = useState({\n    loginEmail: '',\n    loginPassword: '',\n    signupEmail: '',\n    signupPassword: '',\n    signupPasswordConfirm: '',\n    firstName: '',\n    lastName: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [message, setMessage] = useState('');\n  const [isConfirmationAccess, setIsConfirmationAccess] = useState(false);\n  // const navigate = useNavigate();\n\n  useEffect(() => {\n    // Add comprehensive debugging\n\n    // Check if accessed via confirmation email\n    const urlParams = new URLSearchParams(location.search);\n    const token = urlParams.get('token');\n    const type = urlParams.get('type');\n    const confirmationType = urlParams.get('confirmation_url');\n    console.log('URL Params - token:', token, 'type:', type, 'confirmationType:', confirmationType);\n\n    // Check URL hash for access_token (format: #access_token=...)\n    const hash = location.hash;\n    const hasAccessToken = hash && hash.includes('access_token');\n    console.log('Hash check - hasAccessToken:', hasAccessToken);\n\n    // Check if URL comes from Supabase confirmation email (sendibt3.com redirect)\n    const referrer = document.referrer;\n    const isSupabaseConfirmation = referrer && referrer.includes('sendibt3.com');\n    console.log('Referrer check - isSupabaseConfirmation:', isSupabaseConfirmation);\n\n    // Check for confirmation email indicators in current URL\n    const currentUrl = window.location.href;\n    const isConfirmationEmail = currentUrl.includes('sendibt3.com') || currentUrl.includes('confirmation') || currentUrl.includes('verify');\n\n    // Check all hash parameters\n    if (hash) {\n      console.log('Hash parameters detected:', hash);\n      const hashParams = new URLSearchParams(hash.substring(1)); // Remove # from hash\n      console.log('Parsed hash params:');\n      for (let [key, value] of hashParams) {\n        console.log(`  ${key}: ${value}`);\n      }\n    }\n\n    // If hasAccessToken or confirmation email detected, logout and redirect to clean login page\n    if (hasAccessToken || isSupabaseConfirmation || isConfirmationEmail) {\n      // Logout user and redirect to clean login URL\n      const logoutAndRedirect = async () => {\n        try {\n          // Sign out from Supabase if user is logged in\n          await supabase.auth.signOut();\n          console.log('User logged out successfully');\n        } catch (error) {\n          console.error('Error logging out user:', error);\n          // Continue with redirect even if signOut fails\n        } finally {\n          // Clear the hash and redirect to clean login URL\n          window.location.href = '/login';\n        }\n      };\n      logoutAndRedirect();\n      return;\n    }\n\n    // Check localStorage for confirmation access flag\n    const confirmationAccessFlag = localStorage.getItem('confirmation_email_access');\n    if (token || type || confirmationType || confirmationAccessFlag) {\n      // setIsConfirmationAccess(true);\n      // setError('Login is temporarily disabled. This appears to be a confirmation email link. Please access the application directly.');\n\n      // Clear the flag if it exists\n      if (confirmationAccessFlag) {\n        localStorage.removeItem('confirmation_email_access');\n      }\n    }\n  }, [location]);\n  const handleInputChange = e => {\n    const {\n      id,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [id]: value\n    }));\n  };\n  const handleLogin = async e => {\n    e.preventDefault();\n\n    // Prevent login if accessed via confirmation email\n    if (isConfirmationAccess) {\n      setError('Login is disabled when accessing via confirmation email. Please visit the application directly.');\n      return;\n    }\n    setLoading(true);\n    setError(null);\n    setMessage('');\n\n    // Basic validation\n    // Check user status before proceeding with login\n    try {\n      const response = await fetch(API_URLS.USER.CHECK_USER_EXISTS(formData.loginEmail), {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.status === 401) {\n        const errorData = await response.json();\n        setError(errorData.message || 'User account is not active');\n        setLoading(false);\n        return;\n      }\n      if (response.status !== 200) {\n        setError('Failed to verify user status. Please try again.');\n        setLoading(false);\n        return;\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      let errorMessage = 'Failed to verify user status. Please try again.';\n      if (error !== null && error !== void 0 && (_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.detail) {\n        var _error$response2, _error$response2$data;\n        errorMessage = error === null || error === void 0 ? void 0 : (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail;\n      }\n      setError(errorMessage);\n      setLoading(false);\n      return;\n    }\n    if (!formData.loginEmail || !formData.loginPassword) {\n      setError('Please fill in all fields');\n      setLoading(false);\n      return;\n    }\n    try {\n      const {\n        data,\n        error\n      } = await supabase.auth.signInWithPassword({\n        email: formData.loginEmail,\n        password: formData.loginPassword\n      });\n      if (error) {\n        if (error.message.includes('Invalid login credentials')) {\n          setError('Invalid email or password');\n        } else {\n          setError(error.message);\n        }\n        return;\n      }\n\n      // If login successful, navigate to dashboard\n      if (data !== null && data !== void 0 && data.user) {\n        window.location.href = '/dashboard'; // This will force a full page reload\n        // OR use this if you want to keep React state:\n        // navigate('/dashboard', { replace: true });\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      let errorMessage = 'An unexpected error occurred. Please try again.';\n      if (error !== null && error !== void 0 && (_error$response3 = error.response) !== null && _error$response3 !== void 0 && (_error$response3$data = _error$response3.data) !== null && _error$response3$data !== void 0 && _error$response3$data.detail) {\n        var _error$response4, _error$response4$data;\n        errorMessage = error === null || error === void 0 ? void 0 : (_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail;\n      }\n      setError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSignup = async e => {\n    e.preventDefault();\n\n    // Prevent signup if accessed via confirmation email\n    if (isConfirmationAccess) {\n      setError('Signup is disabled when accessing via confirmation email. Please visit the application directly.');\n      return;\n    }\n    setLoading(true);\n    setError(null);\n    setMessage('');\n\n    // Basic validation\n    if (!formData.signupEmail || !formData.signupPassword || !formData.signupPasswordConfirm || !formData.firstName) {\n      setError('Please fill in all required fields');\n      setLoading(false);\n      return;\n    }\n\n    // Password confirmation check\n    if (formData.signupPassword !== formData.signupPasswordConfirm) {\n      setError('Passwords do not match');\n      setLoading(false);\n      return;\n    }\n    try {\n      // Normalize the email\n      const normalizedEmail = formData.signupEmail.trim().toLowerCase();\n      console.log(\"Attempting signup for:\", normalizedEmail);\n\n      // Attempt signup\n      const {\n        data,\n        error: signupError\n      } = await supabase.auth.signUp({\n        email: normalizedEmail,\n        password: formData.signupPassword,\n        options: {\n          data: {\n            first_name: formData.firstName.trim(),\n            last_name: formData.lastName.trim() === '' ? null : formData.lastName.trim()\n          }\n        }\n      });\n      console.log(\"Signup response data:\", data);\n      console.log(\"Signup error:\", signupError);\n\n      // Check for errors\n      if (signupError) {\n        console.error(\"Signup error details:\", signupError);\n        if (signupError.message.includes('Database error saving new user')) {\n          setError(`you are not allowed to login contact your administrator`);\n        } else if (signupError.message.includes('User already registered')) {\n          setError('User already signed up, please login');\n        } else {\n          setError(signupError.message || 'Error during signup');\n        }\n        return;\n      }\n\n      // Check if user was created\n      if (data !== null && data !== void 0 && data.user) {\n        // console.log(\"User created successfully:\", data.user);\n\n        // Check if email confirmation is required\n        if (data.user.identities && data.user.identities.length === 0) {\n          setError('This email is already registered. Please login instead.');\n          return;\n        }\n        setMessage('Check your email for the confirmation link.');\n        // Clear the form\n        setFormData(prev => ({\n          ...prev,\n          signupEmail: '',\n          signupPassword: '',\n          signupPasswordConfirm: '',\n          firstName: '',\n          lastName: ''\n        }));\n      } else {\n        // No user data but also no error - unusual case\n        console.warn(\"No user data returned but no error either\");\n        setError('Something went wrong with registration. Please try again.');\n      }\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      console.error(\"Unexpected error during signup:\", error);\n      let errorMessage = 'An unexpected error occurred. Please try again.';\n      if (error !== null && error !== void 0 && (_error$response5 = error.response) !== null && _error$response5 !== void 0 && (_error$response5$data = _error$response5.data) !== null && _error$response5$data !== void 0 && _error$response5$data.detail) {\n        var _error$response6, _error$response6$data;\n        errorMessage = error === null || error === void 0 ? void 0 : (_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.detail;\n      }\n      setError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"logo-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '10px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/zipplogo.png\",\n          alt: \"Zipp Logo\",\n          className: \"logo-image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#000'\n          },\n          children: \"|\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"latentlogo.png\",\n          className: \"logo-image\",\n          style: {\n            width: '110px',\n            height: '30px',\n            marginTop: '4px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      id: \"auth-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tab-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab-button ${activeTab === 'login' ? 'active' : ''}`,\n          onClick: () => setActiveTab('login'),\n          children: \"Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab-button ${activeTab === 'signup' ? 'active' : ''}`,\n          onClick: () => setActiveTab('signup'),\n          children: \"Sign Up\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        id: \"login\",\n        className: `tab-content ${activeTab === 'login' ? 'active' : ''}`,\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleLogin,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"loginEmail\",\n              children: [\"Email \", /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: 'var(--severity-high-text)'\n                },\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 49\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              id: \"loginEmail\",\n              value: formData.loginEmail,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"loginPassword\",\n              children: [\"Password \", /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: 'var(--severity-high-text)'\n                },\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 55\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              id: \"loginPassword\",\n              value: formData.loginPassword,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn\",\n            disabled: loading,\n            children: loading ? 'Logging in...' : 'Login'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        id: \"signup\",\n        className: `tab-content ${activeTab === 'signup' ? 'active' : ''}`,\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Sign Up\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSignup,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"firstName\",\n              children: [\"First Name \", /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: 'var(--severity-high-text)'\n                },\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 53\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"firstName\",\n              value: formData.firstName,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"lastName\",\n              children: \"Last Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"lastName\",\n              value: formData.lastName,\n              onChange: handleInputChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"signupEmail\",\n              children: [\"Email \", /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: 'var(--severity-high-text)'\n                },\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 50\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              id: \"signupEmail\",\n              value: formData.signupEmail,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"signupPassword\",\n              children: [\"Password \", /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: 'var(--severity-high-text)'\n                },\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 56\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              id: \"signupPassword\",\n              value: formData.signupPassword,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"signupPasswordConfirm\",\n              children: [\"Confirm Password \", /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: 'var(--severity-high-text)'\n                },\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 71\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              id: \"signupPasswordConfirm\",\n              value: formData.signupPasswordConfirm,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn\",\n            disabled: loading,\n            children: loading ? 'Signing up...' : 'Sign Up'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"info-text\",\n          children: \"Only allowed emails can sign up. Please check if your email is on the allowlist.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"message error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 430,\n      columnNumber: 17\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"message success\",\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 431,\n      columnNumber: 19\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 306,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"fMeDziVxGj07HYO08kk2m0jbXI8=\", false, function () {\n  return [useLocation];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useLocation", "supabase", "API_URLS", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "location", "activeTab", "setActiveTab", "formData", "setFormData", "loginEmail", "loginPassword", "signupEmail", "signupPassword", "signupPasswordConfirm", "firstName", "lastName", "loading", "setLoading", "error", "setError", "message", "setMessage", "isConfirmationAccess", "setIsConfirmationAccess", "urlParams", "URLSearchParams", "search", "token", "get", "type", "confirmationType", "console", "log", "hash", "hasAccessToken", "includes", "referrer", "document", "isSupabaseConfirmation", "currentUrl", "window", "href", "isConfirmationEmail", "hashParams", "substring", "key", "value", "logoutAndRedirect", "auth", "signOut", "confirmationAccessFlag", "localStorage", "getItem", "removeItem", "handleInputChange", "e", "id", "target", "prev", "handleLogin", "preventDefault", "response", "fetch", "USER", "CHECK_USER_EXISTS", "method", "headers", "status", "errorData", "json", "_error$response", "_error$response$data", "errorMessage", "data", "detail", "_error$response2", "_error$response2$data", "signInWithPassword", "email", "password", "user", "_error$response3", "_error$response3$data", "_error$response4", "_error$response4$data", "handleSignup", "normalizedEmail", "trim", "toLowerCase", "signupError", "signUp", "options", "first_name", "last_name", "identities", "length", "warn", "_error$response5", "_error$response5$data", "_error$response6", "_error$response6$data", "className", "children", "style", "display", "alignItems", "gap", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "width", "height", "marginTop", "onClick", "onSubmit", "htmlFor", "onChange", "required", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/components/pages/Login/Login.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useLocation } from 'react-router-dom';\r\nimport supabase from '../../../supabase';\r\nimport './Login.css';\r\nimport API_URLS from '../../../config/apiUrls';\r\n\r\nconst Login = () => {\r\n  const location = useLocation();\r\n  const [activeTab, setActiveTab] = useState('login');\r\n  const [formData, setFormData] = useState({\r\n    loginEmail: '',\r\n    loginPassword: '',\r\n    signupEmail: '',\r\n    signupPassword: '',\r\n    signupPasswordConfirm: '',\r\n    firstName: '',\r\n    lastName: ''\r\n  });\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  const [message, setMessage] = useState('');\r\n  const [isConfirmationAccess, setIsConfirmationAccess] = useState(false);\r\n  // const navigate = useNavigate();\r\n\r\n  useEffect(() => {\r\n    // Add comprehensive debugging\r\n    \r\n    \r\n    // Check if accessed via confirmation email\r\n    const urlParams = new URLSearchParams(location.search);\r\n    const token = urlParams.get('token');\r\n    const type = urlParams.get('type');\r\n    const confirmationType = urlParams.get('confirmation_url');\r\n    \r\n    console.log('URL Params - token:', token, 'type:', type, 'confirmationType:', confirmationType);\r\n    \r\n    // Check URL hash for access_token (format: #access_token=...)\r\n    const hash = location.hash;\r\n    const hasAccessToken = hash && hash.includes('access_token');\r\n    \r\n    console.log('Hash check - hasAccessToken:', hasAccessToken);\r\n    \r\n    // Check if URL comes from Supabase confirmation email (sendibt3.com redirect)\r\n    const referrer = document.referrer;\r\n    const isSupabaseConfirmation = referrer && referrer.includes('sendibt3.com');\r\n    \r\n    console.log('Referrer check - isSupabaseConfirmation:', isSupabaseConfirmation);\r\n    \r\n    // Check for confirmation email indicators in current URL\r\n    const currentUrl = window.location.href;\r\n    const isConfirmationEmail = currentUrl.includes('sendibt3.com') || \r\n                               currentUrl.includes('confirmation') ||\r\n                               currentUrl.includes('verify');\r\n    \r\n\r\n    \r\n    // Check all hash parameters\r\n    if (hash) {\r\n      console.log('Hash parameters detected:', hash);\r\n      const hashParams = new URLSearchParams(hash.substring(1)); // Remove # from hash\r\n      console.log('Parsed hash params:');\r\n      for (let [key, value] of hashParams) {\r\n        console.log(`  ${key}: ${value}`);\r\n      }\r\n    }\r\n    \r\n    // If hasAccessToken or confirmation email detected, logout and redirect to clean login page\r\n    if (hasAccessToken || isSupabaseConfirmation || isConfirmationEmail) {\r\n\r\n      \r\n      // Logout user and redirect to clean login URL\r\n      const logoutAndRedirect = async () => {\r\n        try {\r\n          // Sign out from Supabase if user is logged in\r\n          await supabase.auth.signOut();\r\n          console.log('User logged out successfully');\r\n        } catch (error) {\r\n          console.error('Error logging out user:', error);\r\n          // Continue with redirect even if signOut fails\r\n        } finally {\r\n          // Clear the hash and redirect to clean login URL\r\n          window.location.href = '/login';\r\n        }\r\n      };\r\n      \r\n      logoutAndRedirect();\r\n      return;\r\n    }\r\n    \r\n    // Check localStorage for confirmation access flag\r\n    const confirmationAccessFlag = localStorage.getItem('confirmation_email_access');\r\n\r\n    \r\n    if (token || type || confirmationType || confirmationAccessFlag) {\r\n\r\n      // setIsConfirmationAccess(true);\r\n      // setError('Login is temporarily disabled. This appears to be a confirmation email link. Please access the application directly.');\r\n      \r\n      // Clear the flag if it exists\r\n      if (confirmationAccessFlag) {\r\n        localStorage.removeItem('confirmation_email_access');\r\n      }\r\n    }\r\n    \r\n\r\n  }, [location]);\r\n\r\n  const handleInputChange = (e) => {\r\n    const { id, value } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [id]: value\r\n    }));\r\n  };\r\n\r\n  const handleLogin = async (e) => {\r\n    e.preventDefault();\r\n    \r\n    // Prevent login if accessed via confirmation email\r\n    if (isConfirmationAccess) {\r\n      setError('Login is disabled when accessing via confirmation email. Please visit the application directly.');\r\n      return;\r\n    }\r\n    \r\n    setLoading(true);\r\n    setError(null);\r\n    setMessage('');\r\n\r\n    // Basic validation\r\n    // Check user status before proceeding with login\r\n    try {\r\n      const response = await fetch(API_URLS.USER.CHECK_USER_EXISTS(formData.loginEmail), {\r\n        method: 'GET',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n      \r\n      if (response.status === 401) {\r\n        const errorData = await response.json();\r\n        setError(errorData.message || 'User account is not active');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      \r\n      if (response.status !== 200) {\r\n        setError('Failed to verify user status. Please try again.');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n    } catch (error) {\r\n      let errorMessage = 'Failed to verify user status. Please try again.';\r\n      if (error?.response?.data?.detail) {\r\n        errorMessage = error?.response?.data?.detail;\r\n      }\r\n      \r\n      setError(errorMessage);\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    \r\n    if (!formData.loginEmail || !formData.loginPassword) {\r\n      setError('Please fill in all fields');\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const { data, error } = await supabase.auth.signInWithPassword({\r\n        email: formData.loginEmail,\r\n        password: formData.loginPassword,\r\n      });\r\n\r\n      if (error) {\r\n        if (error.message.includes('Invalid login credentials')) {\r\n          setError('Invalid email or password');\r\n        } else {\r\n          setError(error.message);\r\n        }\r\n        return;\r\n      }\r\n\r\n      // If login successful, navigate to dashboard\r\n      if (data?.user) {\r\n        window.location.href = '/dashboard'; // This will force a full page reload\r\n        // OR use this if you want to keep React state:\r\n        // navigate('/dashboard', { replace: true });\r\n\r\n      }\r\n    } catch (error) {\r\n      let errorMessage = 'An unexpected error occurred. Please try again.';\r\n      if (error?.response?.data?.detail) {\r\n        errorMessage = error?.response?.data?.detail;\r\n      }\r\n      \r\n      setError(errorMessage);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleSignup = async (e) => {\r\n    e.preventDefault();\r\n    \r\n    // Prevent signup if accessed via confirmation email\r\n    if (isConfirmationAccess) {\r\n      setError('Signup is disabled when accessing via confirmation email. Please visit the application directly.');\r\n      return;\r\n    }\r\n    \r\n    setLoading(true);\r\n    setError(null);\r\n    setMessage('');\r\n\r\n    // Basic validation\r\n    if (!formData.signupEmail || !formData.signupPassword || !formData.signupPasswordConfirm || \r\n        !formData.firstName ) {\r\n      setError('Please fill in all required fields');\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    // Password confirmation check\r\n    if (formData.signupPassword !== formData.signupPasswordConfirm) {\r\n      setError('Passwords do not match');\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Normalize the email\r\n      const normalizedEmail = formData.signupEmail.trim().toLowerCase();\r\n\r\n      console.log(\"Attempting signup for:\", normalizedEmail);\r\n\r\n      // Attempt signup\r\n      const { data, error: signupError } = await supabase.auth.signUp({\r\n        email: normalizedEmail,\r\n        password: formData.signupPassword,\r\n        options: {\r\n          data: {\r\n            first_name: formData.firstName.trim(),\r\n            last_name: formData.lastName.trim() === '' ? null : formData.lastName.trim()\r\n          }\r\n        }\r\n      });\r\n\r\n      console.log(\"Signup response data:\", data);\r\n      console.log(\"Signup error:\", signupError);\r\n\r\n      // Check for errors\r\n      if (signupError) {\r\n        console.error(\"Signup error details:\", signupError);\r\n        \r\n        if (signupError.message.includes('Database error saving new user')) {\r\n          setError(`you are not allowed to login contact your administrator`);\r\n        } else if (signupError.message.includes('User already registered')) {\r\n          setError('User already signed up, please login');\r\n        } else {\r\n          setError(signupError.message || 'Error during signup');\r\n        }\r\n        return;\r\n      }\r\n\r\n      // Check if user was created\r\n      if (data?.user) {\r\n        // console.log(\"User created successfully:\", data.user);\r\n        \r\n        // Check if email confirmation is required\r\n        if (data.user.identities && data.user.identities.length === 0) {\r\n          setError('This email is already registered. Please login instead.');\r\n          return;\r\n        }\r\n        \r\n        setMessage('Check your email for the confirmation link.');\r\n        // Clear the form\r\n        setFormData(prev => ({\r\n          ...prev,\r\n          signupEmail: '',\r\n          signupPassword: '',\r\n          signupPasswordConfirm: '',\r\n          firstName: '',\r\n          lastName: ''\r\n        }));\r\n      } else {\r\n        // No user data but also no error - unusual case\r\n        console.warn(\"No user data returned but no error either\");\r\n        setError('Something went wrong with registration. Please try again.');\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Unexpected error during signup:\", error);\r\n      \r\n      let errorMessage = 'An unexpected error occurred. Please try again.';\r\n      if (error?.response?.data?.detail) {\r\n        errorMessage = error?.response?.data?.detail;\r\n      }\r\n      \r\n      setError(errorMessage);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"container\">\r\n      <div className=\"logo-container\">\r\n\r\n          <div style={{display: 'flex', alignItems: 'center', gap: '10px'}}>\r\n\r\n            <img \r\n              src=\"/zipplogo.png\" \r\n              alt=\"Zipp Logo\" \r\n              className=\"logo-image\"\r\n              />  \r\n            <span style={{color:'#000'}}>|</span>\r\n            <img src='latentlogo.png'  className=\"logo-image\" style={{width: '110px', height: '30px', marginTop: '4px'}}/>\r\n              </div>\r\n\r\n      </div>\r\n      \r\n      <div id=\"auth-container\">\r\n        <div className=\"tab-container\">\r\n          <button \r\n            className={`tab-button ${activeTab === 'login' ? 'active' : ''}`}\r\n            onClick={() => setActiveTab('login')}\r\n          >\r\n            Login\r\n          </button>\r\n          <button \r\n            className={`tab-button ${activeTab === 'signup' ? 'active' : ''}`}\r\n            onClick={() => setActiveTab('signup')}\r\n          >\r\n            Sign Up\r\n          </button>\r\n        </div>\r\n        \r\n        <div id=\"login\" className={`tab-content ${activeTab === 'login' ? 'active' : ''}`}>\r\n          <h2>Login</h2>\r\n          <form onSubmit={handleLogin}>\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"loginEmail\">Email <span style={{color: 'var(--severity-high-text)'}}>*</span></label>\r\n              <input\r\n                type=\"email\"\r\n                id=\"loginEmail\"\r\n                value={formData.loginEmail}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n            </div>\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"loginPassword\">Password <span style={{color: 'var(--severity-high-text)'}}>*</span></label>\r\n              <input\r\n                type=\"password\"\r\n                id=\"loginPassword\"\r\n                value={formData.loginPassword}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n            </div>\r\n            <button type=\"submit\" className=\"btn\" disabled={loading}>\r\n              {loading ? 'Logging in...' : 'Login'}\r\n            </button>\r\n          </form>\r\n        </div>\r\n\r\n        <div id=\"signup\" className={`tab-content ${activeTab === 'signup' ? 'active' : ''}`}>\r\n          <h2>Sign Up</h2>\r\n          <form onSubmit={handleSignup}>\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"firstName\">First Name <span style={{color: 'var(--severity-high-text)'}}>*</span></label>\r\n              <input\r\n                type=\"text\"\r\n                id=\"firstName\"\r\n                value={formData.firstName}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n            </div>\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"lastName\">Last Name</label>\r\n              <input\r\n                type=\"text\"\r\n                id=\"lastName\"\r\n                value={formData.lastName}\r\n                onChange={handleInputChange}\r\n\r\n              />\r\n          </div>\r\n          <div className=\"form-group\">\r\n              <label htmlFor=\"signupEmail\">Email <span style={{color: 'var(--severity-high-text)'}}>*</span></label>\r\n            <input\r\n              type=\"email\"\r\n                id=\"signupEmail\"\r\n                value={formData.signupEmail}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n            </div>\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"signupPassword\">Password <span style={{color: 'var(--severity-high-text)'}}>*</span></label>\r\n              <input\r\n                type=\"password\"\r\n                id=\"signupPassword\"\r\n                value={formData.signupPassword}\r\n                onChange={handleInputChange}\r\n                required\r\n            />\r\n          </div>\r\n          <div className=\"form-group\">\r\n              <label htmlFor=\"signupPasswordConfirm\">Confirm Password <span style={{color: 'var(--severity-high-text)'}}>*</span></label>\r\n            <input\r\n              type=\"password\"\r\n                id=\"signupPasswordConfirm\"\r\n                value={formData.signupPasswordConfirm}\r\n                onChange={handleInputChange}\r\n                required\r\n            />\r\n          </div>\r\n            <button type=\"submit\" className=\"btn\" disabled={loading}>\r\n              {loading ? 'Signing up...' : 'Sign Up'}\r\n          </button>\r\n          </form>\r\n          <p className=\"info-text\">\r\n            Only allowed emails can sign up. Please check if your email is on the allowlist.\r\n          </p>\r\n          </div>\r\n      </div>\r\n      \r\n      {error && <div className=\"message error\">{error}</div>}\r\n      {message && <div className=\"message success\">{message}</div>}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Login; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,OAAO,aAAa;AACpB,OAAOC,QAAQ,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACQ,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAC,OAAO,CAAC;EACnD,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC;IACvCc,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,qBAAqB,EAAE,EAAE;IACzBC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC2B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACvE;;EAEAC,SAAS,CAAC,MAAM;IACd;;IAGA;IACA,MAAM4B,SAAS,GAAG,IAAIC,eAAe,CAACrB,QAAQ,CAACsB,MAAM,CAAC;IACtD,MAAMC,KAAK,GAAGH,SAAS,CAACI,GAAG,CAAC,OAAO,CAAC;IACpC,MAAMC,IAAI,GAAGL,SAAS,CAACI,GAAG,CAAC,MAAM,CAAC;IAClC,MAAME,gBAAgB,GAAGN,SAAS,CAACI,GAAG,CAAC,kBAAkB,CAAC;IAE1DG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEL,KAAK,EAAE,OAAO,EAAEE,IAAI,EAAE,mBAAmB,EAAEC,gBAAgB,CAAC;;IAE/F;IACA,MAAMG,IAAI,GAAG7B,QAAQ,CAAC6B,IAAI;IAC1B,MAAMC,cAAc,GAAGD,IAAI,IAAIA,IAAI,CAACE,QAAQ,CAAC,cAAc,CAAC;IAE5DJ,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEE,cAAc,CAAC;;IAE3D;IACA,MAAME,QAAQ,GAAGC,QAAQ,CAACD,QAAQ;IAClC,MAAME,sBAAsB,GAAGF,QAAQ,IAAIA,QAAQ,CAACD,QAAQ,CAAC,cAAc,CAAC;IAE5EJ,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEM,sBAAsB,CAAC;;IAE/E;IACA,MAAMC,UAAU,GAAGC,MAAM,CAACpC,QAAQ,CAACqC,IAAI;IACvC,MAAMC,mBAAmB,GAAGH,UAAU,CAACJ,QAAQ,CAAC,cAAc,CAAC,IACpCI,UAAU,CAACJ,QAAQ,CAAC,cAAc,CAAC,IACnCI,UAAU,CAACJ,QAAQ,CAAC,QAAQ,CAAC;;IAIxD;IACA,IAAIF,IAAI,EAAE;MACRF,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEC,IAAI,CAAC;MAC9C,MAAMU,UAAU,GAAG,IAAIlB,eAAe,CAACQ,IAAI,CAACW,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3Db,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MAClC,KAAK,IAAI,CAACa,GAAG,EAAEC,KAAK,CAAC,IAAIH,UAAU,EAAE;QACnCZ,OAAO,CAACC,GAAG,CAAC,KAAKa,GAAG,KAAKC,KAAK,EAAE,CAAC;MACnC;IACF;;IAEA;IACA,IAAIZ,cAAc,IAAII,sBAAsB,IAAII,mBAAmB,EAAE;MAGnE;MACA,MAAMK,iBAAiB,GAAG,MAAAA,CAAA,KAAY;QACpC,IAAI;UACF;UACA,MAAMjD,QAAQ,CAACkD,IAAI,CAACC,OAAO,CAAC,CAAC;UAC7BlB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC7C,CAAC,CAAC,OAAOd,KAAK,EAAE;UACda,OAAO,CAACb,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C;QACF,CAAC,SAAS;UACR;UACAsB,MAAM,CAACpC,QAAQ,CAACqC,IAAI,GAAG,QAAQ;QACjC;MACF,CAAC;MAEDM,iBAAiB,CAAC,CAAC;MACnB;IACF;;IAEA;IACA,MAAMG,sBAAsB,GAAGC,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC;IAGhF,IAAIzB,KAAK,IAAIE,IAAI,IAAIC,gBAAgB,IAAIoB,sBAAsB,EAAE;MAE/D;MACA;;MAEA;MACA,IAAIA,sBAAsB,EAAE;QAC1BC,YAAY,CAACE,UAAU,CAAC,2BAA2B,CAAC;MACtD;IACF;EAGF,CAAC,EAAE,CAACjD,QAAQ,CAAC,CAAC;EAEd,MAAMkD,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,EAAE;MAAEV;IAAM,CAAC,GAAGS,CAAC,CAACE,MAAM;IAC9BjD,WAAW,CAACkD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACF,EAAE,GAAGV;IACR,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMa,WAAW,GAAG,MAAOJ,CAAC,IAAK;IAC/BA,CAAC,CAACK,cAAc,CAAC,CAAC;;IAElB;IACA,IAAItC,oBAAoB,EAAE;MACxBH,QAAQ,CAAC,iGAAiG,CAAC;MAC3G;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,EAAE,CAAC;;IAEd;IACA;IACA,IAAI;MACF,MAAMwC,QAAQ,GAAG,MAAMC,KAAK,CAAC/D,QAAQ,CAACgE,IAAI,CAACC,iBAAiB,CAACzD,QAAQ,CAACE,UAAU,CAAC,EAAE;QACjFwD,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAIL,QAAQ,CAACM,MAAM,KAAK,GAAG,EAAE;QAC3B,MAAMC,SAAS,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;QACvClD,QAAQ,CAACiD,SAAS,CAAChD,OAAO,IAAI,4BAA4B,CAAC;QAC3DH,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,IAAI4C,QAAQ,CAACM,MAAM,KAAK,GAAG,EAAE;QAC3BhD,QAAQ,CAAC,iDAAiD,CAAC;QAC3DF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAoD,eAAA,EAAAC,oBAAA;MACd,IAAIC,YAAY,GAAG,iDAAiD;MACpE,IAAItD,KAAK,aAALA,KAAK,gBAAAoD,eAAA,GAALpD,KAAK,CAAE2C,QAAQ,cAAAS,eAAA,gBAAAC,oBAAA,GAAfD,eAAA,CAAiBG,IAAI,cAAAF,oBAAA,eAArBA,oBAAA,CAAuBG,MAAM,EAAE;QAAA,IAAAC,gBAAA,EAAAC,qBAAA;QACjCJ,YAAY,GAAGtD,KAAK,aAALA,KAAK,wBAAAyD,gBAAA,GAALzD,KAAK,CAAE2C,QAAQ,cAAAc,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBF,IAAI,cAAAG,qBAAA,uBAArBA,qBAAA,CAAuBF,MAAM;MAC9C;MAEAvD,QAAQ,CAACqD,YAAY,CAAC;MACtBvD,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAGA,IAAI,CAACV,QAAQ,CAACE,UAAU,IAAI,CAACF,QAAQ,CAACG,aAAa,EAAE;MACnDS,QAAQ,CAAC,2BAA2B,CAAC;MACrCF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAI;MACF,MAAM;QAAEwD,IAAI;QAAEvD;MAAM,CAAC,GAAG,MAAMpB,QAAQ,CAACkD,IAAI,CAAC6B,kBAAkB,CAAC;QAC7DC,KAAK,EAAEvE,QAAQ,CAACE,UAAU;QAC1BsE,QAAQ,EAAExE,QAAQ,CAACG;MACrB,CAAC,CAAC;MAEF,IAAIQ,KAAK,EAAE;QACT,IAAIA,KAAK,CAACE,OAAO,CAACe,QAAQ,CAAC,2BAA2B,CAAC,EAAE;UACvDhB,QAAQ,CAAC,2BAA2B,CAAC;QACvC,CAAC,MAAM;UACLA,QAAQ,CAACD,KAAK,CAACE,OAAO,CAAC;QACzB;QACA;MACF;;MAEA;MACA,IAAIqD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEO,IAAI,EAAE;QACdxC,MAAM,CAACpC,QAAQ,CAACqC,IAAI,GAAG,YAAY,CAAC,CAAC;QACrC;QACA;MAEF;IACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;MAAA,IAAA+D,gBAAA,EAAAC,qBAAA;MACd,IAAIV,YAAY,GAAG,iDAAiD;MACpE,IAAItD,KAAK,aAALA,KAAK,gBAAA+D,gBAAA,GAAL/D,KAAK,CAAE2C,QAAQ,cAAAoB,gBAAA,gBAAAC,qBAAA,GAAfD,gBAAA,CAAiBR,IAAI,cAAAS,qBAAA,eAArBA,qBAAA,CAAuBR,MAAM,EAAE;QAAA,IAAAS,gBAAA,EAAAC,qBAAA;QACjCZ,YAAY,GAAGtD,KAAK,aAALA,KAAK,wBAAAiE,gBAAA,GAALjE,KAAK,CAAE2C,QAAQ,cAAAsB,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBV,IAAI,cAAAW,qBAAA,uBAArBA,qBAAA,CAAuBV,MAAM;MAC9C;MAEAvD,QAAQ,CAACqD,YAAY,CAAC;IACxB,CAAC,SAAS;MACRvD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoE,YAAY,GAAG,MAAO9B,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;;IAElB;IACA,IAAItC,oBAAoB,EAAE;MACxBH,QAAQ,CAAC,kGAAkG,CAAC;MAC5G;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,EAAE,CAAC;;IAEd;IACA,IAAI,CAACd,QAAQ,CAACI,WAAW,IAAI,CAACJ,QAAQ,CAACK,cAAc,IAAI,CAACL,QAAQ,CAACM,qBAAqB,IACpF,CAACN,QAAQ,CAACO,SAAS,EAAG;MACxBK,QAAQ,CAAC,oCAAoC,CAAC;MAC9CF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;;IAEA;IACA,IAAIV,QAAQ,CAACK,cAAc,KAAKL,QAAQ,CAACM,qBAAqB,EAAE;MAC9DM,QAAQ,CAAC,wBAAwB,CAAC;MAClCF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAI;MACF;MACA,MAAMqE,eAAe,GAAG/E,QAAQ,CAACI,WAAW,CAAC4E,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAEjEzD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEsD,eAAe,CAAC;;MAEtD;MACA,MAAM;QAAEb,IAAI;QAAEvD,KAAK,EAAEuE;MAAY,CAAC,GAAG,MAAM3F,QAAQ,CAACkD,IAAI,CAAC0C,MAAM,CAAC;QAC9DZ,KAAK,EAAEQ,eAAe;QACtBP,QAAQ,EAAExE,QAAQ,CAACK,cAAc;QACjC+E,OAAO,EAAE;UACPlB,IAAI,EAAE;YACJmB,UAAU,EAAErF,QAAQ,CAACO,SAAS,CAACyE,IAAI,CAAC,CAAC;YACrCM,SAAS,EAAEtF,QAAQ,CAACQ,QAAQ,CAACwE,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,IAAI,GAAGhF,QAAQ,CAACQ,QAAQ,CAACwE,IAAI,CAAC;UAC7E;QACF;MACF,CAAC,CAAC;MAEFxD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEyC,IAAI,CAAC;MAC1C1C,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEyD,WAAW,CAAC;;MAEzC;MACA,IAAIA,WAAW,EAAE;QACf1D,OAAO,CAACb,KAAK,CAAC,uBAAuB,EAAEuE,WAAW,CAAC;QAEnD,IAAIA,WAAW,CAACrE,OAAO,CAACe,QAAQ,CAAC,gCAAgC,CAAC,EAAE;UAClEhB,QAAQ,CAAC,yDAAyD,CAAC;QACrE,CAAC,MAAM,IAAIsE,WAAW,CAACrE,OAAO,CAACe,QAAQ,CAAC,yBAAyB,CAAC,EAAE;UAClEhB,QAAQ,CAAC,sCAAsC,CAAC;QAClD,CAAC,MAAM;UACLA,QAAQ,CAACsE,WAAW,CAACrE,OAAO,IAAI,qBAAqB,CAAC;QACxD;QACA;MACF;;MAEA;MACA,IAAIqD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEO,IAAI,EAAE;QACd;;QAEA;QACA,IAAIP,IAAI,CAACO,IAAI,CAACc,UAAU,IAAIrB,IAAI,CAACO,IAAI,CAACc,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE;UAC7D5E,QAAQ,CAAC,yDAAyD,CAAC;UACnE;QACF;QAEAE,UAAU,CAAC,6CAA6C,CAAC;QACzD;QACAb,WAAW,CAACkD,IAAI,KAAK;UACnB,GAAGA,IAAI;UACP/C,WAAW,EAAE,EAAE;UACfC,cAAc,EAAE,EAAE;UAClBC,qBAAqB,EAAE,EAAE;UACzBC,SAAS,EAAE,EAAE;UACbC,QAAQ,EAAE;QACZ,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL;QACAgB,OAAO,CAACiE,IAAI,CAAC,2CAA2C,CAAC;QACzD7E,QAAQ,CAAC,2DAA2D,CAAC;MACvE;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MAAA,IAAA+E,gBAAA,EAAAC,qBAAA;MACdnE,OAAO,CAACb,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MAEvD,IAAIsD,YAAY,GAAG,iDAAiD;MACpE,IAAItD,KAAK,aAALA,KAAK,gBAAA+E,gBAAA,GAAL/E,KAAK,CAAE2C,QAAQ,cAAAoC,gBAAA,gBAAAC,qBAAA,GAAfD,gBAAA,CAAiBxB,IAAI,cAAAyB,qBAAA,eAArBA,qBAAA,CAAuBxB,MAAM,EAAE;QAAA,IAAAyB,gBAAA,EAAAC,qBAAA;QACjC5B,YAAY,GAAGtD,KAAK,aAALA,KAAK,wBAAAiF,gBAAA,GAALjF,KAAK,CAAE2C,QAAQ,cAAAsC,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiB1B,IAAI,cAAA2B,qBAAA,uBAArBA,qBAAA,CAAuB1B,MAAM;MAC9C;MAEAvD,QAAQ,CAACqD,YAAY,CAAC;IACxB,CAAC,SAAS;MACRvD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEhB,OAAA;IAAKoG,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBrG,OAAA;MAAKoG,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAE3BrG,OAAA;QAAKsG,KAAK,EAAE;UAACC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAM,CAAE;QAAAJ,QAAA,gBAE/DrG,OAAA;UACE0G,GAAG,EAAC,eAAe;UACnBC,GAAG,EAAC,WAAW;UACfP,SAAS,EAAC;QAAY;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACJ/G,OAAA;UAAMsG,KAAK,EAAE;YAACU,KAAK,EAAC;UAAM,CAAE;UAAAX,QAAA,EAAC;QAAC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrC/G,OAAA;UAAK0G,GAAG,EAAC,gBAAgB;UAAEN,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAACW,KAAK,EAAE,OAAO;YAAEC,MAAM,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAK;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAET,CAAC,eAEN/G,OAAA;MAAKuD,EAAE,EAAC,gBAAgB;MAAA8C,QAAA,gBACtBrG,OAAA;QAAKoG,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BrG,OAAA;UACEoG,SAAS,EAAE,cAAchG,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAE,EAAG;UACjEgH,OAAO,EAAEA,CAAA,KAAM/G,YAAY,CAAC,OAAO,CAAE;UAAAgG,QAAA,EACtC;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/G,OAAA;UACEoG,SAAS,EAAE,cAAchG,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;UAClEgH,OAAO,EAAEA,CAAA,KAAM/G,YAAY,CAAC,QAAQ,CAAE;UAAAgG,QAAA,EACvC;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN/G,OAAA;QAAKuD,EAAE,EAAC,OAAO;QAAC6C,SAAS,EAAE,eAAehG,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAE,EAAG;QAAAiG,QAAA,gBAChFrG,OAAA;UAAAqG,QAAA,EAAI;QAAK;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACd/G,OAAA;UAAMqH,QAAQ,EAAE3D,WAAY;UAAA2C,QAAA,gBAC1BrG,OAAA;YAAKoG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBrG,OAAA;cAAOsH,OAAO,EAAC,YAAY;cAAAjB,QAAA,GAAC,QAAM,eAAArG,OAAA;gBAAMsG,KAAK,EAAE;kBAACU,KAAK,EAAE;gBAA2B,CAAE;gBAAAX,QAAA,EAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrG/G,OAAA;cACE4B,IAAI,EAAC,OAAO;cACZ2B,EAAE,EAAC,YAAY;cACfV,KAAK,EAAEvC,QAAQ,CAACE,UAAW;cAC3B+G,QAAQ,EAAElE,iBAAkB;cAC5BmE,QAAQ;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN/G,OAAA;YAAKoG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBrG,OAAA;cAAOsH,OAAO,EAAC,eAAe;cAAAjB,QAAA,GAAC,WAAS,eAAArG,OAAA;gBAAMsG,KAAK,EAAE;kBAACU,KAAK,EAAE;gBAA2B,CAAE;gBAAAX,QAAA,EAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3G/G,OAAA;cACE4B,IAAI,EAAC,UAAU;cACf2B,EAAE,EAAC,eAAe;cAClBV,KAAK,EAAEvC,QAAQ,CAACG,aAAc;cAC9B8G,QAAQ,EAAElE,iBAAkB;cAC5BmE,QAAQ;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN/G,OAAA;YAAQ4B,IAAI,EAAC,QAAQ;YAACwE,SAAS,EAAC,KAAK;YAACqB,QAAQ,EAAE1G,OAAQ;YAAAsF,QAAA,EACrDtF,OAAO,GAAG,eAAe,GAAG;UAAO;YAAA6F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN/G,OAAA;QAAKuD,EAAE,EAAC,QAAQ;QAAC6C,SAAS,EAAE,eAAehG,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;QAAAiG,QAAA,gBAClFrG,OAAA;UAAAqG,QAAA,EAAI;QAAO;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChB/G,OAAA;UAAMqH,QAAQ,EAAEjC,YAAa;UAAAiB,QAAA,gBAC3BrG,OAAA;YAAKoG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBrG,OAAA;cAAOsH,OAAO,EAAC,WAAW;cAAAjB,QAAA,GAAC,aAAW,eAAArG,OAAA;gBAAMsG,KAAK,EAAE;kBAACU,KAAK,EAAE;gBAA2B,CAAE;gBAAAX,QAAA,EAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzG/G,OAAA;cACE4B,IAAI,EAAC,MAAM;cACX2B,EAAE,EAAC,WAAW;cACdV,KAAK,EAAEvC,QAAQ,CAACO,SAAU;cAC1B0G,QAAQ,EAAElE,iBAAkB;cAC5BmE,QAAQ;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN/G,OAAA;YAAKoG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBrG,OAAA;cAAOsH,OAAO,EAAC,UAAU;cAAAjB,QAAA,EAAC;YAAS;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3C/G,OAAA;cACE4B,IAAI,EAAC,MAAM;cACX2B,EAAE,EAAC,UAAU;cACbV,KAAK,EAAEvC,QAAQ,CAACQ,QAAS;cACzByG,QAAQ,EAAElE;YAAkB;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN/G,OAAA;YAAKoG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBrG,OAAA;cAAOsH,OAAO,EAAC,aAAa;cAAAjB,QAAA,GAAC,QAAM,eAAArG,OAAA;gBAAMsG,KAAK,EAAE;kBAACU,KAAK,EAAE;gBAA2B,CAAE;gBAAAX,QAAA,EAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxG/G,OAAA;cACE4B,IAAI,EAAC,OAAO;cACV2B,EAAE,EAAC,aAAa;cAChBV,KAAK,EAAEvC,QAAQ,CAACI,WAAY;cAC5B6G,QAAQ,EAAElE,iBAAkB;cAC5BmE,QAAQ;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN/G,OAAA;YAAKoG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBrG,OAAA;cAAOsH,OAAO,EAAC,gBAAgB;cAAAjB,QAAA,GAAC,WAAS,eAAArG,OAAA;gBAAMsG,KAAK,EAAE;kBAACU,KAAK,EAAE;gBAA2B,CAAE;gBAAAX,QAAA,EAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5G/G,OAAA;cACE4B,IAAI,EAAC,UAAU;cACf2B,EAAE,EAAC,gBAAgB;cACnBV,KAAK,EAAEvC,QAAQ,CAACK,cAAe;cAC/B4G,QAAQ,EAAElE,iBAAkB;cAC5BmE,QAAQ;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN/G,OAAA;YAAKoG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBrG,OAAA;cAAOsH,OAAO,EAAC,uBAAuB;cAAAjB,QAAA,GAAC,mBAAiB,eAAArG,OAAA;gBAAMsG,KAAK,EAAE;kBAACU,KAAK,EAAE;gBAA2B,CAAE;gBAAAX,QAAA,EAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7H/G,OAAA;cACE4B,IAAI,EAAC,UAAU;cACb2B,EAAE,EAAC,uBAAuB;cAC1BV,KAAK,EAAEvC,QAAQ,CAACM,qBAAsB;cACtC2G,QAAQ,EAAElE,iBAAkB;cAC5BmE,QAAQ;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACJ/G,OAAA;YAAQ4B,IAAI,EAAC,QAAQ;YAACwE,SAAS,EAAC,KAAK;YAACqB,QAAQ,EAAE1G,OAAQ;YAAAsF,QAAA,EACrDtF,OAAO,GAAG,eAAe,GAAG;UAAS;YAAA6F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACP/G,OAAA;UAAGoG,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAEzB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAEL9F,KAAK,iBAAIjB,OAAA;MAAKoG,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAEpF;IAAK;MAAA2F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACrD5F,OAAO,iBAAInB,OAAA;MAAKoG,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAAElF;IAAO;MAAAyF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzD,CAAC;AAEV,CAAC;AAAC7G,EAAA,CA3aID,KAAK;EAAA,QACQL,WAAW;AAAA;AAAA8H,EAAA,GADxBzH,KAAK;AA6aX,eAAeA,KAAK;AAAC,IAAAyH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}