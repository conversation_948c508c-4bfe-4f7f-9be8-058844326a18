{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\zipp\\\\latent-frontend\\\\src\\\\components\\\\pages\\\\GapAnalysis\\\\AssessmentForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport sopService from '../../../services/sopService';\nimport regulationService from '../../../services/regulationService';\nimport departmentService from '../../../services/departmentService';\nimport apiService from '../../../services/api';\nimport API_URLS from '../../../config/apiUrls';\nimport { toast } from 'react-toastify';\nimport LoadingSpinner from '../../common/LoadingSpinner/LoadingSpinner';\nimport './AssessmentForm.css';\nimport { sanitizeText } from '../../../utils/sanitize';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AssessmentForm = ({\n  onClose,\n  onSubmit,\n  onSuccess,\n  onProcessingStart,\n  onError\n}) => {\n  _s();\n  const [sops, setSops] = useState([]);\n  const [regulations, setRegulations] = useState([]);\n  const [departments, setDepartments] = useState([]);\n  const [showSOPDropdown, setShowSOPDropdown] = useState(false);\n  const [showDepartmentDropdown, setShowDepartmentDropdown] = useState(false);\n  const [showRegulationDropdown, setShowRegulationDropdown] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const sopDropdownRef = useRef(null);\n  const departmentDropdownRef = useRef(null);\n  const regulationDropdownRef = useRef(null);\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    selectedDepartment: null,\n    category: '',\n    selectedComplianceRequirements: [],\n    selectedSOP: null\n  });\n\n  // First, add state variables to track loading state for each dropdown\n  const [sopsLoading, setSOPsLoading] = useState(true);\n  const [regulationsLoading, setRegulationsLoading] = useState(true);\n  const [departmentsLoading, setDepartmentsLoading] = useState(true);\n\n  // Add state for SOP search and error handling\n  const [sopSearchTerm, setSopSearchTerm] = useState('');\n  const [sopsError, setSopsError] = useState(null);\n  const [departmentsError, setDepartmentsError] = useState(null);\n  const [regulationsError, setRegulationsError] = useState(null);\n\n  // Add state for regulation search\n  const [regulationSearchTerm, setRegulationSearchTerm] = useState('');\n\n  // Prevent body scroll when modal is open\n  useEffect(() => {\n    // Prevent scrolling when modal is open\n    document.body.style.overflow = 'hidden';\n\n    // Cleanup function to restore scrolling when component unmounts\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, []);\n\n  // Fetch SOPs, departments, and regulations on component mount\n  useEffect(() => {\n    const fetchSOPs = async () => {\n      try {\n        setSOPsLoading(true);\n        setSopsError(null);\n        const sopData = await sopService.getAllSOPs();\n        setSops(sopData.sops_data || []);\n      } catch (err) {\n        var _err$response, _err$response$data;\n        console.error('Error fetching SOPs:', err);\n        let errorMessage = 'Failed to load SOPs. Please try again later.';\n        if (err !== null && err !== void 0 && (_err$response = err.response) !== null && _err$response !== void 0 && (_err$response$data = _err$response.data) !== null && _err$response$data !== void 0 && _err$response$data.detail) {\n          var _err$response2, _err$response2$data;\n          errorMessage = err === null || err === void 0 ? void 0 : (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.detail;\n        }\n        setSopsError(errorMessage);\n      } finally {\n        setSOPsLoading(false);\n      }\n    };\n    const fetchDepartments = async () => {\n      try {\n        const departmentData = await departmentService.getAllDepartments();\n        setDepartments(departmentData);\n      } catch (err) {\n        var _err$response3, _err$response3$data;\n        console.error('Error fetching departments:', err);\n        let errorMessage = 'Failed to load departments. Please try again later.';\n        if (err !== null && err !== void 0 && (_err$response3 = err.response) !== null && _err$response3 !== void 0 && (_err$response3$data = _err$response3.data) !== null && _err$response3$data !== void 0 && _err$response3$data.detail) {\n          var _err$response4, _err$response4$data;\n          errorMessage = err === null || err === void 0 ? void 0 : (_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.detail;\n        }\n        setDepartmentsError(errorMessage);\n      } finally {\n        setDepartmentsLoading(false);\n      }\n    };\n    const fetchRegulations = async () => {\n      try {\n        setRegulationsLoading(true);\n        const regulationData = await regulationService.getOrganizationRegulations();\n        let regulationData_details = regulationData.map(regulation => regulation.regulation_details);\n        setRegulations(regulationData_details);\n      } catch (err) {\n        var _err$response5, _err$response5$data;\n        console.error('Error fetching regulations:', err);\n        let errorMessage = 'Failed to load compliance requirements. Please try again later.';\n        if (err !== null && err !== void 0 && (_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.detail) {\n          var _err$response6, _err$response6$data;\n          errorMessage = err === null || err === void 0 ? void 0 : (_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.detail;\n        }\n        setRegulationsError(errorMessage);\n      } finally {\n        setRegulationsLoading(false);\n      }\n    };\n    fetchSOPs();\n    fetchDepartments();\n    fetchRegulations();\n  }, []);\n\n  // Close dropdowns when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (sopDropdownRef.current && !sopDropdownRef.current.contains(event.target)) {\n        setShowSOPDropdown(false);\n      }\n      if (departmentDropdownRef.current && !departmentDropdownRef.current.contains(event.target)) {\n        setShowDepartmentDropdown(false);\n      }\n      if (regulationDropdownRef.current && !regulationDropdownRef.current.contains(event.target)) {\n        setShowRegulationDropdown(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  // Handle form input changes\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  // Handle SOP selection\n  const handleSOPSelect = sop => {\n    setFormData({\n      ...formData,\n      selectedSOP: sop\n    });\n    setShowSOPDropdown(false);\n  };\n\n  // Handle department selection\n  const selectDepartment = department => {\n    setFormData({\n      ...formData,\n      selectedDepartment: department\n    });\n    setShowDepartmentDropdown(false);\n  };\n\n  // Handle regulation selection (toggle)\n  const handleRegulationToggle = regulation => {\n    setFormData(prev => {\n      const isAlreadySelected = prev.selectedComplianceRequirements.some(r => r.id === regulation.id);\n      if (isAlreadySelected) {\n        // Remove the regulation if already selected\n        return {\n          ...prev,\n          selectedComplianceRequirements: prev.selectedComplianceRequirements.filter(r => r.id !== regulation.id)\n        };\n      } else {\n        // Add the regulation if not already selected\n        return {\n          ...prev,\n          selectedComplianceRequirements: [...prev.selectedComplianceRequirements, regulation]\n        };\n      }\n    });\n  };\n\n  // Handle search input change\n  const handleSearchChange = e => {\n    setSearchTerm(e.target.value);\n  };\n\n  // Add validation logic to check if form is complete\n  const isFormValid = () => {\n    return formData.selectedSOP &&\n    // SOP must be selected\n    formData.selectedComplianceRequirements && formData.selectedComplianceRequirements.length > 0 // At least one compliance requirement must be selected\n    ;\n  };\n\n  // Filter regulations based on search term\n  const filteredRegulations = regulations.filter(regulation => regulation.name.toLowerCase().includes(searchTerm.toLowerCase()));\n\n  // Handle form submission\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (loading) return;\n\n    // Validate form\n    if (!formData.selectedSOP) {\n      toast.error('Please select a SOP for assessment', {\n        toastId: `sop-required-${Date.now()}`\n      });\n      return;\n    }\n    if (!formData.selectedComplianceRequirements || formData.selectedComplianceRequirements.length === 0) {\n      toast.error('Please select at least one compliance requirement', {\n        toastId: `compliance-required-${Date.now()}`\n      });\n      return;\n    }\n    try {\n      var _formData$selectedDep;\n      setLoading(true);\n      setError(null);\n\n      // Sanitize form data before submission\n      const sanitizedFormData = {\n        ...formData,\n        title: sanitizeText(formData.title),\n        department: sanitizeText((_formData$selectedDep = formData.selectedDepartment) === null || _formData$selectedDep === void 0 ? void 0 : _formData$selectedDep.name),\n        description: sanitizeText(formData.description)\n        // Sanitize any other text fields\n      };\n      const regulationIds = sanitizedFormData.selectedComplianceRequirements.map(req => req.id);\n\n      // Call the processing start handler to close modal and show processing state\n      if (typeof onProcessingStart === 'function') {\n        onProcessingStart(sanitizedFormData.selectedSOP.id);\n      }\n\n      // Make the API call\n      const response = await apiService.post(`${API_URLS.ANALYSIS.ANALYZE}/${sanitizedFormData.selectedSOP.id}`, regulationIds);\n      console.log('Assessment API response:', response);\n\n      // Show success message\n      toast.success('Assessment created successfully', {\n        toastId: `assessment-created-${Date.now()}`\n      });\n\n      // Call the parent's refresh function if provided\n      if (typeof onSuccess === 'function') {\n        onSuccess();\n      } else if (typeof onSubmit === 'function') {\n        onSubmit();\n      }\n    } catch (err) {\n      var _err$response7, _err$response7$data;\n      console.error('Error creating assessment:', err);\n      let errorMessage = 'Failed to create assessment. Please try again later.';\n      if (err !== null && err !== void 0 && (_err$response7 = err.response) !== null && _err$response7 !== void 0 && (_err$response7$data = _err$response7.data) !== null && _err$response7$data !== void 0 && _err$response7$data.detail) {\n        var _err$response8, _err$response8$data;\n        errorMessage = err === null || err === void 0 ? void 0 : (_err$response8 = err.response) === null || _err$response8 === void 0 ? void 0 : (_err$response8$data = _err$response8.data) === null || _err$response8$data === void 0 ? void 0 : _err$response8$data.detail;\n      }\n      setError(errorMessage);\n      toast.error(errorMessage, {\n        toastId: `assessment-error-${Date.now()}`\n      });\n      if (typeof onError === 'function') {\n        onError(errorMessage);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Update the SOP dropdown to include search functionality\n  const renderSOPDropdown = () => {\n    // Sort SOPs by date (most recent first)\n    const sortedSOPs = [...sops].sort((a, b) => {\n      // Get the date from updated_at or created_at, fallback to current date\n      const dateA = new Date(a.updated_at || a.created_at || Date.now());\n      const dateB = new Date(b.updated_at || b.created_at || Date.now());\n      return dateB - dateA; // Sort in descending order (newest first)\n    });\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sop-selector\",\n      ref: sopDropdownRef,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"selected-sop-display\",\n        onClick: () => setShowSOPDropdown(!showSOPDropdown),\n        \"aria-expanded\": showSOPDropdown,\n        children: [sopsLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dropdown-loading\",\n          children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Loading SOPs...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this) : formData.selectedSOP ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"selected-sop\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: sanitizeText(formData.selectedSOP.title || 'Unnamed SOP')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"clear-sop\",\n            onClick: e => {\n              e.stopPropagation();\n              setFormData({\n                ...formData,\n                selectedSOP: null\n              });\n            },\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"placeholder\",\n          children: \"Select a SOP\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"dropdown-arrow\",\n          children: \"\\u25BC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), showSOPDropdown && !sopsLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dropdown-menu\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dropdown-search\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search SOPs...\",\n            value: sopSearchTerm,\n            onChange: e => setSopSearchTerm(e.target.value),\n            onClick: e => e.stopPropagation(),\n            autoFocus: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 13\n        }, this), sopsError ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dropdown-message error\",\n          children: sopsError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 15\n        }, this) : sortedSOPs.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dropdown-message\",\n          children: \"No SOPs available. Please add SOPs first.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dropdown-options\",\n          children: [sortedSOPs.filter(sop => (sop.title || 'Unnamed SOP').toLowerCase().includes(sopSearchTerm.toLowerCase())).map(sop => {\n            var _formData$selectedSOP;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `sop-option ${((_formData$selectedSOP = formData.selectedSOP) === null || _formData$selectedSOP === void 0 ? void 0 : _formData$selectedSOP.id) === sop.id ? 'selected' : ''}`,\n              onClick: () => handleSOPSelect(sop),\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sop-option-content\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sop-title\",\n                  children: sanitizeText(sop.title || 'Unnamed SOP')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 23\n              }, this)\n            }, sop.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 21\n            }, this);\n          }), sortedSOPs.filter(sop => (sop.title || 'Unnamed SOP').toLowerCase().includes(sopSearchTerm.toLowerCase())).length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dropdown-message\",\n            children: \"No SOPs match your search.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Update the regulation dropdown to include search functionality\n  const renderRegulationDropdown = () => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"regulation-selector\",\n      ref: regulationDropdownRef,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"selected-sop-display\",\n        onClick: () => setShowRegulationDropdown(!showRegulationDropdown),\n        children: [regulationsLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dropdown-loading\",\n          children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Loading requirements...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 13\n        }, this) : formData.selectedComplianceRequirements && formData.selectedComplianceRequirements.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"selected-sops-tags\",\n          children: formData.selectedComplianceRequirements.map(req => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sop-tag\",\n            children: [req.name, /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"remove-tag\",\n              onClick: e => {\n                e.stopPropagation();\n                setFormData(prev => ({\n                  ...prev,\n                  selectedComplianceRequirements: prev.selectedComplianceRequirements.filter(r => r.id !== req.id)\n                }));\n              },\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 19\n            }, this)]\n          }, req.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"placeholder\",\n          children: \"Select compliance requirements\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"dropdown-arrow\",\n          children: \"\\u25BC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this), showRegulationDropdown && !regulationsLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dropdown-menu\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dropdown-search\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search compliance...\",\n            value: regulationSearchTerm,\n            onChange: e => setRegulationSearchTerm(e.target.value),\n            onClick: e => e.stopPropagation(),\n            autoFocus: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 13\n        }, this), regulations.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dropdown-message\",\n          children: \"No compliance requirements available.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dropdown-options\",\n          children: [regulations.filter(reg => (reg.name || '').toLowerCase().includes(regulationSearchTerm.toLowerCase())).map(reg => {\n            const isSelected = formData.selectedComplianceRequirements.some(r => r.id === reg.id);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `sop-option ${isSelected ? 'selected' : ''}`,\n              onClick: () => handleRegulationToggle(reg),\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sop-option-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"checkbox\",\n                  children: isSelected && '✓'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sop-title\",\n                  children: reg.name || 'Unnamed Requirement'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 25\n              }, this)\n            }, reg.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 23\n            }, this);\n          }), regulations.filter(reg => (reg.name || '').toLowerCase().includes(regulationSearchTerm.toLowerCase())).length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dropdown-message\",\n            children: \"No requirements match your search.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Add useEffect to handle dynamic spacing when dropdowns open/close\n  useEffect(() => {\n    const updateDropdownSpacing = () => {\n      if (showSOPDropdown && sopDropdownRef.current) {\n        const dropdownMenu = sopDropdownRef.current.querySelector('.dropdown-menu');\n        if (dropdownMenu) {\n          const dropdownHeight = dropdownMenu.offsetHeight;\n          sopDropdownRef.current.style.marginBottom = `${dropdownHeight + 20}px`;\n        }\n      } else if (sopDropdownRef.current) {\n        sopDropdownRef.current.style.marginBottom = '0px';\n      }\n    };\n\n    // Small delay to ensure DOM is updated\n    const timeoutId = setTimeout(updateDropdownSpacing, 10);\n    return () => clearTimeout(timeoutId);\n  }, [showSOPDropdown, sops.length, sopSearchTerm]);\n  useEffect(() => {\n    const updateDropdownSpacing = () => {\n      if (showRegulationDropdown && regulationDropdownRef.current) {\n        const dropdownMenu = regulationDropdownRef.current.querySelector('.dropdown-menu');\n        if (dropdownMenu) {\n          const dropdownHeight = dropdownMenu.offsetHeight;\n          regulationDropdownRef.current.style.marginBottom = `${dropdownHeight + 20}px`;\n        }\n      } else if (regulationDropdownRef.current) {\n        regulationDropdownRef.current.style.marginBottom = '0px';\n      }\n    };\n\n    // Small delay to ensure DOM is updated\n    const timeoutId = setTimeout(updateDropdownSpacing, 10);\n    return () => clearTimeout(timeoutId);\n  }, [showRegulationDropdown, regulations.length, regulationSearchTerm]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"assessment-form\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Create Gap Assessment\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 531,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Select SOPs for assessment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 559,\n          columnNumber: 13\n        }, this), renderSOPDropdown()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 558,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Compliance Requirements\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 11\n        }, this), renderRegulationDropdown()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 608,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 613,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"assessment-form-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"btn-cancel\",\n          onClick: onClose,\n          disabled: loading,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: `create-assessment-btn ${loading ? 'creating' : ''} ${!isFormValid() ? 'disabled' : ''}`,\n          disabled: loading || !isFormValid(),\n          title: !isFormValid() ? 'Please select a SOP and at least one compliance requirement' : '',\n          children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Creating...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : 'Create Assessment'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 615,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 533,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 530,\n    columnNumber: 5\n  }, this);\n};\n_s(AssessmentForm, \"X9BaZ8V29Ff7llqrqD1lceMTxjA=\");\n_c = AssessmentForm;\nexport default AssessmentForm;\nvar _c;\n$RefreshReg$(_c, \"AssessmentForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "sopService", "regulationService", "departmentService", "apiService", "API_URLS", "toast", "LoadingSpinner", "sanitizeText", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AssessmentForm", "onClose", "onSubmit", "onSuccess", "onProcessingStart", "onError", "_s", "sops", "setSops", "regulations", "setRegulations", "departments", "setDepartments", "showSOPDropdown", "setShowSOPDropdown", "showDepartmentDropdown", "setShowDepartmentDropdown", "showRegulationDropdown", "setShowRegulationDropdown", "searchTerm", "setSearchTerm", "loading", "setLoading", "error", "setError", "sopDropdownRef", "departmentDropdownRef", "regulationDropdownRef", "formData", "setFormData", "title", "description", "selectedDepartment", "category", "selectedComplianceRequirements", "selectedSOP", "sopsLoading", "setSOPsLoading", "regulationsLoading", "setRegulationsLoading", "departmentsLoading", "setDepartmentsLoading", "sopSearchTerm", "setSopSearchTerm", "sopsError", "setSopsError", "departmentsError", "setDepartmentsError", "regulationsError", "setRegulationsError", "regulationSearchTerm", "setRegulationSearchTerm", "document", "body", "style", "overflow", "fetchSOPs", "sopData", "getAllSOPs", "sops_data", "err", "_err$response", "_err$response$data", "console", "errorMessage", "response", "data", "detail", "_err$response2", "_err$response2$data", "fetchDepartments", "departmentData", "getAllDepartments", "_err$response3", "_err$response3$data", "_err$response4", "_err$response4$data", "fetchRegulations", "regulationData", "getOrganizationRegulations", "regulationData_details", "map", "regulation", "regulation_details", "_err$response5", "_err$response5$data", "_err$response6", "_err$response6$data", "handleClickOutside", "event", "current", "contains", "target", "addEventListener", "removeEventListener", "handleInputChange", "e", "name", "value", "handleSOPSelect", "sop", "selectDepartment", "department", "handleRegulationToggle", "prev", "isAlreadySelected", "some", "r", "id", "filter", "handleSearchChange", "isFormValid", "length", "filteredRegulations", "toLowerCase", "includes", "handleSubmit", "preventDefault", "toastId", "Date", "now", "_formData$selectedDep", "sanitizedFormData", "regulationIds", "req", "post", "ANALYSIS", "ANALYZE", "log", "success", "_err$response7", "_err$response7$data", "_err$response8", "_err$response8$data", "renderSOPDropdown", "sortedSOPs", "sort", "a", "b", "dateA", "updated_at", "created_at", "dateB", "className", "ref", "children", "onClick", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "stopPropagation", "type", "placeholder", "onChange", "autoFocus", "_formData$selectedSOP", "renderRegulationDropdown", "reg", "isSelected", "updateDropdownSpacing", "dropdownMenu", "querySelector", "dropdownHeight", "offsetHeight", "marginBottom", "timeoutId", "setTimeout", "clearTimeout", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/components/pages/GapAnalysis/AssessmentForm.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport sopService from '../../../services/sopService';\r\nimport regulationService from '../../../services/regulationService';\r\nimport departmentService from '../../../services/departmentService';\r\nimport apiService from '../../../services/api';\r\nimport API_URLS from '../../../config/apiUrls';\r\nimport { toast } from 'react-toastify';\r\nimport LoadingSpinner from '../../common/LoadingSpinner/LoadingSpinner';\r\nimport './AssessmentForm.css';\r\nimport { sanitizeText } from '../../../utils/sanitize';\r\n\r\nconst AssessmentForm = ({ onClose, onSubmit, onSuccess, onProcessingStart, onError }) => {\r\n  const [sops, setSops] = useState([]);\r\n  const [regulations, setRegulations] = useState([]);\r\n  const [departments, setDepartments] = useState([]);\r\n  const [showSOPDropdown, setShowSOPDropdown] = useState(false);\r\n  const [showDepartmentDropdown, setShowDepartmentDropdown] = useState(false);\r\n  const [showRegulationDropdown, setShowRegulationDropdown] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  \r\n  const sopDropdownRef = useRef(null);\r\n  const departmentDropdownRef = useRef(null);\r\n  const regulationDropdownRef = useRef(null);\r\n  \r\n  const [formData, setFormData] = useState({\r\n    title: '',\r\n    description: '',\r\n    selectedDepartment: null,\r\n    category: '',\r\n    selectedComplianceRequirements: [],\r\n    selectedSOP: null\r\n  });\r\n  \r\n  // First, add state variables to track loading state for each dropdown\r\n  const [sopsLoading, setSOPsLoading] = useState(true);\r\n  const [regulationsLoading, setRegulationsLoading] = useState(true);\r\n  const [departmentsLoading, setDepartmentsLoading] = useState(true);\r\n  \r\n  // Add state for SOP search and error handling\r\n  const [sopSearchTerm, setSopSearchTerm] = useState('');\r\n  const [sopsError, setSopsError] = useState(null);\r\n  const [departmentsError, setDepartmentsError] = useState(null);\r\n  const [regulationsError, setRegulationsError] = useState(null);\r\n  \r\n  // Add state for regulation search\r\n  const [regulationSearchTerm, setRegulationSearchTerm] = useState('');\r\n  \r\n  // Prevent body scroll when modal is open\r\n  useEffect(() => {\r\n    // Prevent scrolling when modal is open\r\n    document.body.style.overflow = 'hidden';\r\n\r\n    // Cleanup function to restore scrolling when component unmounts\r\n    return () => {\r\n      document.body.style.overflow = 'unset';\r\n    };\r\n  }, []);\r\n  \r\n  // Fetch SOPs, departments, and regulations on component mount\r\n  useEffect(() => {\r\n    const fetchSOPs = async () => {\r\n      try {\r\n        setSOPsLoading(true);\r\n        setSopsError(null);\r\n        const sopData = await sopService.getAllSOPs();\r\n        setSops(sopData.sops_data || []);\r\n      } catch (err) {\r\n        console.error('Error fetching SOPs:', err);\r\n        \r\n        let errorMessage = 'Failed to load SOPs. Please try again later.';\r\n        if (err?.response?.data?.detail) {\r\n          errorMessage = err?.response?.data?.detail;\r\n        }\r\n        \r\n        setSopsError(errorMessage);\r\n      } finally {\r\n        setSOPsLoading(false);\r\n      }\r\n    };\r\n    \r\n    const fetchDepartments = async () => {\r\n      try {\r\n        const departmentData = await departmentService.getAllDepartments();\r\n        setDepartments(departmentData);\r\n      } catch (err) {\r\n        console.error('Error fetching departments:', err);\r\n        \r\n        let errorMessage = 'Failed to load departments. Please try again later.';\r\n        if (err?.response?.data?.detail) {\r\n          errorMessage = err?.response?.data?.detail;\r\n        }\r\n        \r\n        setDepartmentsError(errorMessage);\r\n      } finally {\r\n        setDepartmentsLoading(false);\r\n      }\r\n    };\r\n    \r\n    const fetchRegulations = async () => {\r\n      try {\r\n        setRegulationsLoading(true);\r\n        const regulationData = await regulationService.getOrganizationRegulations();\r\n        let regulationData_details = regulationData.map(regulation => regulation.regulation_details);\r\n        setRegulations(regulationData_details);\r\n      } catch (err) {\r\n        console.error('Error fetching regulations:', err);\r\n        \r\n        let errorMessage = 'Failed to load compliance requirements. Please try again later.';\r\n        if (err?.response?.data?.detail) {\r\n          errorMessage = err?.response?.data?.detail;\r\n        }\r\n        \r\n        setRegulationsError(errorMessage);\r\n      } finally {\r\n        setRegulationsLoading(false);\r\n      }\r\n    };\r\n    \r\n    fetchSOPs();\r\n    fetchDepartments();\r\n    fetchRegulations();\r\n  }, []);\r\n  \r\n  // Close dropdowns when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (sopDropdownRef.current && !sopDropdownRef.current.contains(event.target)) {\r\n        setShowSOPDropdown(false);\r\n      }\r\n      if (departmentDropdownRef.current && !departmentDropdownRef.current.contains(event.target)) {\r\n        setShowDepartmentDropdown(false);\r\n      }\r\n      if (regulationDropdownRef.current && !regulationDropdownRef.current.contains(event.target)) {\r\n        setShowRegulationDropdown(false);\r\n      }\r\n    };\r\n    \r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, []);\r\n  \r\n  // Handle form input changes\r\n  const handleInputChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({\r\n      ...formData,\r\n      [name]: value\r\n    });\r\n  };\r\n  \r\n  // Handle SOP selection\r\n  const handleSOPSelect = (sop) => {\r\n    setFormData({\r\n      ...formData,\r\n      selectedSOP: sop\r\n    });\r\n    setShowSOPDropdown(false);\r\n  };\r\n  \r\n  // Handle department selection\r\n  const selectDepartment = (department) => {\r\n    setFormData({\r\n      ...formData,\r\n      selectedDepartment: department\r\n    });\r\n    setShowDepartmentDropdown(false);\r\n  };\r\n  \r\n  // Handle regulation selection (toggle)\r\n  const handleRegulationToggle = (regulation) => {\r\n    setFormData(prev => {\r\n      const isAlreadySelected = prev.selectedComplianceRequirements.some(r => r.id === regulation.id);\r\n      \r\n      if (isAlreadySelected) {\r\n        // Remove the regulation if already selected\r\n        return {\r\n          ...prev,\r\n          selectedComplianceRequirements: prev.selectedComplianceRequirements.filter(r => r.id !== regulation.id)\r\n        };\r\n      } else {\r\n        // Add the regulation if not already selected\r\n        return {\r\n          ...prev,\r\n          selectedComplianceRequirements: [...prev.selectedComplianceRequirements, regulation]\r\n        };\r\n      }\r\n    });\r\n  };\r\n  \r\n  // Handle search input change\r\n  const handleSearchChange = (e) => {\r\n    setSearchTerm(e.target.value);\r\n  };\r\n  \r\n  // Add validation logic to check if form is complete\r\n  const isFormValid = () => {\r\n    return (\r\n      formData.selectedSOP && // SOP must be selected\r\n      formData.selectedComplianceRequirements && \r\n      formData.selectedComplianceRequirements.length > 0 // At least one compliance requirement must be selected\r\n    );\r\n  };\r\n  \r\n  // Filter regulations based on search term\r\n  const filteredRegulations = regulations.filter(regulation => \r\n    regulation.name.toLowerCase().includes(searchTerm.toLowerCase())\r\n  );\r\n\r\n  // Handle form submission\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    \r\n    if (loading) return;\r\n    \r\n    // Validate form\r\n    if (!formData.selectedSOP) {\r\n      toast.error('Please select a SOP for assessment', {\r\n        toastId: `sop-required-${Date.now()}`\r\n      });\r\n      return;\r\n    }\r\n    \r\n    if (!formData.selectedComplianceRequirements || formData.selectedComplianceRequirements.length === 0) {\r\n      toast.error('Please select at least one compliance requirement', {\r\n        toastId: `compliance-required-${Date.now()}`\r\n      });\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      \r\n      // Sanitize form data before submission\r\n      const sanitizedFormData = {\r\n        ...formData,\r\n        title: sanitizeText(formData.title),\r\n        department: sanitizeText(formData.selectedDepartment?.name),\r\n        description: sanitizeText(formData.description),\r\n        // Sanitize any other text fields\r\n      };\r\n      \r\n      const regulationIds = sanitizedFormData.selectedComplianceRequirements.map(req => req.id);\r\n      \r\n      // Call the processing start handler to close modal and show processing state\r\n      if (typeof onProcessingStart === 'function') {\r\n        onProcessingStart(sanitizedFormData.selectedSOP.id);\r\n      }\r\n      \r\n      // Make the API call\r\n      const response = await apiService.post(\r\n        `${API_URLS.ANALYSIS.ANALYZE}/${sanitizedFormData.selectedSOP.id}`,\r\n        regulationIds\r\n      );\r\n      \r\n      console.log('Assessment API response:', response);\r\n      \r\n      // Show success message\r\n      toast.success('Assessment created successfully', {\r\n        toastId: `assessment-created-${Date.now()}`\r\n      });\r\n      \r\n      // Call the parent's refresh function if provided\r\n      if (typeof onSuccess === 'function') {\r\n        onSuccess();\r\n      } else if (typeof onSubmit === 'function') {\r\n        onSubmit();\r\n      }\r\n      \r\n    } catch (err) {\r\n      console.error('Error creating assessment:', err);\r\n      \r\n      let errorMessage = 'Failed to create assessment. Please try again later.';\r\n      if (err?.response?.data?.detail) {\r\n        errorMessage = err?.response?.data?.detail;\r\n      }\r\n      \r\n      setError(errorMessage);\r\n      \r\n      toast.error(errorMessage, {\r\n        toastId: `assessment-error-${Date.now()}`\r\n      });\r\n      \r\n      if (typeof onError === 'function') {\r\n        onError(errorMessage);\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  // Update the SOP dropdown to include search functionality\r\n  const renderSOPDropdown = () => {\r\n    // Sort SOPs by date (most recent first)\r\n    const sortedSOPs = [...sops].sort((a, b) => {\r\n      // Get the date from updated_at or created_at, fallback to current date\r\n      const dateA = new Date(a.updated_at || a.created_at || Date.now());\r\n      const dateB = new Date(b.updated_at || b.created_at || Date.now());\r\n      return dateB - dateA; // Sort in descending order (newest first)\r\n    });\r\n    \r\n    return (\r\n      <div className=\"sop-selector\" ref={sopDropdownRef}>\r\n        <div \r\n          className=\"selected-sop-display\"\r\n          onClick={() => setShowSOPDropdown(!showSOPDropdown)}\r\n          aria-expanded={showSOPDropdown}\r\n        >\r\n          {sopsLoading ? (\r\n            <div className=\"dropdown-loading\">\r\n              <LoadingSpinner size=\"small\" />\r\n              <span>Loading SOPs...</span>\r\n            </div>\r\n          ) : formData.selectedSOP ? (\r\n            <div className=\"selected-sop\">\r\n              <span>{sanitizeText(formData.selectedSOP.title || 'Unnamed SOP')}</span>\r\n              <button \r\n                className=\"clear-sop\"\r\n                onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  setFormData({ ...formData, selectedSOP: null });\r\n                }}\r\n              >\r\n                ×\r\n              </button>\r\n            </div>\r\n          ) : (\r\n            <span className=\"placeholder\">Select a SOP</span>\r\n          )}\r\n          <span className=\"dropdown-arrow\">▼</span>\r\n        </div>\r\n        \r\n        {showSOPDropdown && !sopsLoading && (\r\n          <div className=\"dropdown-menu\">\r\n            {/* Add search input */}\r\n            <div className=\"dropdown-search\">\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Search SOPs...\"\r\n                value={sopSearchTerm}\r\n                onChange={(e) => setSopSearchTerm(e.target.value)}\r\n                onClick={(e) => e.stopPropagation()}\r\n                autoFocus\r\n              />\r\n            </div>\r\n            \r\n            {sopsError ? (\r\n              <div className=\"dropdown-message error\">\r\n                {sopsError}\r\n              </div>\r\n            ) : sortedSOPs.length === 0 ? (\r\n              <div className=\"dropdown-message\">\r\n                No SOPs available. Please add SOPs first.\r\n              </div>\r\n            ) : (\r\n              <div className=\"dropdown-options\">\r\n                {/* Filter SOPs based on search term */}\r\n                {sortedSOPs\r\n                  .filter(sop => \r\n                    (sop.title || 'Unnamed SOP').toLowerCase().includes(sopSearchTerm.toLowerCase())\r\n                  )\r\n                  .map(sop => (\r\n                    <div\r\n                      key={sop.id}\r\n                      className={`sop-option ${formData.selectedSOP?.id === sop.id ? 'selected' : ''}`}\r\n                      onClick={() => handleSOPSelect(sop)}\r\n                    >\r\n                      <div className=\"sop-option-content\">\r\n                        <span className=\"sop-title\">{sanitizeText(sop.title || 'Unnamed SOP')}</span>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                \r\n                {/* Show message when no SOPs match the search */}\r\n                {sortedSOPs.filter(sop => \r\n                  (sop.title || 'Unnamed SOP').toLowerCase().includes(sopSearchTerm.toLowerCase())\r\n                ).length === 0 && (\r\n                  <div className=\"dropdown-message\">\r\n                    No SOPs match your search.\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n  \r\n  // Update the regulation dropdown to include search functionality\r\n  const renderRegulationDropdown = () => {\r\n    return (\r\n      <div className=\"regulation-selector\" ref={regulationDropdownRef}>\r\n        <div \r\n          className=\"selected-sop-display\"\r\n          onClick={() => setShowRegulationDropdown(!showRegulationDropdown)}\r\n        >\r\n          {regulationsLoading ? (\r\n            <div className=\"dropdown-loading\">\r\n              <LoadingSpinner size=\"small\" />\r\n              <span>Loading requirements...</span>\r\n            </div>\r\n          ) : formData.selectedComplianceRequirements && formData.selectedComplianceRequirements.length > 0 ? (\r\n            <div className=\"selected-sops-tags\">\r\n              {formData.selectedComplianceRequirements.map(req => (\r\n                <div key={req.id} className=\"sop-tag\">\r\n                  {req.name}\r\n                  <button \r\n                    className=\"remove-tag\"\r\n                    onClick={(e) => {\r\n                      e.stopPropagation();\r\n                      setFormData(prev => ({\r\n                        ...prev,\r\n                        selectedComplianceRequirements: prev.selectedComplianceRequirements.filter(r => r.id !== req.id)\r\n                      }));\r\n                    }}\r\n                  >\r\n                    ×\r\n                  </button>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          ) : (\r\n            <span className=\"placeholder\">Select compliance requirements</span>\r\n          )}\r\n          <span className=\"dropdown-arrow\">▼</span>\r\n        </div>\r\n        \r\n        {showRegulationDropdown && !regulationsLoading && (\r\n          <div className=\"dropdown-menu\">\r\n            {/* Add search input */}\r\n            <div className=\"dropdown-search\">\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Search compliance...\"\r\n                value={regulationSearchTerm}\r\n                onChange={(e) => setRegulationSearchTerm(e.target.value)}\r\n                onClick={(e) => e.stopPropagation()}\r\n                autoFocus\r\n              />\r\n            </div>\r\n            \r\n            {regulations.length === 0 ? (\r\n              <div className=\"dropdown-message\">\r\n                No compliance requirements available.\r\n              </div>\r\n            ) : (\r\n              <div className=\"dropdown-options\">\r\n                {/* Filter regulations based on search term */}\r\n                {regulations\r\n                  .filter(reg => \r\n                    (reg.name || '').toLowerCase().includes(regulationSearchTerm.toLowerCase())                  )\r\n                  .map(reg => {\r\n                    const isSelected = formData.selectedComplianceRequirements.some(r => r.id === reg.id);\r\n                    \r\n                    return (\r\n                      <div\r\n                        key={reg.id}\r\n                        className={`sop-option ${isSelected ? 'selected' : ''}`}\r\n                        onClick={() => handleRegulationToggle(reg)}\r\n                      >\r\n                        <div className=\"sop-option-content\">\r\n                          <div className=\"checkbox\">\r\n                            {isSelected && '✓'}\r\n                          </div>\r\n                          <span className=\"sop-title\">{reg.name || 'Unnamed Requirement'}</span>\r\n                        </div>\r\n                      </div>\r\n                    );\r\n                  })}\r\n                \r\n                {/* Show message when no regulations match the search */}\r\n                {regulations.filter(reg => \r\n                  (reg.name || '').toLowerCase().includes(regulationSearchTerm.toLowerCase()) \r\n                ).length === 0 && (\r\n                  <div className=\"dropdown-message\">\r\n                    No requirements match your search.\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n  \r\n  // Add useEffect to handle dynamic spacing when dropdowns open/close\r\n  useEffect(() => {\r\n    const updateDropdownSpacing = () => {\r\n      if (showSOPDropdown && sopDropdownRef.current) {\r\n        const dropdownMenu = sopDropdownRef.current.querySelector('.dropdown-menu');\r\n        if (dropdownMenu) {\r\n          const dropdownHeight = dropdownMenu.offsetHeight;\r\n          sopDropdownRef.current.style.marginBottom = `${dropdownHeight + 20}px`;\r\n        }\r\n      } else if (sopDropdownRef.current) {\r\n        sopDropdownRef.current.style.marginBottom = '0px';\r\n      }\r\n    };\r\n\r\n    // Small delay to ensure DOM is updated\r\n    const timeoutId = setTimeout(updateDropdownSpacing, 10);\r\n    return () => clearTimeout(timeoutId);\r\n  }, [showSOPDropdown, sops.length, sopSearchTerm]);\r\n\r\n  useEffect(() => {\r\n    const updateDropdownSpacing = () => {\r\n      if (showRegulationDropdown && regulationDropdownRef.current) {\r\n        const dropdownMenu = regulationDropdownRef.current.querySelector('.dropdown-menu');\r\n        if (dropdownMenu) {\r\n          const dropdownHeight = dropdownMenu.offsetHeight;\r\n          regulationDropdownRef.current.style.marginBottom = `${dropdownHeight + 20}px`;\r\n        }\r\n      } else if (regulationDropdownRef.current) {\r\n        regulationDropdownRef.current.style.marginBottom = '0px';\r\n      }\r\n    };\r\n\r\n    // Small delay to ensure DOM is updated\r\n    const timeoutId = setTimeout(updateDropdownSpacing, 10);\r\n    return () => clearTimeout(timeoutId);\r\n  }, [showRegulationDropdown, regulations.length, regulationSearchTerm]);\r\n  \r\n  return (\r\n    <div className=\"assessment-form\">\r\n      <h2>Create Gap Assessment</h2>\r\n      \r\n      <form onSubmit={handleSubmit}>\r\n        {/* <div className=\"form-group\">\r\n          <label htmlFor=\"title\">Assessment Title</label>\r\n          <input \r\n            type=\"text\" \r\n            id=\"title\" \r\n            name=\"title\" \r\n            value={formData.title} \r\n            onChange={handleInputChange} \r\n            placeholder=\"Enter assessment title\"\r\n          />\r\n        </div>\r\n        \r\n        <div className=\"form-group\">\r\n          <label htmlFor=\"description\">Description</label>\r\n          <textarea \r\n            id=\"description\" \r\n            name=\"description\" \r\n            value={formData.description} \r\n            onChange={handleInputChange} \r\n            placeholder=\"Enter assessment description\"\r\n          />\r\n        </div> */}\r\n        \r\n        {/* <div className=\"form-row\"> */}\r\n          <div className=\"form-group\">\r\n            <label>Select SOPs for assessment</label>\r\n            {renderSOPDropdown()}\r\n          </div>\r\n          \r\n          {/* <div className=\"form-group\">\r\n            <label>Department</label>\r\n            <div className=\"department-selector\" ref={departmentDropdownRef}>\r\n              <div \r\n                className=\"selected-sop-display\" \r\n                onClick={() => setShowDepartmentDropdown(!showDepartmentDropdown)}\r\n                aria-expanded={showDepartmentDropdown}\r\n              >\r\n                {!formData.selectedDepartment ? (\r\n                  <span className=\"placeholder\">Select Department</span>\r\n                ) : (\r\n                  <span className=\"selected-value\">\r\n                    {formData.selectedDepartment.name || 'Selected Department'}\r\n                  </span>\r\n                )}\r\n                <span className=\"dropdown-arrow\">▼</span>\r\n              </div>\r\n              \r\n              {showDepartmentDropdown && (\r\n                <div className=\"dropdown-menu\">\r\n                  <input \r\n                    type=\"text\" \r\n                    placeholder=\"Search departments...\" \r\n                    value={searchTerm}\r\n                    onChange={handleSearchChange}\r\n                    onClick={(e) => e.stopPropagation()}\r\n                  />\r\n                  \r\n                  <div className=\"dropdown-options\">\r\n                    {departments.map(department => (\r\n                      <div \r\n                        key={department.id} \r\n                        className={`dropdown-option ${formData.selectedDepartment && formData.selectedDepartment.id === department.id ? 'selected' : ''}`}\r\n                        onClick={() => selectDepartment(department)}\r\n                      >\r\n                        {department.name || 'Unnamed Department'}\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div> */}\r\n        {/* </div> */}\r\n        \r\n        <div className=\"form-group\">\r\n          <label>Compliance Requirements</label>\r\n          {renderRegulationDropdown()}\r\n        </div>\r\n        \r\n        {error && <div className=\"error-message\">{error}</div>}\r\n        \r\n        <div className=\"assessment-form-actions\">\r\n          <button \r\n            type=\"button\" \r\n            className=\"btn-cancel\" \r\n            onClick={onClose}\r\n            disabled={loading}\r\n          >\r\n            Cancel\r\n          </button>\r\n          <button \r\n            type=\"submit\" \r\n            className={`create-assessment-btn ${loading ? 'creating' : ''} ${!isFormValid() ? 'disabled' : ''}`}\r\n            disabled={loading || !isFormValid()}\r\n            title={!isFormValid() ? 'Please select a SOP and at least one compliance requirement' : ''}\r\n          >\r\n            {loading ? (\r\n              <>\r\n                <LoadingSpinner size=\"small\" />\r\n                <span>Creating...</span>\r\n              </>\r\n            ) : 'Create Assessment'}\r\n          </button>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AssessmentForm; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,UAAU,MAAM,8BAA8B;AACrD,OAAOC,iBAAiB,MAAM,qCAAqC;AACnE,OAAOC,iBAAiB,MAAM,qCAAqC;AACnE,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,cAAc,MAAM,4CAA4C;AACvE,OAAO,sBAAsB;AAC7B,SAASC,YAAY,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvD,MAAMC,cAAc,GAAGA,CAAC;EAAEC,OAAO;EAAEC,QAAQ;EAAEC,SAAS;EAAEC,iBAAiB;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACvF,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4B,eAAe,EAAEC,kBAAkB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC8B,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACgC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAMwC,cAAc,GAAGtC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMuC,qBAAqB,GAAGvC,MAAM,CAAC,IAAI,CAAC;EAC1C,MAAMwC,qBAAqB,GAAGxC,MAAM,CAAC,IAAI,CAAC;EAE1C,MAAM,CAACyC,QAAQ,EAAEC,WAAW,CAAC,GAAG5C,QAAQ,CAAC;IACvC6C,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,kBAAkB,EAAE,IAAI;IACxBC,QAAQ,EAAE,EAAE;IACZC,8BAA8B,EAAE,EAAE;IAClCC,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACqD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACuD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;;EAElE;EACA,MAAM,CAACyD,aAAa,EAAEC,gBAAgB,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC2D,SAAS,EAAEC,YAAY,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC6D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC+D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM,CAACiE,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;;EAEpE;EACAC,SAAS,CAAC,MAAM;IACd;IACAkE,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;;IAEvC;IACA,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArE,SAAS,CAAC,MAAM;IACd,MAAMsE,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFnB,cAAc,CAAC,IAAI,CAAC;QACpBQ,YAAY,CAAC,IAAI,CAAC;QAClB,MAAMY,OAAO,GAAG,MAAMrE,UAAU,CAACsE,UAAU,CAAC,CAAC;QAC7ClD,OAAO,CAACiD,OAAO,CAACE,SAAS,IAAI,EAAE,CAAC;MAClC,CAAC,CAAC,OAAOC,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,kBAAA;QACZC,OAAO,CAACxC,KAAK,CAAC,sBAAsB,EAAEqC,GAAG,CAAC;QAE1C,IAAII,YAAY,GAAG,8CAA8C;QACjE,IAAIJ,GAAG,aAAHA,GAAG,gBAAAC,aAAA,GAAHD,GAAG,CAAEK,QAAQ,cAAAJ,aAAA,gBAAAC,kBAAA,GAAbD,aAAA,CAAeK,IAAI,cAAAJ,kBAAA,eAAnBA,kBAAA,CAAqBK,MAAM,EAAE;UAAA,IAAAC,cAAA,EAAAC,mBAAA;UAC/BL,YAAY,GAAGJ,GAAG,aAAHA,GAAG,wBAAAQ,cAAA,GAAHR,GAAG,CAAEK,QAAQ,cAAAG,cAAA,wBAAAC,mBAAA,GAAbD,cAAA,CAAeF,IAAI,cAAAG,mBAAA,uBAAnBA,mBAAA,CAAqBF,MAAM;QAC5C;QAEAtB,YAAY,CAACmB,YAAY,CAAC;MAC5B,CAAC,SAAS;QACR3B,cAAc,CAAC,KAAK,CAAC;MACvB;IACF,CAAC;IAED,MAAMiC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,MAAMC,cAAc,GAAG,MAAMjF,iBAAiB,CAACkF,iBAAiB,CAAC,CAAC;QAClE5D,cAAc,CAAC2D,cAAc,CAAC;MAChC,CAAC,CAAC,OAAOX,GAAG,EAAE;QAAA,IAAAa,cAAA,EAAAC,mBAAA;QACZX,OAAO,CAACxC,KAAK,CAAC,6BAA6B,EAAEqC,GAAG,CAAC;QAEjD,IAAII,YAAY,GAAG,qDAAqD;QACxE,IAAIJ,GAAG,aAAHA,GAAG,gBAAAa,cAAA,GAAHb,GAAG,CAAEK,QAAQ,cAAAQ,cAAA,gBAAAC,mBAAA,GAAbD,cAAA,CAAeP,IAAI,cAAAQ,mBAAA,eAAnBA,mBAAA,CAAqBP,MAAM,EAAE;UAAA,IAAAQ,cAAA,EAAAC,mBAAA;UAC/BZ,YAAY,GAAGJ,GAAG,aAAHA,GAAG,wBAAAe,cAAA,GAAHf,GAAG,CAAEK,QAAQ,cAAAU,cAAA,wBAAAC,mBAAA,GAAbD,cAAA,CAAeT,IAAI,cAAAU,mBAAA,uBAAnBA,mBAAA,CAAqBT,MAAM;QAC5C;QAEApB,mBAAmB,CAACiB,YAAY,CAAC;MACnC,CAAC,SAAS;QACRvB,qBAAqB,CAAC,KAAK,CAAC;MAC9B;IACF,CAAC;IAED,MAAMoC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACFtC,qBAAqB,CAAC,IAAI,CAAC;QAC3B,MAAMuC,cAAc,GAAG,MAAMzF,iBAAiB,CAAC0F,0BAA0B,CAAC,CAAC;QAC3E,IAAIC,sBAAsB,GAAGF,cAAc,CAACG,GAAG,CAACC,UAAU,IAAIA,UAAU,CAACC,kBAAkB,CAAC;QAC5FzE,cAAc,CAACsE,sBAAsB,CAAC;MACxC,CAAC,CAAC,OAAOpB,GAAG,EAAE;QAAA,IAAAwB,cAAA,EAAAC,mBAAA;QACZtB,OAAO,CAACxC,KAAK,CAAC,6BAA6B,EAAEqC,GAAG,CAAC;QAEjD,IAAII,YAAY,GAAG,iEAAiE;QACpF,IAAIJ,GAAG,aAAHA,GAAG,gBAAAwB,cAAA,GAAHxB,GAAG,CAAEK,QAAQ,cAAAmB,cAAA,gBAAAC,mBAAA,GAAbD,cAAA,CAAelB,IAAI,cAAAmB,mBAAA,eAAnBA,mBAAA,CAAqBlB,MAAM,EAAE;UAAA,IAAAmB,cAAA,EAAAC,mBAAA;UAC/BvB,YAAY,GAAGJ,GAAG,aAAHA,GAAG,wBAAA0B,cAAA,GAAH1B,GAAG,CAAEK,QAAQ,cAAAqB,cAAA,wBAAAC,mBAAA,GAAbD,cAAA,CAAepB,IAAI,cAAAqB,mBAAA,uBAAnBA,mBAAA,CAAqBpB,MAAM;QAC5C;QAEAlB,mBAAmB,CAACe,YAAY,CAAC;MACnC,CAAC,SAAS;QACRzB,qBAAqB,CAAC,KAAK,CAAC;MAC9B;IACF,CAAC;IAEDiB,SAAS,CAAC,CAAC;IACXc,gBAAgB,CAAC,CAAC;IAClBO,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3F,SAAS,CAAC,MAAM;IACd,MAAMsG,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIhE,cAAc,CAACiE,OAAO,IAAI,CAACjE,cAAc,CAACiE,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QAC5E9E,kBAAkB,CAAC,KAAK,CAAC;MAC3B;MACA,IAAIY,qBAAqB,CAACgE,OAAO,IAAI,CAAChE,qBAAqB,CAACgE,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QAC1F5E,yBAAyB,CAAC,KAAK,CAAC;MAClC;MACA,IAAIW,qBAAqB,CAAC+D,OAAO,IAAI,CAAC/D,qBAAqB,CAAC+D,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QAC1F1E,yBAAyB,CAAC,KAAK,CAAC;MAClC;IACF,CAAC;IAEDkC,QAAQ,CAACyC,gBAAgB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACXpC,QAAQ,CAAC0C,mBAAmB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACJ,MAAM;IAChC/D,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACqE,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,eAAe,GAAIC,GAAG,IAAK;IAC/BvE,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXO,WAAW,EAAEiE;IACf,CAAC,CAAC;IACFtF,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMuF,gBAAgB,GAAIC,UAAU,IAAK;IACvCzE,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXI,kBAAkB,EAAEsE;IACtB,CAAC,CAAC;IACFtF,yBAAyB,CAAC,KAAK,CAAC;EAClC,CAAC;;EAED;EACA,MAAMuF,sBAAsB,GAAIrB,UAAU,IAAK;IAC7CrD,WAAW,CAAC2E,IAAI,IAAI;MAClB,MAAMC,iBAAiB,GAAGD,IAAI,CAACtE,8BAA8B,CAACwE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAK1B,UAAU,CAAC0B,EAAE,CAAC;MAE/F,IAAIH,iBAAiB,EAAE;QACrB;QACA,OAAO;UACL,GAAGD,IAAI;UACPtE,8BAA8B,EAAEsE,IAAI,CAACtE,8BAA8B,CAAC2E,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAK1B,UAAU,CAAC0B,EAAE;QACxG,CAAC;MACH,CAAC,MAAM;QACL;QACA,OAAO;UACL,GAAGJ,IAAI;UACPtE,8BAA8B,EAAE,CAAC,GAAGsE,IAAI,CAACtE,8BAA8B,EAAEgD,UAAU;QACrF,CAAC;MACH;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM4B,kBAAkB,GAAId,CAAC,IAAK;IAChC5E,aAAa,CAAC4E,CAAC,CAACJ,MAAM,CAACM,KAAK,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMa,WAAW,GAAGA,CAAA,KAAM;IACxB,OACEnF,QAAQ,CAACO,WAAW;IAAI;IACxBP,QAAQ,CAACM,8BAA8B,IACvCN,QAAQ,CAACM,8BAA8B,CAAC8E,MAAM,GAAG,CAAC,CAAC;IAAA;EAEvD,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGxG,WAAW,CAACoG,MAAM,CAAC3B,UAAU,IACvDA,UAAU,CAACe,IAAI,CAACiB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChG,UAAU,CAAC+F,WAAW,CAAC,CAAC,CACjE,CAAC;;EAED;EACA,MAAME,YAAY,GAAG,MAAOpB,CAAC,IAAK;IAChCA,CAAC,CAACqB,cAAc,CAAC,CAAC;IAElB,IAAIhG,OAAO,EAAE;;IAEb;IACA,IAAI,CAACO,QAAQ,CAACO,WAAW,EAAE;MACzB1C,KAAK,CAAC8B,KAAK,CAAC,oCAAoC,EAAE;QAChD+F,OAAO,EAAE,gBAAgBC,IAAI,CAACC,GAAG,CAAC,CAAC;MACrC,CAAC,CAAC;MACF;IACF;IAEA,IAAI,CAAC5F,QAAQ,CAACM,8BAA8B,IAAIN,QAAQ,CAACM,8BAA8B,CAAC8E,MAAM,KAAK,CAAC,EAAE;MACpGvH,KAAK,CAAC8B,KAAK,CAAC,mDAAmD,EAAE;QAC/D+F,OAAO,EAAE,uBAAuBC,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5C,CAAC,CAAC;MACF;IACF;IAEA,IAAI;MAAA,IAAAC,qBAAA;MACFnG,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMkG,iBAAiB,GAAG;QACxB,GAAG9F,QAAQ;QACXE,KAAK,EAAEnC,YAAY,CAACiC,QAAQ,CAACE,KAAK,CAAC;QACnCwE,UAAU,EAAE3G,YAAY,EAAA8H,qBAAA,GAAC7F,QAAQ,CAACI,kBAAkB,cAAAyF,qBAAA,uBAA3BA,qBAAA,CAA6BxB,IAAI,CAAC;QAC3DlE,WAAW,EAAEpC,YAAY,CAACiC,QAAQ,CAACG,WAAW;QAC9C;MACF,CAAC;MAED,MAAM4F,aAAa,GAAGD,iBAAiB,CAACxF,8BAA8B,CAAC+C,GAAG,CAAC2C,GAAG,IAAIA,GAAG,CAAChB,EAAE,CAAC;;MAEzF;MACA,IAAI,OAAOxG,iBAAiB,KAAK,UAAU,EAAE;QAC3CA,iBAAiB,CAACsH,iBAAiB,CAACvF,WAAW,CAACyE,EAAE,CAAC;MACrD;;MAEA;MACA,MAAM3C,QAAQ,GAAG,MAAM1E,UAAU,CAACsI,IAAI,CACpC,GAAGrI,QAAQ,CAACsI,QAAQ,CAACC,OAAO,IAAIL,iBAAiB,CAACvF,WAAW,CAACyE,EAAE,EAAE,EAClEe,aACF,CAAC;MAED5D,OAAO,CAACiE,GAAG,CAAC,0BAA0B,EAAE/D,QAAQ,CAAC;;MAEjD;MACAxE,KAAK,CAACwI,OAAO,CAAC,iCAAiC,EAAE;QAC/CX,OAAO,EAAE,sBAAsBC,IAAI,CAACC,GAAG,CAAC,CAAC;MAC3C,CAAC,CAAC;;MAEF;MACA,IAAI,OAAOrH,SAAS,KAAK,UAAU,EAAE;QACnCA,SAAS,CAAC,CAAC;MACb,CAAC,MAAM,IAAI,OAAOD,QAAQ,KAAK,UAAU,EAAE;QACzCA,QAAQ,CAAC,CAAC;MACZ;IAEF,CAAC,CAAC,OAAO0D,GAAG,EAAE;MAAA,IAAAsE,cAAA,EAAAC,mBAAA;MACZpE,OAAO,CAACxC,KAAK,CAAC,4BAA4B,EAAEqC,GAAG,CAAC;MAEhD,IAAII,YAAY,GAAG,sDAAsD;MACzE,IAAIJ,GAAG,aAAHA,GAAG,gBAAAsE,cAAA,GAAHtE,GAAG,CAAEK,QAAQ,cAAAiE,cAAA,gBAAAC,mBAAA,GAAbD,cAAA,CAAehE,IAAI,cAAAiE,mBAAA,eAAnBA,mBAAA,CAAqBhE,MAAM,EAAE;QAAA,IAAAiE,cAAA,EAAAC,mBAAA;QAC/BrE,YAAY,GAAGJ,GAAG,aAAHA,GAAG,wBAAAwE,cAAA,GAAHxE,GAAG,CAAEK,QAAQ,cAAAmE,cAAA,wBAAAC,mBAAA,GAAbD,cAAA,CAAelE,IAAI,cAAAmE,mBAAA,uBAAnBA,mBAAA,CAAqBlE,MAAM;MAC5C;MAEA3C,QAAQ,CAACwC,YAAY,CAAC;MAEtBvE,KAAK,CAAC8B,KAAK,CAACyC,YAAY,EAAE;QACxBsD,OAAO,EAAE,oBAAoBC,IAAI,CAACC,GAAG,CAAC,CAAC;MACzC,CAAC,CAAC;MAEF,IAAI,OAAOnH,OAAO,KAAK,UAAU,EAAE;QACjCA,OAAO,CAAC2D,YAAY,CAAC;MACvB;IACF,CAAC,SAAS;MACR1C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgH,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACA,MAAMC,UAAU,GAAG,CAAC,GAAGhI,IAAI,CAAC,CAACiI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC1C;MACA,MAAMC,KAAK,GAAG,IAAIpB,IAAI,CAACkB,CAAC,CAACG,UAAU,IAAIH,CAAC,CAACI,UAAU,IAAItB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;MAClE,MAAMsB,KAAK,GAAG,IAAIvB,IAAI,CAACmB,CAAC,CAACE,UAAU,IAAIF,CAAC,CAACG,UAAU,IAAItB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;MAClE,OAAOsB,KAAK,GAAGH,KAAK,CAAC,CAAC;IACxB,CAAC,CAAC;IAEF,oBACE9I,OAAA;MAAKkJ,SAAS,EAAC,cAAc;MAACC,GAAG,EAAEvH,cAAe;MAAAwH,QAAA,gBAChDpJ,OAAA;QACEkJ,SAAS,EAAC,sBAAsB;QAChCG,OAAO,EAAEA,CAAA,KAAMpI,kBAAkB,CAAC,CAACD,eAAe,CAAE;QACpD,iBAAeA,eAAgB;QAAAoI,QAAA,GAE9B7G,WAAW,gBACVvC,OAAA;UAAKkJ,SAAS,EAAC,kBAAkB;UAAAE,QAAA,gBAC/BpJ,OAAA,CAACH,cAAc;YAACyJ,IAAI,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/B1J,OAAA;YAAAoJ,QAAA,EAAM;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,GACJ3H,QAAQ,CAACO,WAAW,gBACtBtC,OAAA;UAAKkJ,SAAS,EAAC,cAAc;UAAAE,QAAA,gBAC3BpJ,OAAA;YAAAoJ,QAAA,EAAOtJ,YAAY,CAACiC,QAAQ,CAACO,WAAW,CAACL,KAAK,IAAI,aAAa;UAAC;YAAAsH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxE1J,OAAA;YACEkJ,SAAS,EAAC,WAAW;YACrBG,OAAO,EAAGlD,CAAC,IAAK;cACdA,CAAC,CAACwD,eAAe,CAAC,CAAC;cACnB3H,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEO,WAAW,EAAE;cAAK,CAAC,CAAC;YACjD,CAAE;YAAA8G,QAAA,EACH;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAEN1J,OAAA;UAAMkJ,SAAS,EAAC,aAAa;UAAAE,QAAA,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACjD,eACD1J,OAAA;UAAMkJ,SAAS,EAAC,gBAAgB;UAAAE,QAAA,EAAC;QAAC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,EAEL1I,eAAe,IAAI,CAACuB,WAAW,iBAC9BvC,OAAA;QAAKkJ,SAAS,EAAC,eAAe;QAAAE,QAAA,gBAE5BpJ,OAAA;UAAKkJ,SAAS,EAAC,iBAAiB;UAAAE,QAAA,eAC9BpJ,OAAA;YACE4J,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,gBAAgB;YAC5BxD,KAAK,EAAExD,aAAc;YACrBiH,QAAQ,EAAG3D,CAAC,IAAKrD,gBAAgB,CAACqD,CAAC,CAACJ,MAAM,CAACM,KAAK,CAAE;YAClDgD,OAAO,EAAGlD,CAAC,IAAKA,CAAC,CAACwD,eAAe,CAAC,CAAE;YACpCI,SAAS;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAEL3G,SAAS,gBACR/C,OAAA;UAAKkJ,SAAS,EAAC,wBAAwB;UAAAE,QAAA,EACpCrG;QAAS;UAAAwG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,GACJhB,UAAU,CAACvB,MAAM,KAAK,CAAC,gBACzBnH,OAAA;UAAKkJ,SAAS,EAAC,kBAAkB;UAAAE,QAAA,EAAC;QAElC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,gBAEN1J,OAAA;UAAKkJ,SAAS,EAAC,kBAAkB;UAAAE,QAAA,GAE9BV,UAAU,CACR1B,MAAM,CAACT,GAAG,IACT,CAACA,GAAG,CAACtE,KAAK,IAAI,aAAa,EAAEoF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzE,aAAa,CAACwE,WAAW,CAAC,CAAC,CACjF,CAAC,CACAjC,GAAG,CAACmB,GAAG;YAAA,IAAAyD,qBAAA;YAAA,oBACNhK,OAAA;cAEEkJ,SAAS,EAAE,cAAc,EAAAc,qBAAA,GAAAjI,QAAQ,CAACO,WAAW,cAAA0H,qBAAA,uBAApBA,qBAAA,CAAsBjD,EAAE,MAAKR,GAAG,CAACQ,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;cACjFsC,OAAO,EAAEA,CAAA,KAAM/C,eAAe,CAACC,GAAG,CAAE;cAAA6C,QAAA,eAEpCpJ,OAAA;gBAAKkJ,SAAS,EAAC,oBAAoB;gBAAAE,QAAA,eACjCpJ,OAAA;kBAAMkJ,SAAS,EAAC,WAAW;kBAAAE,QAAA,EAAEtJ,YAAY,CAACyG,GAAG,CAACtE,KAAK,IAAI,aAAa;gBAAC;kBAAAsH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E;YAAC,GANDnD,GAAG,CAACQ,EAAE;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOR,CAAC;UAAA,CACP,CAAC,EAGHhB,UAAU,CAAC1B,MAAM,CAACT,GAAG,IACpB,CAACA,GAAG,CAACtE,KAAK,IAAI,aAAa,EAAEoF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzE,aAAa,CAACwE,WAAW,CAAC,CAAC,CACjF,CAAC,CAACF,MAAM,KAAK,CAAC,iBACZnH,OAAA;YAAKkJ,SAAS,EAAC,kBAAkB;YAAAE,QAAA,EAAC;UAElC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;;EAED;EACA,MAAMO,wBAAwB,GAAGA,CAAA,KAAM;IACrC,oBACEjK,OAAA;MAAKkJ,SAAS,EAAC,qBAAqB;MAACC,GAAG,EAAErH,qBAAsB;MAAAsH,QAAA,gBAC9DpJ,OAAA;QACEkJ,SAAS,EAAC,sBAAsB;QAChCG,OAAO,EAAEA,CAAA,KAAMhI,yBAAyB,CAAC,CAACD,sBAAsB,CAAE;QAAAgI,QAAA,GAEjE3G,kBAAkB,gBACjBzC,OAAA;UAAKkJ,SAAS,EAAC,kBAAkB;UAAAE,QAAA,gBAC/BpJ,OAAA,CAACH,cAAc;YAACyJ,IAAI,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/B1J,OAAA;YAAAoJ,QAAA,EAAM;UAAuB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,GACJ3H,QAAQ,CAACM,8BAA8B,IAAIN,QAAQ,CAACM,8BAA8B,CAAC8E,MAAM,GAAG,CAAC,gBAC/FnH,OAAA;UAAKkJ,SAAS,EAAC,oBAAoB;UAAAE,QAAA,EAChCrH,QAAQ,CAACM,8BAA8B,CAAC+C,GAAG,CAAC2C,GAAG,iBAC9C/H,OAAA;YAAkBkJ,SAAS,EAAC,SAAS;YAAAE,QAAA,GAClCrB,GAAG,CAAC3B,IAAI,eACTpG,OAAA;cACEkJ,SAAS,EAAC,YAAY;cACtBG,OAAO,EAAGlD,CAAC,IAAK;gBACdA,CAAC,CAACwD,eAAe,CAAC,CAAC;gBACnB3H,WAAW,CAAC2E,IAAI,KAAK;kBACnB,GAAGA,IAAI;kBACPtE,8BAA8B,EAAEsE,IAAI,CAACtE,8BAA8B,CAAC2E,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKgB,GAAG,CAAChB,EAAE;gBACjG,CAAC,CAAC,CAAC;cACL,CAAE;cAAAqC,QAAA,EACH;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,GAbD3B,GAAG,CAAChB,EAAE;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcX,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAEN1J,OAAA;UAAMkJ,SAAS,EAAC,aAAa;UAAAE,QAAA,EAAC;QAA8B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACnE,eACD1J,OAAA;UAAMkJ,SAAS,EAAC,gBAAgB;UAAAE,QAAA,EAAC;QAAC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,EAELtI,sBAAsB,IAAI,CAACqB,kBAAkB,iBAC5CzC,OAAA;QAAKkJ,SAAS,EAAC,eAAe;QAAAE,QAAA,gBAE5BpJ,OAAA;UAAKkJ,SAAS,EAAC,iBAAiB;UAAAE,QAAA,eAC9BpJ,OAAA;YACE4J,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,sBAAsB;YAClCxD,KAAK,EAAEhD,oBAAqB;YAC5ByG,QAAQ,EAAG3D,CAAC,IAAK7C,uBAAuB,CAAC6C,CAAC,CAACJ,MAAM,CAACM,KAAK,CAAE;YACzDgD,OAAO,EAAGlD,CAAC,IAAKA,CAAC,CAACwD,eAAe,CAAC,CAAE;YACpCI,SAAS;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAEL9I,WAAW,CAACuG,MAAM,KAAK,CAAC,gBACvBnH,OAAA;UAAKkJ,SAAS,EAAC,kBAAkB;UAAAE,QAAA,EAAC;QAElC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,gBAEN1J,OAAA;UAAKkJ,SAAS,EAAC,kBAAkB;UAAAE,QAAA,GAE9BxI,WAAW,CACToG,MAAM,CAACkD,GAAG,IACT,CAACA,GAAG,CAAC9D,IAAI,IAAI,EAAE,EAAEiB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjE,oBAAoB,CAACgE,WAAW,CAAC,CAAC,CAAmB,CAAC,CAC/FjC,GAAG,CAAC8E,GAAG,IAAI;YACV,MAAMC,UAAU,GAAGpI,QAAQ,CAACM,8BAA8B,CAACwE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKmD,GAAG,CAACnD,EAAE,CAAC;YAErF,oBACE/G,OAAA;cAEEkJ,SAAS,EAAE,cAAciB,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;cACxDd,OAAO,EAAEA,CAAA,KAAM3C,sBAAsB,CAACwD,GAAG,CAAE;cAAAd,QAAA,eAE3CpJ,OAAA;gBAAKkJ,SAAS,EAAC,oBAAoB;gBAAAE,QAAA,gBACjCpJ,OAAA;kBAAKkJ,SAAS,EAAC,UAAU;kBAAAE,QAAA,EACtBe,UAAU,IAAI;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACN1J,OAAA;kBAAMkJ,SAAS,EAAC,WAAW;kBAAAE,QAAA,EAAEc,GAAG,CAAC9D,IAAI,IAAI;gBAAqB;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE;YAAC,GATDQ,GAAG,CAACnD,EAAE;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUR,CAAC;UAEV,CAAC,CAAC,EAGH9I,WAAW,CAACoG,MAAM,CAACkD,GAAG,IACrB,CAACA,GAAG,CAAC9D,IAAI,IAAI,EAAE,EAAEiB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjE,oBAAoB,CAACgE,WAAW,CAAC,CAAC,CAC5E,CAAC,CAACF,MAAM,KAAK,CAAC,iBACZnH,OAAA;YAAKkJ,SAAS,EAAC,kBAAkB;YAAAE,QAAA,EAAC;UAElC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;;EAED;EACArK,SAAS,CAAC,MAAM;IACd,MAAM+K,qBAAqB,GAAGA,CAAA,KAAM;MAClC,IAAIpJ,eAAe,IAAIY,cAAc,CAACiE,OAAO,EAAE;QAC7C,MAAMwE,YAAY,GAAGzI,cAAc,CAACiE,OAAO,CAACyE,aAAa,CAAC,gBAAgB,CAAC;QAC3E,IAAID,YAAY,EAAE;UAChB,MAAME,cAAc,GAAGF,YAAY,CAACG,YAAY;UAChD5I,cAAc,CAACiE,OAAO,CAACpC,KAAK,CAACgH,YAAY,GAAG,GAAGF,cAAc,GAAG,EAAE,IAAI;QACxE;MACF,CAAC,MAAM,IAAI3I,cAAc,CAACiE,OAAO,EAAE;QACjCjE,cAAc,CAACiE,OAAO,CAACpC,KAAK,CAACgH,YAAY,GAAG,KAAK;MACnD;IACF,CAAC;;IAED;IACA,MAAMC,SAAS,GAAGC,UAAU,CAACP,qBAAqB,EAAE,EAAE,CAAC;IACvD,OAAO,MAAMQ,YAAY,CAACF,SAAS,CAAC;EACtC,CAAC,EAAE,CAAC1J,eAAe,EAAEN,IAAI,CAACyG,MAAM,EAAEtE,aAAa,CAAC,CAAC;EAEjDxD,SAAS,CAAC,MAAM;IACd,MAAM+K,qBAAqB,GAAGA,CAAA,KAAM;MAClC,IAAIhJ,sBAAsB,IAAIU,qBAAqB,CAAC+D,OAAO,EAAE;QAC3D,MAAMwE,YAAY,GAAGvI,qBAAqB,CAAC+D,OAAO,CAACyE,aAAa,CAAC,gBAAgB,CAAC;QAClF,IAAID,YAAY,EAAE;UAChB,MAAME,cAAc,GAAGF,YAAY,CAACG,YAAY;UAChD1I,qBAAqB,CAAC+D,OAAO,CAACpC,KAAK,CAACgH,YAAY,GAAG,GAAGF,cAAc,GAAG,EAAE,IAAI;QAC/E;MACF,CAAC,MAAM,IAAIzI,qBAAqB,CAAC+D,OAAO,EAAE;QACxC/D,qBAAqB,CAAC+D,OAAO,CAACpC,KAAK,CAACgH,YAAY,GAAG,KAAK;MAC1D;IACF,CAAC;;IAED;IACA,MAAMC,SAAS,GAAGC,UAAU,CAACP,qBAAqB,EAAE,EAAE,CAAC;IACvD,OAAO,MAAMQ,YAAY,CAACF,SAAS,CAAC;EACtC,CAAC,EAAE,CAACtJ,sBAAsB,EAAER,WAAW,CAACuG,MAAM,EAAE9D,oBAAoB,CAAC,CAAC;EAEtE,oBACErD,OAAA;IAAKkJ,SAAS,EAAC,iBAAiB;IAAAE,QAAA,gBAC9BpJ,OAAA;MAAAoJ,QAAA,EAAI;IAAqB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE9B1J,OAAA;MAAMK,QAAQ,EAAEkH,YAAa;MAAA6B,QAAA,gBAyBzBpJ,OAAA;QAAKkJ,SAAS,EAAC,YAAY;QAAAE,QAAA,gBACzBpJ,OAAA;UAAAoJ,QAAA,EAAO;QAA0B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACxCjB,iBAAiB,CAAC,CAAC;MAAA;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eA+CR1J,OAAA;QAAKkJ,SAAS,EAAC,YAAY;QAAAE,QAAA,gBACzBpJ,OAAA;UAAAoJ,QAAA,EAAO;QAAuB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACrCO,wBAAwB,CAAC,CAAC;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,EAELhI,KAAK,iBAAI1B,OAAA;QAAKkJ,SAAS,EAAC,eAAe;QAAAE,QAAA,EAAE1H;MAAK;QAAA6H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEtD1J,OAAA;QAAKkJ,SAAS,EAAC,yBAAyB;QAAAE,QAAA,gBACtCpJ,OAAA;UACE4J,IAAI,EAAC,QAAQ;UACbV,SAAS,EAAC,YAAY;UACtBG,OAAO,EAAEjJ,OAAQ;UACjByK,QAAQ,EAAErJ,OAAQ;UAAA4H,QAAA,EACnB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1J,OAAA;UACE4J,IAAI,EAAC,QAAQ;UACbV,SAAS,EAAE,yBAAyB1H,OAAO,GAAG,UAAU,GAAG,EAAE,IAAI,CAAC0F,WAAW,CAAC,CAAC,GAAG,UAAU,GAAG,EAAE,EAAG;UACpG2D,QAAQ,EAAErJ,OAAO,IAAI,CAAC0F,WAAW,CAAC,CAAE;UACpCjF,KAAK,EAAE,CAACiF,WAAW,CAAC,CAAC,GAAG,6DAA6D,GAAG,EAAG;UAAAkC,QAAA,EAE1F5H,OAAO,gBACNxB,OAAA,CAAAE,SAAA;YAAAkJ,QAAA,gBACEpJ,OAAA,CAACH,cAAc;cAACyJ,IAAI,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/B1J,OAAA;cAAAoJ,QAAA,EAAM;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACxB,CAAC,GACD;QAAmB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACjJ,EAAA,CArnBIN,cAAc;AAAA2K,EAAA,GAAd3K,cAAc;AAunBpB,eAAeA,cAAc;AAAC,IAAA2K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}