from openai import AzureOpenAI
from app.models.chatbot_models import Chat<PERSON>urn
from app.services.chatbot_services.custom_promt import COMP<PERSON><PERSON>NCE_PROMPT,CHAT_COMPLIANCE_PROMPT
from typing import List, Optional
from dotenv import load_dotenv
load_dotenv()
import logging
import os
import asyncio

logger = logging.getLogger(__name__)

# Environment variables at the top
AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY")
AZURE_OPENAI_API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION", "2023-12-01-preview")
AZURE_OPENAI_EMBEDDING_MODEL = os.getenv("AZURE_OPENAI_EMBEDDING_MODEL", "text-embedding-3-large")
AZURE_OPENAI_CHAT_MODEL = os.getenv("AZURE_OPENAI_CHAT_MODEL", "gpt-35-turbo")


def get_openai_client():
    """Initialize and return Azure OpenAI client"""
    
    try:
        if not AZURE_OPENAI_ENDPOINT:
            raise ValueError("AZURE_OPENAI_ENDPOINT environment variable is not set")
        if not AZURE_OPENAI_API_KEY:
            raise ValueError("AZURE_OPENAI_API_KEY is not set.")
        
         
        client = AzureOpenAI(
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            api_key=AZURE_OPENAI_API_KEY,
            api_version=AZURE_OPENAI_API_VERSION,
        )
        #logger.info("AzureOpenAI client created successfully")
        return client
        
    except Exception as e:
        logger.error(f"Failed to initialize OpenAI client: {str(e)}", exc_info=True)
        raise Exception(f"OpenAI client initialization failed: {str(e)}")

async def get_embeddings(text: str) -> List[float]:
    """Get embeddings for the given text using Azure OpenAI"""
    try:
        
        if not text or not text.strip():
            raise ValueError("Input text cannot be empty")

        
        client = get_openai_client()
        response = await asyncio.to_thread(
            client.embeddings.create,
            input=text,
            model=AZURE_OPENAI_EMBEDDING_MODEL
        )
        
        if not response.data or len(response.data) == 0:
            raise Exception("No embedding data received from OpenAI")
            
        embedding = response.data[0].embedding
        logger.info(f"Generated embedding of length: {len(embedding)}")
        return embedding

    except Exception as e:
        logger.error(f"Error getting embeddings: {str(e)}", exc_info=True)
        raise Exception(f"Embedding generation failed: {str(e)}")

async def generate_response(context: str, question: str, chat_history: Optional[List[ChatTurn]] = None) -> str:
    """
    Generate response using Azure OpenAI Chat Completions, including conversation history
    
    Args:
        context: Context information for the response
        question: User's question
        chat_history: Previous conversation history
        
    Returns:
        Generated response text
    """
    try:
        if not context or not question:
            raise ValueError("Context and question cannot be empty")
            
        client = get_openai_client()
        
        # Build prompt based on whether chat history exists
        if chat_history:
            history_str = "\n".join(
                [f"User: {turn.user_query}\nAssistant: {turn.bot_answer}" for turn in chat_history]
            )
            prompt = CHAT_COMPLIANCE_PROMPT.format(
                history=history_str, 
                context=context, 
                question=question
            )
            #logger.info(f"History: {history_str}")
        else:
            prompt = COMPLIANCE_PROMPT.format(
                context=context, 
                question=question
            )

        #logger.info(f"FINAL prompt: {prompt}")
        
        # Generate response from the LLM
        response = client.chat.completions.create(
            model=AZURE_OPENAI_CHAT_MODEL,
            messages=[
                {"role": "system", "content": "You are a helpful compliance assistant."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=400,
            temperature=0.1
        )
        
        if not response.choices or len(response.choices) == 0:
            raise Exception("No response generated from OpenAI")
            
        content = response.choices[0].message.content
        if not content:
            raise Exception("Received empty response from OpenAI")
            
        return content

    except Exception as e:
        logger.error(f"Error generating response: {str(e)}", exc_info=True)
        raise Exception(f"Response generation failed: {str(e)}")

class OpenAIService:
    """
    Legacy class wrapper for backward compatibility
    Delegates to the functional approach above
    """
    def __init__(self):
        pass
    
    async def get_embeddings(self, text: str) -> List[float]:
        return await get_embeddings(text)
    
    async def generate_response(self, context: str, question: str, chat_history: Optional[List[ChatTurn]] = None) -> str:
        return await generate_response(context, question, chat_history)