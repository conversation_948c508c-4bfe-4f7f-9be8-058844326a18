{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\zipp\\\\latent-frontend\\\\src\\\\routes\\\\index.js\";\nimport React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport Login from '../components/pages/Login/Login';\nimport Dashboard from '../components/pages/Dashboard/Dashboard';\nimport ProfileSettings from '../components/pages/ProfileSettings/ProfileSettings';\nimport SOPLibrary from '../components/pages/SOPLibrary/SOPLibrary';\nimport GapAnalysis from '../components/pages/GapAnalysis/GapAnalysis';\nimport ProtectedRoute from '../components/common/ProtectedRoute/ProtectedRoute';\nimport NotFound from '../components/NotFound';\nimport Layout from '../components/common/Layout/Layout';\nimport ChatPage from '../components/pages/ChatBot/ChatPage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst routes = user => [{\n  path: '/login',\n  element: user ? /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/dashboard\",\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 21\n  }, this) : /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 60\n  }, this)\n}, {\n  path: '/dashboard',\n  element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n    user: user,\n    children: /*#__PURE__*/_jsxDEV(Layout, {\n      children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 7\n  }, this)\n}, {\n  path: '/profile',\n  element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n    user: user,\n    children: /*#__PURE__*/_jsxDEV(Layout, {\n      children: /*#__PURE__*/_jsxDEV(ProfileSettings, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 7\n  }, this)\n}, {\n  path: '/sop-library',\n  element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n    user: user,\n    children: /*#__PURE__*/_jsxDEV(Layout, {\n      children: /*#__PURE__*/_jsxDEV(SOPLibrary, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 7\n  }, this)\n}, {\n  path: '/gap-analysis',\n  element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n    user: user,\n    children: /*#__PURE__*/_jsxDEV(Layout, {\n      children: /*#__PURE__*/_jsxDEV(GapAnalysis, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 7\n  }, this)\n}, {\n  path: '/chatbot',\n  element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n    user: user,\n    children: /*#__PURE__*/_jsxDEV(ChatPage, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 7\n  }, this)\n}, {\n  path: '/',\n  element: /*#__PURE__*/_jsxDEV(Navigate, {\n    to: user ? \"/dashboard\" : \"/login\",\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 14\n  }, this)\n}, {\n  path: '*',\n  element: /*#__PURE__*/_jsxDEV(NotFound, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 14\n  }, this)\n}];\nexport default routes;", "map": {"version": 3, "names": ["React", "Navigate", "<PERSON><PERSON>", "Dashboard", "ProfileSettings", "SOPLibrary", "GapAnalysis", "ProtectedRoute", "NotFound", "Layout", "ChatPage", "jsxDEV", "_jsxDEV", "routes", "user", "path", "element", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/routes/index.js"], "sourcesContent": ["import React from 'react';\r\nimport { Navigate } from 'react-router-dom';\r\nimport Login from '../components/pages/Login/Login';\r\nimport Dashboard from '../components/pages/Dashboard/Dashboard';\r\nimport ProfileSettings from '../components/pages/ProfileSettings/ProfileSettings';\r\nimport SOPLibrary from '../components/pages/SOPLibrary/SOPLibrary';\r\nimport GapAnalysis from '../components/pages/GapAnalysis/GapAnalysis';\r\nimport ProtectedRoute from '../components/common/ProtectedRoute/ProtectedRoute';\r\nimport NotFound from '../components/NotFound';\r\nimport Layout from '../components/common/Layout/Layout';\r\nimport ChatPage from '../components/pages/ChatBot/ChatPage';\r\nconst routes = (user) => [\r\n  {\r\n    path: '/login',\r\n    element: user ? <Navigate to=\"/dashboard\" replace /> : <Login />\r\n  },\r\n  {\r\n    path: '/dashboard',\r\n    element: (\r\n      <ProtectedRoute user={user}>\r\n        <Layout >\r\n          <Dashboard />\r\n        </Layout>\r\n      </ProtectedRoute>\r\n    )\r\n  },\r\n  {\r\n    path: '/profile',\r\n    element: (\r\n      <ProtectedRoute user={user}>\r\n        <Layout>\r\n          <ProfileSettings />\r\n        </Layout>\r\n      </ProtectedRoute>\r\n    )\r\n  },\r\n  {\r\n    path: '/sop-library',\r\n    element: (\r\n      <ProtectedRoute user={user}>\r\n        <Layout>\r\n          <SOPLibrary />\r\n        </Layout>\r\n      </ProtectedRoute>\r\n    )\r\n  },\r\n  {\r\n    path: '/gap-analysis',\r\n    element: (\r\n      <ProtectedRoute user={user}>\r\n        <Layout>\r\n          <GapAnalysis />\r\n        </Layout>\r\n      </ProtectedRoute>\r\n    )\r\n  },\r\n   {\r\n    path: '/chatbot',\r\n    element: (\r\n      <ProtectedRoute user={user}>\r\n        <ChatPage />\r\n      </ProtectedRoute>\r\n    )\r\n  },\r\n  {\r\n    path: '/',\r\n    element: <Navigate to={user ? \"/dashboard\" : \"/login\"} replace />\r\n  },\r\n  {\r\n    path: '*',\r\n    element: <NotFound />\r\n  }\r\n];\r\n\r\nexport default routes; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,KAAK,MAAM,iCAAiC;AACnD,OAAOC,SAAS,MAAM,yCAAyC;AAC/D,OAAOC,eAAe,MAAM,qDAAqD;AACjF,OAAOC,UAAU,MAAM,2CAA2C;AAClE,OAAOC,WAAW,MAAM,6CAA6C;AACrE,OAAOC,cAAc,MAAM,oDAAoD;AAC/E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,MAAM,MAAM,oCAAoC;AACvD,OAAOC,QAAQ,MAAM,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC5D,MAAMC,MAAM,GAAIC,IAAI,IAAK,CACvB;EACEC,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAEF,IAAI,gBAAGF,OAAA,CAACX,QAAQ;IAACgB,EAAE,EAAC,YAAY;IAACC,OAAO;EAAA;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,gBAAGV,OAAA,CAACV,KAAK;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACjE,CAAC,EACD;EACEP,IAAI,EAAE,YAAY;EAClBC,OAAO,eACLJ,OAAA,CAACL,cAAc;IAACO,IAAI,EAAEA,IAAK;IAAAS,QAAA,eACzBX,OAAA,CAACH,MAAM;MAAAc,QAAA,eACLX,OAAA,CAACT,SAAS;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK;AAEpB,CAAC,EACD;EACEP,IAAI,EAAE,UAAU;EAChBC,OAAO,eACLJ,OAAA,CAACL,cAAc;IAACO,IAAI,EAAEA,IAAK;IAAAS,QAAA,eACzBX,OAAA,CAACH,MAAM;MAAAc,QAAA,eACLX,OAAA,CAACR,eAAe;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK;AAEpB,CAAC,EACD;EACEP,IAAI,EAAE,cAAc;EACpBC,OAAO,eACLJ,OAAA,CAACL,cAAc;IAACO,IAAI,EAAEA,IAAK;IAAAS,QAAA,eACzBX,OAAA,CAACH,MAAM;MAAAc,QAAA,eACLX,OAAA,CAACP,UAAU;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK;AAEpB,CAAC,EACD;EACEP,IAAI,EAAE,eAAe;EACrBC,OAAO,eACLJ,OAAA,CAACL,cAAc;IAACO,IAAI,EAAEA,IAAK;IAAAS,QAAA,eACzBX,OAAA,CAACH,MAAM;MAAAc,QAAA,eACLX,OAAA,CAACN,WAAW;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK;AAEpB,CAAC,EACA;EACCP,IAAI,EAAE,UAAU;EAChBC,OAAO,eACLJ,OAAA,CAACL,cAAc;IAACO,IAAI,EAAEA,IAAK;IAAAS,QAAA,eACzBX,OAAA,CAACF,QAAQ;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE;AAEpB,CAAC,EACD;EACEP,IAAI,EAAE,GAAG;EACTC,OAAO,eAAEJ,OAAA,CAACX,QAAQ;IAACgB,EAAE,EAAEH,IAAI,GAAG,YAAY,GAAG,QAAS;IAACI,OAAO;EAAA;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAClE,CAAC,EACD;EACEP,IAAI,EAAE,GAAG;EACTC,OAAO,eAAEJ,OAAA,CAACJ,QAAQ;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACtB,CAAC,CACF;AAED,eAAeT,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}