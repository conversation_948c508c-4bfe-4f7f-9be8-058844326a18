import { useState } from "react";
import { Send, Loader2 } from "lucide-react";
import "./QueryInput.css";

const QueryInput = ({ onSubmit, isLoading }) => {
  const [query, setQuery] = useState("");

  const handleSubmit = (e) => {
    e.preventDefault();
    if (query.trim() && !isLoading) {
      onSubmit(query.trim());
      setQuery("");
    }
  };

  return (
    <form onSubmit={handleSubmit} className="query-input-form">
      <input
        type="text"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="Ask a question about your SOPs and Regulations..."
        className="query-input-field"
        disabled={isLoading}
      />
      <button
        type="submit"
        disabled={!query.trim() || isLoading}
        className="query-input-button"
      >
        {isLoading ? (
          <Loader2 className="query-icon spin" />
        ) : (
          <Send className="query-icon" />
        )}
        {isLoading ? "Processing..." : "Send"}
      </button>
    </form>
  );
};

export default QueryInput;