import { useState } from "react";
import { ChevronDown, FileText } from "lucide-react";
import "./SOPDropdown.css";
import LoadingSpinner from "../LoadingSpinner/LoadingSpinner";

const SOPDropdown = ({ sopTitles, selectedSOPs, onSOPChange, isLoading }) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleSOPToggle = (sopTitle) => {
    if (selectedSOPs.includes(sopTitle)) {
      onSOPChange(selectedSOPs.filter((sop) => sop !== sopTitle));
    } else {
      onSOPChange([...selectedSOPs, sopTitle]);
    }
  };

  const handleSelectAll = () => {
    onSOPChange([...sopTitles]);
  };

  const handleClearAll = () => {
    onSOPChange([]);
  };

  return (
    <div className="sop-dropdown-wrapper">
      <label className="sop-label">Select Documents (Optional)</label>
      <div className="sop-selector">
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="sop-button"
        >
          <div className="sop-button-content">
            <div className="sop-summary">
              <FileText className="sop-icon" />
              <span>
                {selectedSOPs.length === 0
                  ? "All documents"
                  : selectedSOPs.length === 1
                  ? selectedSOPs[0]
                  : `${selectedSOPs.length} documents selected`}
              </span>
            </div>
            <ChevronDown className={`sop-chevron ${isOpen ? "rotate" : ""}`} />
          </div>
        </button>

        {isOpen && (
          <div className="sop-dropdown">
            <div className="sop-actions">
              <button
                onClick={handleSelectAll}
                className="sop-action-button blue"
              >
                Select All
              </button>
              <button
                onClick={handleClearAll}
                className="sop-action-button gray"
              >
                Clear All
              </button>
            </div>

            <div className="sop-options">
              {isLoading ? (
                <div className="sop-loading">
                  <LoadingSpinner size="small" />
                </div>
              ) : sopTitles.length === 0 ? (
                <div className="sop-empty">No documents available</div>
              ) : (
                sopTitles.map((sopTitle) => (
                  <label key={sopTitle} className="sop-option">
                    <input
                      type="checkbox"
                      checked={selectedSOPs.includes(sopTitle)}
                      onChange={() => handleSOPToggle(sopTitle)}
                      className="sop-checkbox"
                    />
                    <span className="sop-title">{sopTitle}</span>
                  </label>
                ))
              )}
            </div>
          </div>
        )}
      </div>

      {selectedSOPs.length > 0 && (
        <p className="sop-selected">Selected: {selectedSOPs.join(", ")}</p>
      )}
    </div>
  );
};

export default SOPDropdown;