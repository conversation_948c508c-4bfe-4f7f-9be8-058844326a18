.chatbot-page {
    min-height: 100vh;
    background-color: var(--bg-white);
    display: flex;
    flex-direction: column;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 80%;
    margin: 0 auto;
    padding: 0;
    position: relative;
    box-sizing: border-box;
    max-width: 1200px;
}

.error-box {
    margin: 1.5rem;
    padding: 1rem;
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.error-icon {
    width: 1.25rem;
    height: 1.25rem;
    color: #ef4444;
    flex-shrink: 0;
}

.error-text {
    color: #b91c1c;
    text-align: center;
}

.welcome-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    text-align: center;
    margin-top: 2rem;
    width: 100%;
    box-sizing: border-box;
}

.welcome-icon {
    padding: 1rem;
    background-color: var(--primary-blue-light);
    border-radius: 9999px;
    width: fit-content;
    margin: 0 auto 1rem;
}

.welcome-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0.5rem;
}

.welcome-subtitle {
    color: #4b5563;
    margin-bottom: 1.5rem;
}

.welcome-hints {
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.5;
}

.filter-indicator {
    color: var(--primary-blue);
    font-weight: 500;
}

.chat-history {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;
}

.loading-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
}

.loading-text {
    color: #6b7280;
}

/* Spinner Animation */
@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .chat-container {
    width: 95%;
    padding-bottom: 80px;
  }
  
  .welcome-section {
    padding: 1.5rem 0.5rem;
  }
  
  .welcome-title {
    font-size: 1.1rem;
  }
  
  .welcome-subtitle {
    font-size: 0.9rem;
  }
}