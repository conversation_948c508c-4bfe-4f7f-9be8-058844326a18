{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\zipp\\\\latent-frontend\\\\src\\\\components\\\\pages\\\\SOPLibrary\\\\SOPLibrary.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Navigation from '../../common/Navigation/Navigation';\nimport Modal from '../../common/Modal/Modal';\nimport AddSOP from './AddSOP';\nimport DepartmentFilters from '../../common/DepartmentFilters/DepartmentFilters';\nimport sopService from '../../../services/sopService';\nimport { ToastContainer, toast } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\nimport './SOPLibrary.css';\nimport LoadingSpinner from '../../common/LoadingSpinner/LoadingSpinner';\nimport ConfirmationModal from '../../common/ConfirmationModal/ConfirmationModal';\nimport { sanitizeText } from '../../../utils/sanitize';\nimport { formatDate } from '../../../utils/dateUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SOPLibrary = () => {\n  _s();\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [selectedDepartment, setSelectedDepartment] = useState('All');\n  const [sops, setSops] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [downloadingId, setDownloadingId] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [deletingId, setDeletingId] = useState(null);\n  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);\n  const [sopToDelete, setSopToDelete] = useState(null);\n  const [isUploading, setIsUploading] = useState(false);\n\n  // Add sorting and filtering states\n  const [sortField, setSortField] = useState('date');\n  const [sortDirection, setSortDirection] = useState('desc');\n  const [dateFilter, setDateFilter] = useState('all');\n\n  // Fetch SOPs on component mount\n  useEffect(() => {\n    fetchSOPs();\n  }, []);\n  const fetchSOPs = async (silent = false) => {\n    try {\n      if (!silent) {\n        setLoading(true);\n      }\n      const data = await sopService.getAllSOPs();\n      setSops(data.sops_data);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error fetching SOPs:', error);\n      let errorMessage = 'Failed to load SOPs. Please try again later.';\n      if (error !== null && error !== void 0 && (_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.detail) {\n        var _error$response2, _error$response2$data;\n        errorMessage = error === null || error === void 0 ? void 0 : (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail;\n      }\n      setError(errorMessage);\n    } finally {\n      if (!silent) {\n        setLoading(false);\n      }\n    }\n  };\n\n  // Silent fetch function for background updates\n  const silentFetchSOPs = async () => {\n    await fetchSOPs(true);\n  };\n\n  // Handle search input change\n  const handleSearchChange = e => {\n    setSearchTerm(e.target.value);\n  };\n\n  // Filter SOPs based on department and search term\n  const filteredSOPs = React.useMemo(() => {\n    let filtered = sops;\n\n    // Filter by department if not \"All\"\n    if (selectedDepartment !== 'All') {\n      filtered = filtered.filter(sop => {\n        // Check if department exists in the SOP object\n        // It might be stored in different properties depending on the API\n        return sop.department && sop.department === selectedDepartment || sop.department_name && sop.department_name === selectedDepartment || sop.dept && sop.dept === selectedDepartment || sop.metadata && sop.metadata.department === selectedDepartment;\n      });\n    }\n\n    // Filter by search term if not empty\n    if (searchTerm.trim() !== '') {\n      const term = searchTerm.toLowerCase();\n      filtered = filtered.filter(sop => (sop.title || sop.file_name || '').toLowerCase().includes(term));\n    }\n\n    // Apply date filter\n    if (dateFilter !== 'all') {\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      const lastWeek = new Date(today);\n      lastWeek.setDate(lastWeek.getDate() - 7);\n      const lastMonth = new Date(today);\n      lastMonth.setMonth(lastMonth.getMonth() - 1);\n      filtered = filtered.filter(sop => {\n        const sopDate = new Date(sop.updated_at || sop.created_at);\n        switch (dateFilter) {\n          case 'today':\n            return sopDate >= today;\n          case 'yesterday':\n            return sopDate >= yesterday && sopDate < today;\n          case 'week':\n            return sopDate >= lastWeek;\n          case 'month':\n            return sopDate >= lastMonth;\n          default:\n            return true;\n        }\n      });\n    }\n\n    // Sort the filtered results\n    return filtered.sort((a, b) => {\n      if (sortField === 'date') {\n        const dateA = new Date(a.updated_at || a.created_at || 0);\n        const dateB = new Date(b.updated_at || b.created_at || 0);\n        return sortDirection === 'asc' ? dateA - dateB : dateB - dateA;\n      } else if (sortField === 'title') {\n        const titleA = (a.title || '').toLowerCase();\n        const titleB = (b.title || '').toLowerCase();\n        return sortDirection === 'asc' ? titleA.localeCompare(titleB) : titleB.localeCompare(titleA);\n      }\n      return 0;\n    });\n  }, [sops, selectedDepartment, searchTerm, sortField, sortDirection, dateFilter]);\n  const handleDepartmentChange = department => {\n    setSelectedDepartment(department);\n  };\n\n  // Function to handle sort changes\n  const handleSortChange = field => {\n    if (sortField === field) {\n      // Toggle direction if clicking the same field\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      // Set new field and default to descending for date, ascending for title\n      setSortField(field);\n      setSortDirection(field === 'date' ? 'desc' : 'asc');\n    }\n  };\n\n  // Add a function to reset all filters\n  const resetFilters = () => {\n    setSearchTerm('');\n    setDateFilter('all');\n    setSortField('date');\n    setSortDirection('desc');\n    // Keep the department filter as is, since it's a primary filter\n  };\n\n  // formatDate is now imported from '../../../utils/dateUtils'\n\n  // Extract file extension for display\n  // const getFileExtension = (fileName) => {\n  //   if (!fileName) return '';\n  //   return fileName.split('.').pop().toUpperCase();\n  // };\n\n  // Add a function to extract department from SOP for display\n  const getDepartment = sop => {\n    // Check if metadata exists and has a department property\n    if (sop.metadata && sop.metadata.department) {\n      return sop.metadata.department;\n    }\n\n    // Fall back to other possible department properties\n    return sop.department || sop.department_name || sop.dept || 'Unknown';\n  };\n  const handleSOPUploadSuccess = newSOP => {\n    // Add a property to identify this as a newly uploaded SOP\n    // const sopWithUploadFlag = {\n    //   ...newSOP,\n    //   isNewlyUploaded: true\n    // };\n\n    // // Add the new SOP to the existing list\n    // setSops(prevSops => [sopWithUploadFlag, ...prevSops]);\n\n    // // Close the modal\n    silentFetchSOPs();\n    setIsModalOpen(false);\n\n    // Success message is already shown in AddSOP component, no need to duplicate it here\n  };\n  const handleSOPUploadError = errorMessage => {\n    toast.error(errorMessage || 'Failed to upload SOP. Please try again.');\n  };\n\n  // Handle SOP download\n  const handleDownloadSOP = async (sopId, fileName) => {\n    try {\n      setDownloadingId(sopId);\n      const response = await sopService.downloadSOP(sopId);\n      await console.log(response);\n      // Create a blob URL for the file\n      const url = window.URL.createObjectURL(new Blob([response.data]));\n\n      // Create a temporary link element to trigger the download\n      const link = document.createElement('a');\n      link.href = url;\n      if (response.data.type === 'application/pdf') {\n        link.setAttribute('download', `${fileName}.pdf` || 'document.pdf'); // Use the original filename or a default\n      } else if (response.data.type === 'text/plain') {\n        link.setAttribute('download', `${fileName}.txt` || 'document.txt'); // Use the original filename or a default\n      } else if (response.data.type === 'application/msword') {\n        link.setAttribute('download', `${fileName}.doc` || 'document.doc'); // Use the original filename or a default\n      } else if (response.data.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {\n        link.setAttribute('download', `${fileName}.docx` || 'document.docx'); // Use the original filename or a default\n      }\n      document.body.appendChild(link);\n\n      // Trigger the download\n      link.click();\n\n      // Clean up\n      window.URL.revokeObjectURL(url);\n      document.body.removeChild(link);\n      toast.success('File downloaded successfully');\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Error downloading SOP:', err);\n      let errorMessage = 'Failed to download file. Please try again.';\n      if (err !== null && err !== void 0 && (_err$response = err.response) !== null && _err$response !== void 0 && (_err$response$data = _err$response.data) !== null && _err$response$data !== void 0 && _err$response$data.detail) {\n        var _err$response2, _err$response2$data;\n        errorMessage = err === null || err === void 0 ? void 0 : (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.detail;\n      }\n      toast.error(errorMessage);\n    } finally {\n      setDownloadingId(null);\n    }\n  };\n  const handleDeleteClick = sop => {\n    setSopToDelete(sop);\n    setShowDeleteConfirmation(true);\n  };\n  const handleDeleteSOP = async () => {\n    if (!sopToDelete) return;\n    try {\n      setDeletingId(sopToDelete.id);\n\n      // Make the DELETE API call\n      await sopService.deleteSOP(sopToDelete.id);\n\n      // Remove the deleted SOP from the state\n      setSops(prevSops => prevSops.filter(sop => sop.id !== sopToDelete.id));\n      toast.success('SOP deleted successfully');\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      console.error('Error deleting SOP:', err);\n      let errorMessage = 'Failed to delete SOP. Please try again.';\n      if (err !== null && err !== void 0 && (_err$response3 = err.response) !== null && _err$response3 !== void 0 && (_err$response3$data = _err$response3.data) !== null && _err$response3$data !== void 0 && _err$response3$data.detail) {\n        var _err$response4, _err$response4$data;\n        errorMessage = err === null || err === void 0 ? void 0 : (_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.detail;\n      }\n      toast.error(errorMessage);\n    } finally {\n      setDeletingId(null);\n      setSopToDelete(null);\n    }\n  };\n\n  // Add a function to handle adding a new SOP at the top of the list\n  const handleSOPAdded = newSOP => {\n    // Add the new SOP at the beginning of the array\n    setSops(prevSops => [newSOP, ...prevSops]);\n  };\n  const renderAddSOPButton = () => {\n    return /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"add-sop-btn\",\n      onClick: () => setIsModalOpen(true),\n      children: \"Add New SOP\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Update the callback function to track processing state\n  const handleBeforeUpload = () => {\n    setIsUploading(true);\n  };\n  const handleAfterUpload = () => {\n    setIsUploading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"sop-library\",\n    children: [/*#__PURE__*/_jsxDEV(Navigation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sop-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"SOP Library\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), !loading && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"subtitle\",\n          children: [filteredSOPs.length, \" items\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 24\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-container\",\n        children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"loading-text\",\n          children: \"Loading SOPs...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(DepartmentFilters, {\n          onDepartmentChange: handleDepartmentChange,\n          defaultSelected: \"All\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 13\n        }, this), sops.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search SOPs...\",\n              className: \"search-input\",\n              value: searchTerm,\n              onChange: handleSearchChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 3\n            }, this), renderAddSOPButton(), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"connect-qms-btn\",\n              children: \"Connect to QMS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 3\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-sort-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Sort by:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sort-buttons\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `sort-btn ${sortField === 'date' ? 'active' : ''}`,\n                  onClick: () => handleSortChange('date'),\n                  children: [\"Date\", sortField === 'date' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"sort-direction\",\n                    children: sortDirection === 'asc' ? '↑' : '↓'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `sort-btn ${sortField === 'title' ? 'active' : ''}`,\n                  onClick: () => handleSortChange('title'),\n                  children: [\"Title\", sortField === 'title' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"sort-direction\",\n                    children: sortDirection === 'asc' ? '↑' : '↓'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Date:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: dateFilter,\n                onChange: e => setDateFilter(e.target.value),\n                className: \"filter-select\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Time\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"today\",\n                  children: \"Today\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"yesterday\",\n                  children: \"Yesterday\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"week\",\n                  children: \"Last 7 Days\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"month\",\n                  children: \"Last 30 Days\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"reset-filters-btn\",\n              onClick: resetFilters,\n              disabled: dateFilter === 'all' && sortField === 'date' && sortDirection === 'desc' && searchTerm === '',\n              children: \"Reset Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sop-list\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Standard Operating Procedures\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 15\n          }, this), error ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 17\n          }, this) : sops.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"empty-state\",\n            children: [\"No SOPs found. Add your first SOP by clicking \", renderAddSOPButton()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 17\n          }, this) : filteredSOPs.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"empty-state\",\n            children: searchTerm || dateFilter !== 'all' || selectedDepartment !== 'All' ? /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"No results found matching your filters. Try different filter settings.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 21\n            }, this) : 'No SOPs available.'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sop-items\",\n            children: filteredSOPs.map(sop => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sop-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sop-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: sanitizeText(sop.title)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"sop-details\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"department\",\n                    children: getDepartment(sop)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sop-meta\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"last-updated\",\n                  children: [\"Last updated: \", formatDate(sop.updated_at)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"sop-actions\",\n                  children: [!sop.isNewlyUploaded && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"view-icon-link\",\n                    onClick: () => handleDownloadSOP(sop.id, sop.title),\n                    disabled: downloadingId === sop.id,\n                    \"data-tooltip\": \"Download SOP\",\n                    children: downloadingId === sop.id ? /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n                      size: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 449,\n                      columnNumber: 33\n                    }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"view-icon\",\n                      width: \"16\",\n                      height: \"16\",\n                      viewBox: \"0 0 16 16\",\n                      fill: \"none\",\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M8 11.5L8 3.5\",\n                        stroke: \"#6c63ff\",\n                        strokeWidth: \"1.5\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 452,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M4.5 8L8 11.5L11.5 8\",\n                        stroke: \"#6c63ff\",\n                        strokeWidth: \"1.5\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 453,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M3 13.5H13\",\n                        stroke: \"#6c63ff\",\n                        strokeWidth: \"1.5\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 454,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"delete-icon-link\",\n                    onClick: () => handleDeleteClick(sop),\n                    disabled: deletingId === sop.id,\n                    \"data-tooltip\": \"Delete SOP\",\n                    children: deletingId === sop.id ? /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n                      size: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 467,\n                      columnNumber: 31\n                    }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"delete-icon\",\n                      width: \"16\",\n                      height: \"16\",\n                      viewBox: \"0 0 16 16\",\n                      fill: \"none\",\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M2 4H3.33333H14\",\n                        stroke: \"#ff6b6b\",\n                        strokeWidth: \"1.5\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 470,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M5.33334 4V2.66667C5.33334 2.31305 5.47382 1.97391 5.72387 1.72386C5.97392 1.47381 6.31305 1.33334 6.66667 1.33334H9.33334C9.68696 1.33334 10.0261 1.47381 10.2761 1.72386C10.5262 1.97391 10.6667 2.31305 10.6667 2.66667V4M12.6667 4V13.3333C12.6667 13.687 12.5262 14.0261 12.2761 14.2761C12.0261 14.5262 11.687 14.6667 11.3333 14.6667H4.66667C4.31305 14.6667 3.97391 14.5262 3.72386 14.2761C3.47381 14.0261 3.33334 13.687 3.33334 13.3333V4H12.6667Z\",\n                        stroke: \"#ff6b6b\",\n                        strokeWidth: \"1.5\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 471,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 469,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"play-icon-link\",\n                    \"data-tooltip\": \"Play SOP\",\n                    disabled: true,\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"play-icon\",\n                      width: \"16\",\n                      height: \"16\",\n                      viewBox: \"0 0 16 16\",\n                      fill: \"none\",\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M4.5 3.5L11.5 8L4.5 12.5V3.5Z\",\n                        stroke: \"#6c63ff\",\n                        strokeWidth: \"1.5\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 481,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 480,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 475,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 23\n              }, this)]\n            }, sop.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(Modal, {\n        isOpen: isModalOpen,\n        onClose: () => setIsModalOpen(false),\n        closeOnOutsideClick: true,\n        isProcessing: isUploading,\n        children: /*#__PURE__*/_jsxDEV(AddSOP, {\n          onClose: () => setIsModalOpen(false),\n          onSuccess: sop => {\n            handleSOPUploadSuccess(sop);\n            handleAfterUpload();\n          },\n          onError: () => {\n            handleSOPUploadError();\n            handleAfterUpload();\n          },\n          onBeforeUpload: handleBeforeUpload,\n          refreshSOPs: silentFetchSOPs\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 494,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n        isOpen: showDeleteConfirmation,\n        onClose: () => setShowDeleteConfirmation(false),\n        onConfirm: handleDeleteSOP,\n        title: \"Delete SOP\",\n        message: `Are you sure you want to delete \"${sopToDelete === null || sopToDelete === void 0 ? void 0 : sopToDelete.title}\"? This action cannot be undone.`,\n        confirmText: \"Delete\",\n        cancelText: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n      position: \"top-right\",\n      autoClose: 5000,\n      hideProgressBar: false,\n      newestOnTop: true,\n      closeOnClick: true,\n      rtl: false,\n      pauseOnFocusLoss: true,\n      draggable: true,\n      pauseOnHover: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 525,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 313,\n    columnNumber: 5\n  }, this);\n};\n_s(SOPLibrary, \"zH0MQmNierEWt3xCaEmT5YHb2Pc=\");\n_c = SOPLibrary;\nexport default SOPLibrary;\nvar _c;\n$RefreshReg$(_c, \"SOPLibrary\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Navigation", "Modal", "AddSOP", "DepartmentFilters", "sopService", "ToastContainer", "toast", "LoadingSpinner", "ConfirmationModal", "sanitizeText", "formatDate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SOPLibrary", "_s", "isModalOpen", "setIsModalOpen", "selectedDepartment", "setSelectedDepartment", "sops", "setSops", "loading", "setLoading", "error", "setError", "downloadingId", "setDownloadingId", "searchTerm", "setSearchTerm", "deletingId", "setDeletingId", "showDeleteConfirmation", "setShowDeleteConfirmation", "sopToDelete", "setSopToDelete", "isUploading", "setIsUploading", "sortField", "setSortField", "sortDirection", "setSortDirection", "dateFilter", "setDateFilter", "fetchSOPs", "silent", "data", "getAllSOPs", "sops_data", "_error$response", "_error$response$data", "console", "errorMessage", "response", "detail", "_error$response2", "_error$response2$data", "silentFetchSOPs", "handleSearchChange", "e", "target", "value", "filteredSOPs", "useMemo", "filtered", "filter", "sop", "department", "department_name", "dept", "metadata", "trim", "term", "toLowerCase", "title", "file_name", "includes", "now", "Date", "today", "getFullYear", "getMonth", "getDate", "yesterday", "setDate", "lastWeek", "lastM<PERSON>h", "setMonth", "sopDate", "updated_at", "created_at", "sort", "a", "b", "dateA", "dateB", "titleA", "titleB", "localeCompare", "handleDepartmentChange", "handleSortChange", "field", "resetFilters", "getDepartment", "handleSOPUploadSuccess", "newSOP", "handleSOPUploadError", "handleDownloadSOP", "sopId", "fileName", "downloadSOP", "log", "url", "window", "URL", "createObjectURL", "Blob", "link", "document", "createElement", "href", "type", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "success", "err", "_err$response", "_err$response$data", "_err$response2", "_err$response2$data", "handleDeleteClick", "handleDeleteSOP", "id", "deleteSOP", "prevSops", "_err$response3", "_err$response3$data", "_err$response4", "_err$response4$data", "handleSOPAdded", "renderAddSOPButton", "className", "onClick", "children", "_jsxFileName", "lineNumber", "columnNumber", "handleBeforeUpload", "handleAfterUpload", "length", "size", "onDepartmentChange", "defaultSelected", "placeholder", "onChange", "disabled", "map", "isNewlyUploaded", "width", "height", "viewBox", "fill", "xmlns", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "isOpen", "onClose", "closeOnOutsideClick", "isProcessing", "onSuccess", "onError", "onBeforeUpload", "refreshSOPs", "onConfirm", "message", "confirmText", "cancelText", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/components/pages/SOPLibrary/SOPLibrary.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport Navigation from '../../common/Navigation/Navigation';\r\nimport Modal from '../../common/Modal/Modal';\r\nimport AddSOP from './AddSOP';\r\nimport DepartmentFilters from '../../common/DepartmentFilters/DepartmentFilters';\r\nimport sopService from '../../../services/sopService';\r\nimport { ToastContainer, toast } from 'react-toastify';\r\nimport 'react-toastify/dist/ReactToastify.css';\r\nimport './SOPLibrary.css';\r\nimport LoadingSpinner from '../../common/LoadingSpinner/LoadingSpinner';\r\nimport ConfirmationModal from '../../common/ConfirmationModal/ConfirmationModal';\r\nimport { sanitizeText } from '../../../utils/sanitize';\r\nimport { formatDate } from '../../../utils/dateUtils';\r\n\r\nconst SOPLibrary = () => {\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n  const [selectedDepartment, setSelectedDepartment] = useState('All');\r\n  const [sops, setSops] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [downloadingId, setDownloadingId] = useState(null);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [deletingId, setDeletingId] = useState(null);\r\n  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);\r\n  const [sopToDelete, setSopToDelete] = useState(null);\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  \r\n  // Add sorting and filtering states\r\n  const [sortField, setSortField] = useState('date');\r\n  const [sortDirection, setSortDirection] = useState('desc');\r\n  const [dateFilter, setDateFilter] = useState('all');\r\n  \r\n  // Fetch SOPs on component mount\r\n  useEffect(() => {\r\n    fetchSOPs();\r\n  }, []);\r\n\r\n  const fetchSOPs = async (silent = false) => {\r\n    try {\r\n      if (!silent) {\r\n      setLoading(true);\r\n      }\r\n      const data = await sopService.getAllSOPs();\r\n      setSops(data.sops_data);\r\n    } catch (error) {\r\n      console.error('Error fetching SOPs:', error);\r\n      \r\n      let errorMessage = 'Failed to load SOPs. Please try again later.';\r\n      if (error?.response?.data?.detail) {\r\n        errorMessage = error?.response?.data?.detail;\r\n      }\r\n      \r\n      setError(errorMessage);\r\n    } finally {\r\n      if (!silent) {\r\n      setLoading(false);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Silent fetch function for background updates\r\n  const silentFetchSOPs = async () => {\r\n    await fetchSOPs(true);\r\n  };\r\n\r\n  // Handle search input change\r\n  const handleSearchChange = (e) => {\r\n    setSearchTerm(e.target.value);\r\n  };\r\n\r\n  // Filter SOPs based on department and search term\r\n  const filteredSOPs = React.useMemo(() => {\r\n    let filtered = sops;\r\n    \r\n    // Filter by department if not \"All\"\r\n    if (selectedDepartment !== 'All') {\r\n      filtered = filtered.filter(sop => {\r\n        // Check if department exists in the SOP object\r\n        // It might be stored in different properties depending on the API\r\n        return (\r\n          (sop.department && sop.department === selectedDepartment) ||\r\n          (sop.department_name && sop.department_name === selectedDepartment) ||\r\n          (sop.dept && sop.dept === selectedDepartment) ||\r\n          (sop.metadata && sop.metadata.department === selectedDepartment)\r\n        );\r\n      });\r\n    }\r\n    \r\n    // Filter by search term if not empty\r\n    if (searchTerm.trim() !== '') {\r\n      const term = searchTerm.toLowerCase();\r\n      filtered = filtered.filter(sop => \r\n        (sop.title || sop.file_name || '').toLowerCase().includes(term)\r\n      );\r\n    }\r\n    \r\n    // Apply date filter\r\n    if (dateFilter !== 'all') {\r\n      const now = new Date();\r\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\r\n      const yesterday = new Date(today);\r\n      yesterday.setDate(yesterday.getDate() - 1);\r\n      const lastWeek = new Date(today);\r\n      lastWeek.setDate(lastWeek.getDate() - 7);\r\n      const lastMonth = new Date(today);\r\n      lastMonth.setMonth(lastMonth.getMonth() - 1);\r\n      \r\n      filtered = filtered.filter(sop => {\r\n        const sopDate = new Date(sop.updated_at || sop.created_at);\r\n        \r\n        switch (dateFilter) {\r\n          case 'today':\r\n            return sopDate >= today;\r\n          case 'yesterday':\r\n            return sopDate >= yesterday && sopDate < today;\r\n          case 'week':\r\n            return sopDate >= lastWeek;\r\n          case 'month':\r\n            return sopDate >= lastMonth;\r\n          default:\r\n            return true;\r\n        }\r\n      });\r\n    }\r\n    \r\n    // Sort the filtered results\r\n    return filtered.sort((a, b) => {\r\n      if (sortField === 'date') {\r\n      const dateA = new Date(a.updated_at || a.created_at || 0);\r\n      const dateB = new Date(b.updated_at || b.created_at || 0);\r\n        return sortDirection === 'asc' ? dateA - dateB : dateB - dateA;\r\n      } else if (sortField === 'title') {\r\n        const titleA = (a.title || '').toLowerCase();\r\n        const titleB = (b.title || '').toLowerCase();\r\n        return sortDirection === 'asc' \r\n          ? titleA.localeCompare(titleB)\r\n          : titleB.localeCompare(titleA);\r\n      }\r\n      return 0;\r\n    });\r\n  }, [sops, selectedDepartment, searchTerm, sortField, sortDirection, dateFilter]);\r\n\r\n  const handleDepartmentChange = (department) => {\r\n    setSelectedDepartment(department);\r\n  };\r\n\r\n  // Function to handle sort changes\r\n  const handleSortChange = (field) => {\r\n    if (sortField === field) {\r\n      // Toggle direction if clicking the same field\r\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\r\n    } else {\r\n      // Set new field and default to descending for date, ascending for title\r\n      setSortField(field);\r\n      setSortDirection(field === 'date' ? 'desc' : 'asc');\r\n    }\r\n  };\r\n\r\n  // Add a function to reset all filters\r\n  const resetFilters = () => {\r\n    setSearchTerm('');\r\n    setDateFilter('all');\r\n    setSortField('date');\r\n    setSortDirection('desc');\r\n    // Keep the department filter as is, since it's a primary filter\r\n  };\r\n\r\n  // formatDate is now imported from '../../../utils/dateUtils'\r\n\r\n  // Extract file extension for display\r\n  // const getFileExtension = (fileName) => {\r\n  //   if (!fileName) return '';\r\n  //   return fileName.split('.').pop().toUpperCase();\r\n  // };\r\n\r\n  // Add a function to extract department from SOP for display\r\n  const getDepartment = (sop) => {\r\n    // Check if metadata exists and has a department property\r\n    if (sop.metadata && sop.metadata.department) {\r\n      return sop.metadata.department;\r\n    }\r\n    \r\n    // Fall back to other possible department properties\r\n    return sop.department || sop.department_name || sop.dept || 'Unknown';\r\n  };\r\n\r\n  const handleSOPUploadSuccess = (newSOP) => {\r\n    // Add a property to identify this as a newly uploaded SOP\r\n    // const sopWithUploadFlag = {\r\n    //   ...newSOP,\r\n    //   isNewlyUploaded: true\r\n    // };\r\n    \r\n    // // Add the new SOP to the existing list\r\n    // setSops(prevSops => [sopWithUploadFlag, ...prevSops]);\r\n    \r\n    // // Close the modal\r\n    silentFetchSOPs();\r\n    setIsModalOpen(false);\r\n    \r\n    // Success message is already shown in AddSOP component, no need to duplicate it here\r\n  };\r\n\r\n  const handleSOPUploadError = (errorMessage) => {\r\n    toast.error(errorMessage || 'Failed to upload SOP. Please try again.');\r\n  };\r\n\r\n  // Handle SOP download\r\n  const handleDownloadSOP = async (sopId, fileName) => {\r\n    try {\r\n      setDownloadingId(sopId);\r\n      const response = await sopService.downloadSOP(sopId);\r\n      await console.log(response);\r\n      // Create a blob URL for the file\r\n      const url = window.URL.createObjectURL(new Blob([response.data]));\r\n      \r\n      // Create a temporary link element to trigger the download\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      if(response.data.type === 'application/pdf'){\r\n        link.setAttribute('download', `${fileName}.pdf` || 'document.pdf'); // Use the original filename or a default\r\n      }else if(response.data.type === 'text/plain'){\r\n        link.setAttribute('download', `${fileName}.txt` || 'document.txt'); // Use the original filename or a default\r\n      }else if(response.data.type === 'application/msword'){\r\n        link.setAttribute('download', `${fileName}.doc` || 'document.doc'); // Use the original filename or a default\r\n      }else if(response.data.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'){\r\n        link.setAttribute('download', `${fileName}.docx` || 'document.docx'); // Use the original filename or a default\r\n      }\r\n      document.body.appendChild(link);\r\n      \r\n      // Trigger the download\r\n      link.click();\r\n      \r\n      // Clean up\r\n      window.URL.revokeObjectURL(url);\r\n      document.body.removeChild(link);\r\n      \r\n      toast.success('File downloaded successfully');\r\n    } catch (err) {\r\n      console.error('Error downloading SOP:', err);\r\n      \r\n      let errorMessage = 'Failed to download file. Please try again.';\r\n      if (err?.response?.data?.detail) {\r\n        errorMessage = err?.response?.data?.detail;\r\n      }\r\n      \r\n      toast.error(errorMessage);\r\n    } finally {\r\n      setDownloadingId(null);\r\n    }\r\n  };\r\n\r\n  const handleDeleteClick = (sop) => {\r\n    setSopToDelete(sop);\r\n    setShowDeleteConfirmation(true);\r\n  };\r\n\r\n  const handleDeleteSOP = async () => {\r\n    if (!sopToDelete) return;\r\n    \r\n    try {\r\n      setDeletingId(sopToDelete.id);\r\n      \r\n      // Make the DELETE API call\r\n      await sopService.deleteSOP(sopToDelete.id);\r\n      \r\n      // Remove the deleted SOP from the state\r\n      setSops(prevSops => prevSops.filter(sop => sop.id !== sopToDelete.id));\r\n      \r\n      toast.success('SOP deleted successfully');\r\n    } catch (err) {\r\n      console.error('Error deleting SOP:', err);\r\n      \r\n      let errorMessage = 'Failed to delete SOP. Please try again.';\r\n      if (err?.response?.data?.detail) {\r\n        errorMessage = err?.response?.data?.detail;\r\n      }\r\n      \r\n      toast.error(errorMessage);\r\n    } finally {\r\n      setDeletingId(null);\r\n      setSopToDelete(null);\r\n    }\r\n  };\r\n\r\n  // Add a function to handle adding a new SOP at the top of the list\r\n  const handleSOPAdded = (newSOP) => {\r\n    // Add the new SOP at the beginning of the array\r\n    setSops(prevSops => [newSOP, ...prevSops]);\r\n  };\r\n\r\n  const renderAddSOPButton = () => {\r\n    return (\r\n      <button \r\n        className=\"add-sop-btn\"\r\n        onClick={() => setIsModalOpen(true)}\r\n      >\r\n        Add New SOP\r\n      </button>\r\n    )\r\n  }\r\n\r\n  // Update the callback function to track processing state\r\n  const handleBeforeUpload = () => {\r\n    setIsUploading(true);\r\n  };\r\n\r\n  const handleAfterUpload = () => {\r\n    setIsUploading(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"sop-library\">\r\n      <Navigation />\r\n      <div className=\"sop-content\">\r\n        <div className=\"page-header\">\r\n          <h2>SOP Library</h2>\r\n          {!loading && <span className=\"subtitle\">{filteredSOPs.length} items</span>}\r\n        </div>\r\n\r\n        {loading ? (\r\n          <div className=\"loading-container\">\r\n            <LoadingSpinner size=\"large\" />\r\n            <span className=\"loading-text\">Loading SOPs...</span>\r\n          </div>\r\n        ) : (\r\n          <>\r\n            <DepartmentFilters \r\n              onDepartmentChange={handleDepartmentChange}\r\n              defaultSelected=\"All\"\r\n            />\r\n            \r\n            {/* Show search and filter controls if there are any SOPs in the database */}\r\n            {sops.length > 0 && (\r\n              <>\r\n                <div className=\"search-section\">\r\n  <input \r\n    type=\"text\" \r\n    placeholder=\"Search SOPs...\" \r\n    className=\"search-input\"\r\n    value={searchTerm}\r\n    onChange={handleSearchChange}\r\n  />\r\n  {renderAddSOPButton()}\r\n  <button \r\n    className=\"connect-qms-btn\"\r\n  >\r\n    Connect to QMS\r\n  </button>\r\n</div>\r\n                \r\n                <div className=\"filter-sort-controls\">\r\n                  <div className=\"filter-group\">\r\n                    <label>Sort by:</label>\r\n                    <div className=\"sort-buttons\">\r\n                      <button \r\n                        className={`sort-btn ${sortField === 'date' ? 'active' : ''}`}\r\n                        onClick={() => handleSortChange('date')}\r\n                      >\r\n                        Date\r\n                        {sortField === 'date' && (\r\n                          <span className=\"sort-direction\">\r\n                            {sortDirection === 'asc' ? '↑' : '↓'}\r\n                          </span>\r\n                        )}\r\n                      </button>\r\n                      <button \r\n                        className={`sort-btn ${sortField === 'title' ? 'active' : ''}`}\r\n                        onClick={() => handleSortChange('title')}\r\n                      >\r\n                        Title\r\n                        {sortField === 'title' && (\r\n                          <span className=\"sort-direction\">\r\n                            {sortDirection === 'asc' ? '↑' : '↓'}\r\n                          </span>\r\n                        )}\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  <div className=\"filter-group\">\r\n                    <label>Date:</label>\r\n                    <select \r\n                      value={dateFilter} \r\n                      onChange={(e) => setDateFilter(e.target.value)}\r\n                      className=\"filter-select\"\r\n                    >\r\n                      <option value=\"all\">All Time</option>\r\n                      <option value=\"today\">Today</option>\r\n                      <option value=\"yesterday\">Yesterday</option>\r\n                      <option value=\"week\">Last 7 Days</option>\r\n                      <option value=\"month\">Last 30 Days</option>\r\n                    </select>\r\n                  </div>\r\n                  \r\n                  <button \r\n                    className=\"reset-filters-btn\"\r\n                    onClick={resetFilters}\r\n                    disabled={dateFilter === 'all' && sortField === 'date' && sortDirection === 'desc' && searchTerm === ''}\r\n                  >\r\n                    Reset Filters\r\n                  </button>\r\n                </div>\r\n              </>\r\n            )}\r\n\r\n            <div className=\"sop-list\">\r\n              <h2>Standard Operating Procedures</h2>\r\n              \r\n              {error ? (\r\n                <div className=\"error-message\">{error}</div>\r\n              ) : sops.length === 0 ? (\r\n                <div className=\"empty-state\">\r\n                  No SOPs found. Add your first SOP by clicking {renderAddSOPButton()}\r\n                </div>\r\n              ) : filteredSOPs.length === 0 ? (\r\n                <div className=\"empty-state\">\r\n                  {searchTerm || dateFilter !== 'all' || selectedDepartment !== 'All' ? (\r\n                    <div>\r\n                      <p>No results found matching your filters. Try different filter settings.</p>\r\n                   \r\n                    </div>\r\n                  ) : (\r\n                    'No SOPs available.'\r\n                  )}\r\n                </div>\r\n              ) : (\r\n                <div className=\"sop-items\">\r\n                  {filteredSOPs.map((sop) => (\r\n                    <div key={sop.id} className=\"sop-item\">\r\n                      <div className=\"sop-info\">\r\n                        <h3>{sanitizeText(sop.title)}</h3>\r\n                        <div className=\"sop-details\">\r\n                          <span className=\"department\">{getDepartment(sop)}</span>\r\n                          {/* <span className=\"file-type\">{getFileExtension(sop.file_name)}</span> */}\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"sop-meta\">\r\n                        <span className=\"last-updated\">Last updated: {formatDate(sop.updated_at)}</span>\r\n                        <div className=\"sop-actions\">\r\n                          {!sop.isNewlyUploaded && (\r\n                            <button\r\n                              className=\"view-icon-link\"\r\n                              onClick={() => handleDownloadSOP(sop.id, sop.title)}\r\n                              disabled={downloadingId === sop.id}\r\n                              data-tooltip=\"Download SOP\"\r\n                            >\r\n                              {downloadingId === sop.id ? (\r\n                                <LoadingSpinner size=\"small\" />\r\n                              ) : (\r\n                                <svg className=\"view-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                                  <path d=\"M8 11.5L8 3.5\" stroke=\"#6c63ff\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                                  <path d=\"M4.5 8L8 11.5L11.5 8\" stroke=\"#6c63ff\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                                  <path d=\"M3 13.5H13\" stroke=\"#6c63ff\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                                </svg>\r\n                              )}\r\n                            </button>\r\n                          )}\r\n                          \r\n                          <button\r\n                            className=\"delete-icon-link\"\r\n                            onClick={() => handleDeleteClick(sop)}\r\n                            disabled={deletingId === sop.id}\r\n                            data-tooltip=\"Delete SOP\"\r\n                          >\r\n                            {deletingId === sop.id ? (\r\n                              <LoadingSpinner size=\"small\" />\r\n                            ) : (\r\n                              <svg className=\"delete-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                                <path d=\"M2 4H3.33333H14\" stroke=\"#ff6b6b\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                                <path d=\"M5.33334 4V2.66667C5.33334 2.31305 5.47382 1.97391 5.72387 1.72386C5.97392 1.47381 6.31305 1.33334 6.66667 1.33334H9.33334C9.68696 1.33334 10.0261 1.47381 10.2761 1.72386C10.5262 1.97391 10.6667 2.31305 10.6667 2.66667V4M12.6667 4V13.3333C12.6667 13.687 12.5262 14.0261 12.2761 14.2761C12.0261 14.5262 11.687 14.6667 11.3333 14.6667H4.66667C4.31305 14.6667 3.97391 14.5262 3.72386 14.2761C3.47381 14.0261 3.33334 13.687 3.33334 13.3333V4H12.6667Z\" stroke=\"#ff6b6b\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                              </svg>\r\n                            )}\r\n                          </button>\r\n                          <button\r\n                            className=\"play-icon-link\"\r\n                            data-tooltip=\"Play SOP\"\r\n                            disabled={true}\r\n                          >\r\n                            <svg className=\"play-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                              <path d=\"M4.5 3.5L11.5 8L4.5 12.5V3.5Z\" stroke=\"#6c63ff\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                            </svg>\r\n                          </button>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </>\r\n        )}\r\n\r\n        <Modal \r\n          isOpen={isModalOpen} \r\n          onClose={() => setIsModalOpen(false)} \r\n          closeOnOutsideClick={true}\r\n          isProcessing={isUploading}\r\n        >\r\n          <AddSOP \r\n            onClose={() => setIsModalOpen(false)} \r\n            onSuccess={(sop) => {\r\n              handleSOPUploadSuccess(sop);\r\n              handleAfterUpload();\r\n            }}\r\n            onError={() => {\r\n              handleSOPUploadError();\r\n              handleAfterUpload();\r\n            }}\r\n            onBeforeUpload={handleBeforeUpload}\r\n            refreshSOPs={silentFetchSOPs}\r\n          />\r\n        </Modal>\r\n\r\n        <ConfirmationModal\r\n          isOpen={showDeleteConfirmation}\r\n          onClose={() => setShowDeleteConfirmation(false)}\r\n          onConfirm={handleDeleteSOP}\r\n          title=\"Delete SOP\"\r\n          message={`Are you sure you want to delete \"${sopToDelete?.title}\"? This action cannot be undone.`}\r\n          confirmText=\"Delete\"\r\n          cancelText=\"Cancel\"\r\n        />\r\n      </div>\r\n      <ToastContainer \r\n        position=\"top-right\"\r\n        autoClose={5000}\r\n        hideProgressBar={false}\r\n        newestOnTop\r\n        closeOnClick\r\n        rtl={false}\r\n        pauseOnFocusLoss\r\n        draggable\r\n        pauseOnHover\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SOPLibrary; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,KAAK,MAAM,0BAA0B;AAC5C,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,iBAAiB,MAAM,kDAAkD;AAChF,OAAOC,UAAU,MAAM,8BAA8B;AACrD,SAASC,cAAc,EAAEC,KAAK,QAAQ,gBAAgB;AACtD,OAAO,uCAAuC;AAC9C,OAAO,kBAAkB;AACzB,OAAOC,cAAc,MAAM,4CAA4C;AACvE,OAAOC,iBAAiB,MAAM,kDAAkD;AAChF,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,UAAU,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACuB,IAAI,EAAEC,OAAO,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACmC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAM,CAACyC,SAAS,EAAEC,YAAY,CAAC,GAAG1C,QAAQ,CAAC,MAAM,CAAC;EAClD,MAAM,CAAC2C,aAAa,EAAEC,gBAAgB,CAAC,GAAG5C,QAAQ,CAAC,MAAM,CAAC;EAC1D,MAAM,CAAC6C,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACAC,SAAS,CAAC,MAAM;IACd8C,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAOC,MAAM,GAAG,KAAK,KAAK;IAC1C,IAAI;MACF,IAAI,CAACA,MAAM,EAAE;QACbtB,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAMuB,IAAI,GAAG,MAAM3C,UAAU,CAAC4C,UAAU,CAAC,CAAC;MAC1C1B,OAAO,CAACyB,IAAI,CAACE,SAAS,CAAC;IACzB,CAAC,CAAC,OAAOxB,KAAK,EAAE;MAAA,IAAAyB,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAAC3B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAE5C,IAAI4B,YAAY,GAAG,8CAA8C;MACjE,IAAI5B,KAAK,aAALA,KAAK,gBAAAyB,eAAA,GAALzB,KAAK,CAAE6B,QAAQ,cAAAJ,eAAA,gBAAAC,oBAAA,GAAfD,eAAA,CAAiBH,IAAI,cAAAI,oBAAA,eAArBA,oBAAA,CAAuBI,MAAM,EAAE;QAAA,IAAAC,gBAAA,EAAAC,qBAAA;QACjCJ,YAAY,GAAG5B,KAAK,aAALA,KAAK,wBAAA+B,gBAAA,GAAL/B,KAAK,CAAE6B,QAAQ,cAAAE,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBT,IAAI,cAAAU,qBAAA,uBAArBA,qBAAA,CAAuBF,MAAM;MAC9C;MAEA7B,QAAQ,CAAC2B,YAAY,CAAC;IACxB,CAAC,SAAS;MACR,IAAI,CAACP,MAAM,EAAE;QACbtB,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;EACF,CAAC;;EAED;EACA,MAAMkC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,MAAMb,SAAS,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMc,kBAAkB,GAAIC,CAAC,IAAK;IAChC9B,aAAa,CAAC8B,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGlE,KAAK,CAACmE,OAAO,CAAC,MAAM;IACvC,IAAIC,QAAQ,GAAG5C,IAAI;;IAEnB;IACA,IAAIF,kBAAkB,KAAK,KAAK,EAAE;MAChC8C,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,GAAG,IAAI;QAChC;QACA;QACA,OACGA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACC,UAAU,KAAKjD,kBAAkB,IACvDgD,GAAG,CAACE,eAAe,IAAIF,GAAG,CAACE,eAAe,KAAKlD,kBAAmB,IAClEgD,GAAG,CAACG,IAAI,IAAIH,GAAG,CAACG,IAAI,KAAKnD,kBAAmB,IAC5CgD,GAAG,CAACI,QAAQ,IAAIJ,GAAG,CAACI,QAAQ,CAACH,UAAU,KAAKjD,kBAAmB;MAEpE,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIU,UAAU,CAAC2C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC5B,MAAMC,IAAI,GAAG5C,UAAU,CAAC6C,WAAW,CAAC,CAAC;MACrCT,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,GAAG,IAC5B,CAACA,GAAG,CAACQ,KAAK,IAAIR,GAAG,CAACS,SAAS,IAAI,EAAE,EAAEF,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,IAAI,CAChE,CAAC;IACH;;IAEA;IACA,IAAI9B,UAAU,KAAK,KAAK,EAAE;MACxB,MAAMmC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;MACtB,MAAMC,KAAK,GAAG,IAAID,IAAI,CAACD,GAAG,CAACG,WAAW,CAAC,CAAC,EAAEH,GAAG,CAACI,QAAQ,CAAC,CAAC,EAAEJ,GAAG,CAACK,OAAO,CAAC,CAAC,CAAC;MACxE,MAAMC,SAAS,GAAG,IAAIL,IAAI,CAACC,KAAK,CAAC;MACjCI,SAAS,CAACC,OAAO,CAACD,SAAS,CAACD,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;MAC1C,MAAMG,QAAQ,GAAG,IAAIP,IAAI,CAACC,KAAK,CAAC;MAChCM,QAAQ,CAACD,OAAO,CAACC,QAAQ,CAACH,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;MACxC,MAAMI,SAAS,GAAG,IAAIR,IAAI,CAACC,KAAK,CAAC;MACjCO,SAAS,CAACC,QAAQ,CAACD,SAAS,CAACL,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MAE5CjB,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,GAAG,IAAI;QAChC,MAAMsB,OAAO,GAAG,IAAIV,IAAI,CAACZ,GAAG,CAACuB,UAAU,IAAIvB,GAAG,CAACwB,UAAU,CAAC;QAE1D,QAAQhD,UAAU;UAChB,KAAK,OAAO;YACV,OAAO8C,OAAO,IAAIT,KAAK;UACzB,KAAK,WAAW;YACd,OAAOS,OAAO,IAAIL,SAAS,IAAIK,OAAO,GAAGT,KAAK;UAChD,KAAK,MAAM;YACT,OAAOS,OAAO,IAAIH,QAAQ;UAC5B,KAAK,OAAO;YACV,OAAOG,OAAO,IAAIF,SAAS;UAC7B;YACE,OAAO,IAAI;QACf;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,OAAOtB,QAAQ,CAAC2B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC7B,IAAIvD,SAAS,KAAK,MAAM,EAAE;QAC1B,MAAMwD,KAAK,GAAG,IAAIhB,IAAI,CAACc,CAAC,CAACH,UAAU,IAAIG,CAAC,CAACF,UAAU,IAAI,CAAC,CAAC;QACzD,MAAMK,KAAK,GAAG,IAAIjB,IAAI,CAACe,CAAC,CAACJ,UAAU,IAAII,CAAC,CAACH,UAAU,IAAI,CAAC,CAAC;QACvD,OAAOlD,aAAa,KAAK,KAAK,GAAGsD,KAAK,GAAGC,KAAK,GAAGA,KAAK,GAAGD,KAAK;MAChE,CAAC,MAAM,IAAIxD,SAAS,KAAK,OAAO,EAAE;QAChC,MAAM0D,MAAM,GAAG,CAACJ,CAAC,CAAClB,KAAK,IAAI,EAAE,EAAED,WAAW,CAAC,CAAC;QAC5C,MAAMwB,MAAM,GAAG,CAACJ,CAAC,CAACnB,KAAK,IAAI,EAAE,EAAED,WAAW,CAAC,CAAC;QAC5C,OAAOjC,aAAa,KAAK,KAAK,GAC1BwD,MAAM,CAACE,aAAa,CAACD,MAAM,CAAC,GAC5BA,MAAM,CAACC,aAAa,CAACF,MAAM,CAAC;MAClC;MACA,OAAO,CAAC;IACV,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC5E,IAAI,EAAEF,kBAAkB,EAAEU,UAAU,EAAEU,SAAS,EAAEE,aAAa,EAAEE,UAAU,CAAC,CAAC;EAEhF,MAAMyD,sBAAsB,GAAIhC,UAAU,IAAK;IAC7ChD,qBAAqB,CAACgD,UAAU,CAAC;EACnC,CAAC;;EAED;EACA,MAAMiC,gBAAgB,GAAIC,KAAK,IAAK;IAClC,IAAI/D,SAAS,KAAK+D,KAAK,EAAE;MACvB;MACA5D,gBAAgB,CAACD,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;IAC5D,CAAC,MAAM;MACL;MACAD,YAAY,CAAC8D,KAAK,CAAC;MACnB5D,gBAAgB,CAAC4D,KAAK,KAAK,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBzE,aAAa,CAAC,EAAE,CAAC;IACjBc,aAAa,CAAC,KAAK,CAAC;IACpBJ,YAAY,CAAC,MAAM,CAAC;IACpBE,gBAAgB,CAAC,MAAM,CAAC;IACxB;EACF,CAAC;;EAED;;EAEA;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAM8D,aAAa,GAAIrC,GAAG,IAAK;IAC7B;IACA,IAAIA,GAAG,CAACI,QAAQ,IAAIJ,GAAG,CAACI,QAAQ,CAACH,UAAU,EAAE;MAC3C,OAAOD,GAAG,CAACI,QAAQ,CAACH,UAAU;IAChC;;IAEA;IACA,OAAOD,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,eAAe,IAAIF,GAAG,CAACG,IAAI,IAAI,SAAS;EACvE,CAAC;EAED,MAAMmC,sBAAsB,GAAIC,MAAM,IAAK;IACzC;IACA;IACA;IACA;IACA;;IAEA;IACA;;IAEA;IACAhD,eAAe,CAAC,CAAC;IACjBxC,cAAc,CAAC,KAAK,CAAC;;IAErB;EACF,CAAC;EAED,MAAMyF,oBAAoB,GAAItD,YAAY,IAAK;IAC7C/C,KAAK,CAACmB,KAAK,CAAC4B,YAAY,IAAI,yCAAyC,CAAC;EACxE,CAAC;;EAED;EACA,MAAMuD,iBAAiB,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACnD,IAAI;MACFlF,gBAAgB,CAACiF,KAAK,CAAC;MACvB,MAAMvD,QAAQ,GAAG,MAAMlD,UAAU,CAAC2G,WAAW,CAACF,KAAK,CAAC;MACpD,MAAMzD,OAAO,CAAC4D,GAAG,CAAC1D,QAAQ,CAAC;MAC3B;MACA,MAAM2D,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAAC/D,QAAQ,CAACP,IAAI,CAAC,CAAC,CAAC;;MAEjE;MACA,MAAMuE,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGR,GAAG;MACf,IAAG3D,QAAQ,CAACP,IAAI,CAAC2E,IAAI,KAAK,iBAAiB,EAAC;QAC1CJ,IAAI,CAACK,YAAY,CAAC,UAAU,EAAE,GAAGb,QAAQ,MAAM,IAAI,cAAc,CAAC,CAAC,CAAC;MACtE,CAAC,MAAK,IAAGxD,QAAQ,CAACP,IAAI,CAAC2E,IAAI,KAAK,YAAY,EAAC;QAC3CJ,IAAI,CAACK,YAAY,CAAC,UAAU,EAAE,GAAGb,QAAQ,MAAM,IAAI,cAAc,CAAC,CAAC,CAAC;MACtE,CAAC,MAAK,IAAGxD,QAAQ,CAACP,IAAI,CAAC2E,IAAI,KAAK,oBAAoB,EAAC;QACnDJ,IAAI,CAACK,YAAY,CAAC,UAAU,EAAE,GAAGb,QAAQ,MAAM,IAAI,cAAc,CAAC,CAAC,CAAC;MACtE,CAAC,MAAK,IAAGxD,QAAQ,CAACP,IAAI,CAAC2E,IAAI,KAAK,yEAAyE,EAAC;QACxGJ,IAAI,CAACK,YAAY,CAAC,UAAU,EAAE,GAAGb,QAAQ,OAAO,IAAI,eAAe,CAAC,CAAC,CAAC;MACxE;MACAS,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;;MAE/B;MACAA,IAAI,CAACQ,KAAK,CAAC,CAAC;;MAEZ;MACAZ,MAAM,CAACC,GAAG,CAACY,eAAe,CAACd,GAAG,CAAC;MAC/BM,QAAQ,CAACK,IAAI,CAACI,WAAW,CAACV,IAAI,CAAC;MAE/BhH,KAAK,CAAC2H,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZhF,OAAO,CAAC3B,KAAK,CAAC,wBAAwB,EAAEyG,GAAG,CAAC;MAE5C,IAAI7E,YAAY,GAAG,4CAA4C;MAC/D,IAAI6E,GAAG,aAAHA,GAAG,gBAAAC,aAAA,GAAHD,GAAG,CAAE5E,QAAQ,cAAA6E,aAAA,gBAAAC,kBAAA,GAAbD,aAAA,CAAepF,IAAI,cAAAqF,kBAAA,eAAnBA,kBAAA,CAAqB7E,MAAM,EAAE;QAAA,IAAA8E,cAAA,EAAAC,mBAAA;QAC/BjF,YAAY,GAAG6E,GAAG,aAAHA,GAAG,wBAAAG,cAAA,GAAHH,GAAG,CAAE5E,QAAQ,cAAA+E,cAAA,wBAAAC,mBAAA,GAAbD,cAAA,CAAetF,IAAI,cAAAuF,mBAAA,uBAAnBA,mBAAA,CAAqB/E,MAAM;MAC5C;MAEAjD,KAAK,CAACmB,KAAK,CAAC4B,YAAY,CAAC;IAC3B,CAAC,SAAS;MACRzB,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;EAED,MAAM2G,iBAAiB,GAAIpE,GAAG,IAAK;IACjC/B,cAAc,CAAC+B,GAAG,CAAC;IACnBjC,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMsG,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACrG,WAAW,EAAE;IAElB,IAAI;MACFH,aAAa,CAACG,WAAW,CAACsG,EAAE,CAAC;;MAE7B;MACA,MAAMrI,UAAU,CAACsI,SAAS,CAACvG,WAAW,CAACsG,EAAE,CAAC;;MAE1C;MACAnH,OAAO,CAACqH,QAAQ,IAAIA,QAAQ,CAACzE,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACsE,EAAE,KAAKtG,WAAW,CAACsG,EAAE,CAAC,CAAC;MAEtEnI,KAAK,CAAC2H,OAAO,CAAC,0BAA0B,CAAC;IAC3C,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAU,cAAA,EAAAC,mBAAA;MACZzF,OAAO,CAAC3B,KAAK,CAAC,qBAAqB,EAAEyG,GAAG,CAAC;MAEzC,IAAI7E,YAAY,GAAG,yCAAyC;MAC5D,IAAI6E,GAAG,aAAHA,GAAG,gBAAAU,cAAA,GAAHV,GAAG,CAAE5E,QAAQ,cAAAsF,cAAA,gBAAAC,mBAAA,GAAbD,cAAA,CAAe7F,IAAI,cAAA8F,mBAAA,eAAnBA,mBAAA,CAAqBtF,MAAM,EAAE;QAAA,IAAAuF,cAAA,EAAAC,mBAAA;QAC/B1F,YAAY,GAAG6E,GAAG,aAAHA,GAAG,wBAAAY,cAAA,GAAHZ,GAAG,CAAE5E,QAAQ,cAAAwF,cAAA,wBAAAC,mBAAA,GAAbD,cAAA,CAAe/F,IAAI,cAAAgG,mBAAA,uBAAnBA,mBAAA,CAAqBxF,MAAM;MAC5C;MAEAjD,KAAK,CAACmB,KAAK,CAAC4B,YAAY,CAAC;IAC3B,CAAC,SAAS;MACRrB,aAAa,CAAC,IAAI,CAAC;MACnBI,cAAc,CAAC,IAAI,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAM4G,cAAc,GAAItC,MAAM,IAAK;IACjC;IACApF,OAAO,CAACqH,QAAQ,IAAI,CAACjC,MAAM,EAAE,GAAGiC,QAAQ,CAAC,CAAC;EAC5C,CAAC;EAED,MAAMM,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,oBACErI,OAAA;MACEsI,SAAS,EAAC,aAAa;MACvBC,OAAO,EAAEA,CAAA,KAAMjI,cAAc,CAAC,IAAI,CAAE;MAAAkI,QAAA,EACrC;IAED;MAAAtC,QAAA,EAAAuC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAEb,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BlH,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMmH,iBAAiB,GAAGA,CAAA,KAAM;IAC9BnH,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,oBACE1B,OAAA;IAAKsI,SAAS,EAAC,aAAa;IAAAE,QAAA,gBAC1BxI,OAAA,CAACZ,UAAU;MAAA8G,QAAA,EAAAuC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACd3I,OAAA;MAAKsI,SAAS,EAAC,aAAa;MAAAE,QAAA,gBAC1BxI,OAAA;QAAKsI,SAAS,EAAC,aAAa;QAAAE,QAAA,gBAC1BxI,OAAA;UAAAwI,QAAA,EAAI;QAAW;UAAAtC,QAAA,EAAAuC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACnB,CAAChI,OAAO,iBAAIX,OAAA;UAAMsI,SAAS,EAAC,UAAU;UAAAE,QAAA,GAAErF,YAAY,CAAC2F,MAAM,EAAC,QAAM;QAAA;UAAA5C,QAAA,EAAAuC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAzC,QAAA,EAAAuC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,EAELhI,OAAO,gBACNX,OAAA;QAAKsI,SAAS,EAAC,mBAAmB;QAAAE,QAAA,gBAChCxI,OAAA,CAACL,cAAc;UAACoJ,IAAI,EAAC;QAAO;UAAA7C,QAAA,EAAAuC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/B3I,OAAA;UAAMsI,SAAS,EAAC,cAAc;UAAAE,QAAA,EAAC;QAAe;UAAAtC,QAAA,EAAAuC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAzC,QAAA,EAAAuC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,gBAEN3I,OAAA,CAAAE,SAAA;QAAAsI,QAAA,gBACExI,OAAA,CAACT,iBAAiB;UAChByJ,kBAAkB,EAAExD,sBAAuB;UAC3CyD,eAAe,EAAC;QAAK;UAAA/C,QAAA,EAAAuC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,EAGDlI,IAAI,CAACqI,MAAM,GAAG,CAAC,iBACd9I,OAAA,CAAAE,SAAA;UAAAsI,QAAA,gBACExI,OAAA;YAAKsI,SAAS,EAAC,gBAAgB;YAAAE,QAAA,gBAC7CxI,OAAA;cACE8G,IAAI,EAAC,MAAM;cACXoC,WAAW,EAAC,gBAAgB;cAC5BZ,SAAS,EAAC,cAAc;cACxBpF,KAAK,EAAEjC,UAAW;cAClBkI,QAAQ,EAAEpG;YAAmB;cAAAmD,QAAA,EAAAuC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,EACDN,kBAAkB,CAAC,CAAC,eACrBrI,OAAA;cACEsI,SAAS,EAAC,iBAAiB;cAAAE,QAAA,EAC5B;YAED;cAAAtC,QAAA,EAAAuC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAzC,QAAA,EAAAuC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEU3I,OAAA;YAAKsI,SAAS,EAAC,sBAAsB;YAAAE,QAAA,gBACnCxI,OAAA;cAAKsI,SAAS,EAAC,cAAc;cAAAE,QAAA,gBAC3BxI,OAAA;gBAAAwI,QAAA,EAAO;cAAQ;gBAAAtC,QAAA,EAAAuC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvB3I,OAAA;gBAAKsI,SAAS,EAAC,cAAc;gBAAAE,QAAA,gBAC3BxI,OAAA;kBACEsI,SAAS,EAAE,YAAY3G,SAAS,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;kBAC9D4G,OAAO,EAAEA,CAAA,KAAM9C,gBAAgB,CAAC,MAAM,CAAE;kBAAA+C,QAAA,GACzC,MAEC,EAAC7G,SAAS,KAAK,MAAM,iBACnB3B,OAAA;oBAAMsI,SAAS,EAAC,gBAAgB;oBAAAE,QAAA,EAC7B3G,aAAa,KAAK,KAAK,GAAG,GAAG,GAAG;kBAAG;oBAAAqE,QAAA,EAAAuC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CACP;gBAAA;kBAAAzC,QAAA,EAAAuC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACT3I,OAAA;kBACEsI,SAAS,EAAE,YAAY3G,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAE,EAAG;kBAC/D4G,OAAO,EAAEA,CAAA,KAAM9C,gBAAgB,CAAC,OAAO,CAAE;kBAAA+C,QAAA,GAC1C,OAEC,EAAC7G,SAAS,KAAK,OAAO,iBACpB3B,OAAA;oBAAMsI,SAAS,EAAC,gBAAgB;oBAAAE,QAAA,EAC7B3G,aAAa,KAAK,KAAK,GAAG,GAAG,GAAG;kBAAG;oBAAAqE,QAAA,EAAAuC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CACP;gBAAA;kBAAAzC,QAAA,EAAAuC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAzC,QAAA,EAAAuC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAzC,QAAA,EAAAuC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN3I,OAAA;cAAKsI,SAAS,EAAC,cAAc;cAAAE,QAAA,gBAC3BxI,OAAA;gBAAAwI,QAAA,EAAO;cAAK;gBAAAtC,QAAA,EAAAuC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpB3I,OAAA;gBACEkD,KAAK,EAAEnB,UAAW;gBAClBoH,QAAQ,EAAGnG,CAAC,IAAKhB,aAAa,CAACgB,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;gBAC/CoF,SAAS,EAAC,eAAe;gBAAAE,QAAA,gBAEzBxI,OAAA;kBAAQkD,KAAK,EAAC,KAAK;kBAAAsF,QAAA,EAAC;gBAAQ;kBAAAtC,QAAA,EAAAuC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrC3I,OAAA;kBAAQkD,KAAK,EAAC,OAAO;kBAAAsF,QAAA,EAAC;gBAAK;kBAAAtC,QAAA,EAAAuC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC3I,OAAA;kBAAQkD,KAAK,EAAC,WAAW;kBAAAsF,QAAA,EAAC;gBAAS;kBAAAtC,QAAA,EAAAuC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5C3I,OAAA;kBAAQkD,KAAK,EAAC,MAAM;kBAAAsF,QAAA,EAAC;gBAAW;kBAAAtC,QAAA,EAAAuC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzC3I,OAAA;kBAAQkD,KAAK,EAAC,OAAO;kBAAAsF,QAAA,EAAC;gBAAY;kBAAAtC,QAAA,EAAAuC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAzC,QAAA,EAAAuC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAzC,QAAA,EAAAuC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN3I,OAAA;cACEsI,SAAS,EAAC,mBAAmB;cAC7BC,OAAO,EAAE5C,YAAa;cACtByD,QAAQ,EAAErH,UAAU,KAAK,KAAK,IAAIJ,SAAS,KAAK,MAAM,IAAIE,aAAa,KAAK,MAAM,IAAIZ,UAAU,KAAK,EAAG;cAAAuH,QAAA,EACzG;YAED;cAAAtC,QAAA,EAAAuC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAzC,QAAA,EAAAuC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,eACN,CACH,eAED3I,OAAA;UAAKsI,SAAS,EAAC,UAAU;UAAAE,QAAA,gBACvBxI,OAAA;YAAAwI,QAAA,EAAI;UAA6B;YAAAtC,QAAA,EAAAuC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAErC9H,KAAK,gBACJb,OAAA;YAAKsI,SAAS,EAAC,eAAe;YAAAE,QAAA,EAAE3H;UAAK;YAAAqF,QAAA,EAAAuC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,GAC1ClI,IAAI,CAACqI,MAAM,KAAK,CAAC,gBACnB9I,OAAA;YAAKsI,SAAS,EAAC,aAAa;YAAAE,QAAA,GAAC,gDACmB,EAACH,kBAAkB,CAAC,CAAC;UAAA;YAAAnC,QAAA,EAAAuC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,GACJxF,YAAY,CAAC2F,MAAM,KAAK,CAAC,gBAC3B9I,OAAA;YAAKsI,SAAS,EAAC,aAAa;YAAAE,QAAA,EACzBvH,UAAU,IAAIc,UAAU,KAAK,KAAK,IAAIxB,kBAAkB,KAAK,KAAK,gBACjEP,OAAA;cAAAwI,QAAA,eACExI,OAAA;gBAAAwI,QAAA,EAAG;cAAsE;gBAAAtC,QAAA,EAAAuC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAzC,QAAA,EAAAuC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE1E,CAAC,GAEN;UACD;YAAAzC,QAAA,EAAAuC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,gBAEN3I,OAAA;YAAKsI,SAAS,EAAC,WAAW;YAAAE,QAAA,EACvBrF,YAAY,CAACkG,GAAG,CAAE9F,GAAG,iBACpBvD,OAAA;cAAkBsI,SAAS,EAAC,UAAU;cAAAE,QAAA,gBACpCxI,OAAA;gBAAKsI,SAAS,EAAC,UAAU;gBAAAE,QAAA,gBACvBxI,OAAA;kBAAAwI,QAAA,EAAK3I,YAAY,CAAC0D,GAAG,CAACQ,KAAK;gBAAC;kBAAAmC,QAAA,EAAAuC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClC3I,OAAA;kBAAKsI,SAAS,EAAC,aAAa;kBAAAE,QAAA,eAC1BxI,OAAA;oBAAMsI,SAAS,EAAC,YAAY;oBAAAE,QAAA,EAAE5C,aAAa,CAACrC,GAAG;kBAAC;oBAAA2C,QAAA,EAAAuC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAzC,QAAA,EAAAuC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAErD,CAAC;cAAA;gBAAAzC,QAAA,EAAAuC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN3I,OAAA;gBAAKsI,SAAS,EAAC,UAAU;gBAAAE,QAAA,gBACvBxI,OAAA;kBAAMsI,SAAS,EAAC,cAAc;kBAAAE,QAAA,GAAC,gBAAc,EAAC1I,UAAU,CAACyD,GAAG,CAACuB,UAAU,CAAC;gBAAA;kBAAAoB,QAAA,EAAAuC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChF3I,OAAA;kBAAKsI,SAAS,EAAC,aAAa;kBAAAE,QAAA,GACzB,CAACjF,GAAG,CAAC+F,eAAe,iBACnBtJ,OAAA;oBACEsI,SAAS,EAAC,gBAAgB;oBAC1BC,OAAO,EAAEA,CAAA,KAAMvC,iBAAiB,CAACzC,GAAG,CAACsE,EAAE,EAAEtE,GAAG,CAACQ,KAAK,CAAE;oBACpDqF,QAAQ,EAAErI,aAAa,KAAKwC,GAAG,CAACsE,EAAG;oBACnC,gBAAa,cAAc;oBAAAW,QAAA,EAE1BzH,aAAa,KAAKwC,GAAG,CAACsE,EAAE,gBACvB7H,OAAA,CAACL,cAAc;sBAACoJ,IAAI,EAAC;oBAAO;sBAAA7C,QAAA,EAAAuC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAE/B3I,OAAA;sBAAKsI,SAAS,EAAC,WAAW;sBAACiB,KAAK,EAAC,IAAI;sBAACC,MAAM,EAAC,IAAI;sBAACC,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,MAAM;sBAACC,KAAK,EAAC,4BAA4B;sBAAAnB,QAAA,gBAClHxI,OAAA;wBAAM4J,CAAC,EAAC,eAAe;wBAACC,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC,KAAK;wBAACC,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC;sBAAO;wBAAA9D,QAAA,EAAAuC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eACzG3I,OAAA;wBAAM4J,CAAC,EAAC,sBAAsB;wBAACC,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC,KAAK;wBAACC,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC;sBAAO;wBAAA9D,QAAA,EAAAuC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eAChH3I,OAAA;wBAAM4J,CAAC,EAAC,YAAY;wBAACC,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC,KAAK;wBAACC,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC;sBAAO;wBAAA9D,QAAA,EAAAuC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAzC,QAAA,EAAAuC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnG;kBACN;oBAAAzC,QAAA,EAAAuC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CACT,eAED3I,OAAA;oBACEsI,SAAS,EAAC,kBAAkB;oBAC5BC,OAAO,EAAEA,CAAA,KAAMZ,iBAAiB,CAACpE,GAAG,CAAE;oBACtC6F,QAAQ,EAAEjI,UAAU,KAAKoC,GAAG,CAACsE,EAAG;oBAChC,gBAAa,YAAY;oBAAAW,QAAA,EAExBrH,UAAU,KAAKoC,GAAG,CAACsE,EAAE,gBACpB7H,OAAA,CAACL,cAAc;sBAACoJ,IAAI,EAAC;oBAAO;sBAAA7C,QAAA,EAAAuC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAE/B3I,OAAA;sBAAKsI,SAAS,EAAC,aAAa;sBAACiB,KAAK,EAAC,IAAI;sBAACC,MAAM,EAAC,IAAI;sBAACC,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,MAAM;sBAACC,KAAK,EAAC,4BAA4B;sBAAAnB,QAAA,gBACpHxI,OAAA;wBAAM4J,CAAC,EAAC,iBAAiB;wBAACC,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC,KAAK;wBAACC,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC;sBAAO;wBAAA9D,QAAA,EAAAuC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eAC3G3I,OAAA;wBAAM4J,CAAC,EAAC,gcAAgc;wBAACC,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC,KAAK;wBAACC,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC;sBAAO;wBAAA9D,QAAA,EAAAuC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAzC,QAAA,EAAAuC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvhB;kBACN;oBAAAzC,QAAA,EAAAuC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC,eACT3I,OAAA;oBACEsI,SAAS,EAAC,gBAAgB;oBAC1B,gBAAa,UAAU;oBACvBc,QAAQ,EAAE,IAAK;oBAAAZ,QAAA,eAEfxI,OAAA;sBAAKsI,SAAS,EAAC,WAAW;sBAACiB,KAAK,EAAC,IAAI;sBAACC,MAAM,EAAC,IAAI;sBAACC,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,MAAM;sBAACC,KAAK,EAAC,4BAA4B;sBAAAnB,QAAA,eAClHxI,OAAA;wBAAM4J,CAAC,EAAC,+BAA+B;wBAACC,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC,KAAK;wBAACC,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC;sBAAO;wBAAA9D,QAAA,EAAAuC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAzC,QAAA,EAAAuC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtH;kBAAC;oBAAAzC,QAAA,EAAAuC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAzC,QAAA,EAAAuC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAzC,QAAA,EAAAuC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAvDEpF,GAAG,CAACsE,EAAE;cAAA3B,QAAA,EAAAuC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwDX,CACN;UAAC;YAAAzC,QAAA,EAAAuC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAzC,QAAA,EAAAuC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA,eACN,CACH,eAED3I,OAAA,CAACX,KAAK;QACJ4K,MAAM,EAAE5J,WAAY;QACpB6J,OAAO,EAAEA,CAAA,KAAM5J,cAAc,CAAC,KAAK,CAAE;QACrC6J,mBAAmB,EAAE,IAAK;QAC1BC,YAAY,EAAE3I,WAAY;QAAA+G,QAAA,eAE1BxI,OAAA,CAACV,MAAM;UACL4K,OAAO,EAAEA,CAAA,KAAM5J,cAAc,CAAC,KAAK,CAAE;UACrC+J,SAAS,EAAG9G,GAAG,IAAK;YAClBsC,sBAAsB,CAACtC,GAAG,CAAC;YAC3BsF,iBAAiB,CAAC,CAAC;UACrB,CAAE;UACFyB,OAAO,EAAEA,CAAA,KAAM;YACbvE,oBAAoB,CAAC,CAAC;YACtB8C,iBAAiB,CAAC,CAAC;UACrB,CAAE;UACF0B,cAAc,EAAE3B,kBAAmB;UACnC4B,WAAW,EAAE1H;QAAgB;UAAAoD,QAAA,EAAAuC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B;MAAC;QAAAzC,QAAA,EAAAuC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAER3I,OAAA,CAACJ,iBAAiB;QAChBqK,MAAM,EAAE5I,sBAAuB;QAC/B6I,OAAO,EAAEA,CAAA,KAAM5I,yBAAyB,CAAC,KAAK,CAAE;QAChDmJ,SAAS,EAAE7C,eAAgB;QAC3B7D,KAAK,EAAC,YAAY;QAClB2G,OAAO,EAAE,oCAAoCnJ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEwC,KAAK,kCAAmC;QAClG4G,WAAW,EAAC,QAAQ;QACpBC,UAAU,EAAC;MAAQ;QAAA1E,QAAA,EAAAuC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC;IAAA;MAAAzC,QAAA,EAAAuC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACN3I,OAAA,CAACP,cAAc;MACboL,QAAQ,EAAC,WAAW;MACpBC,SAAS,EAAE,IAAK;MAChBC,eAAe,EAAE,KAAM;MACvBC,WAAW;MACXC,YAAY;MACZC,GAAG,EAAE,KAAM;MACXC,gBAAgB;MAChBC,SAAS;MACTC,YAAY;IAAA;MAAAnF,QAAA,EAAAuC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAAA;IAAAzC,QAAA,EAAAuC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACvI,EAAA,CA3gBID,UAAU;AAAAmL,EAAA,GAAVnL,UAAU;AA6gBhB,eAAeA,UAAU;AAAC,IAAAmL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}