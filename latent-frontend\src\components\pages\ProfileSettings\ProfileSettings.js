import React, { useState, useEffect } from 'react';
import userService from '../../../services/userService';
import Navigation from '../../common/Navigation/Navigation';
import './ProfileSettings.css';
import apiService from '../../../services/api';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import API_URLS from '../../../config/apiUrls';
import LoadingSpinner from '../../common/LoadingSpinner/LoadingSpinner';
import { adminOnly } from '../../../constants/constants';
import { sanitizeText } from '../../../utils/sanitize';
import supabase from '../../../supabase';

const ProfileSettings = () => {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    department: '',
    designation: '',
    organizationName: '',
    industry: '',
    subIndustry: '',
    standards:''
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [regulations, setRegulations] = useState([]);
  const [selectedRegulations, setSelectedRegulations] = useState([]);
  const [regulationsLoading, setRegulationsLoading] = useState(false);
  const [regulationsError, setRegulationsError] = useState(null);
  const [originalFormData, setOriginalFormData] = useState(null);
  const [originalRegulations, setOriginalRegulations] = useState([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [regulationsDataReady, setRegulationsDataReady] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  // Helper function to compare arrays
  const arraysEqual = (a, b) => {
    if (a.length !== b.length) return false;
    const sortedA = [...a].sort();
    const sortedB = [...b].sort();
    return sortedA.every((val, idx) => val === sortedB[idx]);
  };

  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        setLoading(true);
        const userData = await userService.getUserProfile();
        
        if (userData && userData.length > 0) {
          const user = userData[0];
          const dummyData = {
            department:'',
            standards:'',
            organizationName:'',
            industry:'',
            subIndustry:''
          }
          
          // Create the form data object
          const newFormData = {
            fullName: `${user.first_name || ''} ${user.last_name || ''}`.trim(),
            email: user.email || '',
            department: user.department || dummyData.department,
            designation: user.org_role || '',
            organizationName: user.organization.name || dummyData.organizationName,
            industry: user.industry || dummyData.industry,
            subIndustry: user.sub_industry || dummyData.subIndustry,
            standards: user.standards || dummyData.standards
          };
          
          // Update form data
          setFormData(newFormData);
          
          // Store original form data for comparison
          setOriginalFormData(JSON.stringify(newFormData));
        }
      } catch (err) {
        console.error('Error fetching user profile:', err);
        
        let errorMessage = 'Failed to load user profile. Please try again later.';
        if (err?.response?.data?.detail) {
          errorMessage = err?.response?.data?.detail;
        }
        
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchUserProfile();
  }, []);

  useEffect(() => {
    const fetchRegulations = async () => {
      try {
        setRegulationsLoading(true);
        setRegulationsError(null);
        
        // Fetch both regulation data sets in parallel
        const [allRegulations, orgRegulations] = await Promise.all([
          apiService.get(API_URLS.REGULATIONS.LIST),
          apiService.get(API_URLS.REGULATIONS.ORGANIZATION)
        ]);
        
        // Set all regulations
        setRegulations(allRegulations || []);
        
        // Map the organization regulations to IDs for easy checking
        const selectedIds = orgRegulations.map(reg => reg.regulation_id);
        setSelectedRegulations(selectedIds);
        
        // Store original regulations for comparison
        setOriginalRegulations([...selectedIds]);
        
        // Set data as ready
        setRegulationsDataReady(true);
        
      } catch (error) {
        console.error('Error fetching regulations:', error);
        
        let errorMessage = 'Failed to load regulations. Please try again.';
        if (error?.response?.data?.detail) {
          errorMessage = error?.response?.data?.detail;
        }
        
        setRegulationsError(errorMessage);
      } finally {
        setRegulationsLoading(false);
      }
    };
    
    fetchRegulations();
  }, []);

  useEffect(() => {
    // Check if form data has changed
    const formDataChanged = originalFormData && JSON.stringify(formData) !== originalFormData;
    
    // Check if regulations have changed
    const regulationsChanged = originalRegulations && !arraysEqual(selectedRegulations, originalRegulations);
    
    // Update hasChanges state
    setHasChanges(formDataChanged || regulationsChanged);
  }, [formData, selectedRegulations, originalFormData, originalRegulations]);

  useEffect(() => {
    const checkAdmin = async () => {
      try {
        const userData = await userService.getUserProfile();
        if (userData && userData.length > 0) {
          const user = userData[0];
          setIsAdmin(user.org_role === adminOnly);
        }
      } catch (err) {
        console.error('Error checking admin status:', err);
        
        let errorMessage = 'Failed to check admin status. Please try again later.';
        if (err?.response?.data?.detail) {
          errorMessage = err?.response?.data?.detail;
        }
        
        setError(errorMessage);
      }
    };

    checkAdmin();
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleRegulationToggle = (regulationId) => {
    // Only allow admin users to toggle regulations
    if (!isAdmin) return;
    
    setSelectedRegulations(prev => {
      if (prev.includes(regulationId)) {
        return prev.filter(id => id !== regulationId);
      } else {
        return [...prev, regulationId];
      }
    });
  };

  const handleSaveChanges = async () => {
    try {
      setLoading(true);
      
      // Sanitize form data before submission
      const sanitizedFormData = {
        ...formData,
        fullName: sanitizeText(formData.fullName),
        department: sanitizeText(formData.department),
        designation: sanitizeText(formData.designation),
        industry: sanitizeText(formData.industry),
        subIndustry: sanitizeText(formData.subIndustry),
        // Sanitize any other text fields
      };
      
      // Get the current user ID from Supabase
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;
      
      if (!userId) {
        throw new Error('User ID not found');
      }
      
      // Split the full name into first and last name
      const nameParts = sanitizedFormData.fullName.trim().split(/\s+/);
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';
      
      // Prepare data for API
      const updateData = {
        first_name: firstName,
        last_name: lastName,
      };
      
      // Get the current user data to access existing metadata
      const currentUserData = await userService.getUserProfile();
      const currentUser = currentUserData && currentUserData.length > 0 ? currentUserData[0] : null;
      
      // Handle metadata for standards
      if (sanitizedFormData.standards && sanitizedFormData.standards.trim() !== '') {
        // Initialize metadata object if it doesn't exist
        if (!updateData.metadata) {
          updateData.metadata = {};
        }
        
        // Get existing user_recommended_compliances as an array or initialize as empty array
        let existingCompliances = [];
        
        if (currentUser && 
            currentUser.metadata && 
            currentUser.metadata.user_recommended_compliances) {
          // If it's already an array, use it
          if (Array.isArray(currentUser.metadata.user_recommended_compliances)) {
            existingCompliances = currentUser.metadata.user_recommended_compliances;
          } 
          // If it's a string, convert to array with one item
          else if (typeof currentUser.metadata.user_recommended_compliances === 'string') {
            existingCompliances = [currentUser.metadata.user_recommended_compliances];
          }
        }
        
        // Add the new compliance if it's not already in the array
        if (!existingCompliances.includes(sanitizedFormData.standards.trim())) {
          existingCompliances.push(sanitizedFormData.standards.trim());
        }
        
        // Update the metadata with the combined array
        updateData.metadata.user_recommended_compliances = existingCompliances;
      }
      
      console.log('Updating user profile with data:', updateData);
      
      // Update user profile
      const response = await userService.updateUserProfile(updateData, userId);
      
      // Handle regulation changes
      const regulationsToAdd = selectedRegulations.filter(id => !originalRegulations.includes(id));
      const regulationsToRemove = originalRegulations.filter(id => !selectedRegulations.includes(id));
      
      // Process regulation additions
      for (const regulationId of regulationsToAdd) {
        await apiService.post(API_URLS.REGULATIONS.ORGANIZATION + `/${regulationId}`);
      }
      
      // Process regulation removals
      for (const regulationId of regulationsToRemove) {
        await apiService.delete(API_URLS.REGULATIONS.ORGANIZATION + `/${regulationId}`);
      }
      
      console.log('Profile update response:', response);
      
      // Show success message with more details
      toast.success('Profile updated successfully!', {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
      
      // Update the original form data and regulations to reflect the saved state
      setOriginalFormData(JSON.stringify(sanitizedFormData));
      setOriginalRegulations([...selectedRegulations]);
      
      // Clear the standards input after successful update
      setFormData(prev => ({
        ...prev,
        standards: ''
      }));
      
    } catch (err) {
      console.error('Error updating profile:', err);
      
      let errorMessage = 'Failed to update profile. Please try again.';
      if (err?.response?.data?.detail) {
        errorMessage = err?.response?.data?.detail;
      }
      
      setError(errorMessage);
      
      // Show detailed error message
      toast.error(`Failed to update profile: ${errorMessage}`, {
        position: "top-right",
        autoClose: 7000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading && !formData.email) {
    return <div className="loading-indicator">Loading profile data...</div>;
  }

  return (
    <>
      <Navigation />
     <ToastContainer />
      <div className="profile-settings">
        {error && <div className="error-message">{error}</div>}

        <section className="settings-section">
          <h1>User Profile Settings</h1>
          <h2>Basic Information</h2>
          <div className="form-group">
            <label>Full Name</label>
            <input
              type="text"
              name="fullName"
              placeholder="Enter your full name"
              value={formData.fullName}
              onChange={handleInputChange}
            />
          </div>

          <div className="form-group">
            <label>Email</label>
            <input
              type="email"
              name="email"
              placeholder="Enter user's email address"
              value={formData.email}
              onChange={handleInputChange}
              disabled
            />
          </div>

          <div className="form-row">
            <div className="form-group">
              <label>Department</label>
              <input
                type="text"
                name="department"
                placeholder="Select department"
                value={formData.department}
                onChange={handleInputChange}
              />
            </div>
            <div className="form-group">
              <label>Designation/Role</label>
              <input
                type="text"
                name="designation"
                placeholder="Enter user's role"
                value={formData.designation}
                onChange={handleInputChange}
                disabled
              />
            </div>
          </div>
        </section>

        <section className="settings-section">
          <h2>Organization Details</h2>
          <div className="form-group">
            <label>Organization Name</label>
            <input
              type="text"
              name="organizationName"
              placeholder="Enter organization name"
              value={formData.organizationName}
              onChange={handleInputChange}
              disabled
            />
          </div>

          <div className="form-row">
            <div className="form-group">
              <label>Industry</label>
              <input
                type="text"
                name="industry"
                placeholder="Enter industry"
                value={formData.industry}
                onChange={handleInputChange}
              />
            </div>
            <div className="form-group">
              <label>Sub-Industry</label>
              <input
                type="text"
                name="subIndustry"
                placeholder="Enter sub-industry"
                value={formData.subIndustry}
                onChange={handleInputChange}
              />
            </div>
          </div>
        </section>

        <section className="settings-section">
          <div className="section-header">
            <h3 className="regulations-heading">
              Regulations List
              {!isAdmin && <span className="admin-only-badge">Admin only</span>}
            </h3>
            {regulationsLoading && <LoadingSpinner size="small" />}
          </div>
          
          {!isAdmin && (
            <div className="admin-notice">
              <p>Only administrators can modify organization regulations. Contact your administrator for changes.</p>
            </div>
          )}
          
          {regulationsError ? (
            <div className="error-message">{regulationsError}</div>
          ) : !regulationsDataReady ? (
            <div className="loading-message">Loading regulations data...</div>
          ) : (
            <div className="regulations-list">
              {regulations.length === 0 ? (
                <p>No regulations available.</p>
              ) : (
                regulations.map(regulation => (
                  <div 
                    key={regulation.id} 
                    className={`regulation-item ${!isAdmin ? 'disabled-regulation' : ''}`}
                  >
                    <label className={`checkbox-container ${!isAdmin ? 'disabled-checkbox' : ''}`}>
                      <input
                        type="checkbox"
                        checked={selectedRegulations.includes(regulation.id)}
                        onChange={() => handleRegulationToggle(regulation.id)}
                        disabled={!isAdmin}
                      />
                      <span className={`checkmark ${!isAdmin ? 'disabled' : ''}`}></span>
                      <span className="regulation-name">{regulation.name}</span>
                    </label>
                  </div>
                ))
              )}
            </div>
          )}
        </section>

        <section className="settings-section">
          <h2>Recommend Compliances To Be Added</h2>
          <div className="form-group">
            {/* <label>GXP Compliance Standards</label> */}
            <input
              type="text"
              name="standards"
              placeholder="Add applicable standards"
              value={formData.standards}
              onChange={handleInputChange}
            />
          </div>
       
          <div className="form-actions">
            <button 
              className="btn-cancel"
              onClick={() => window.history.back()}
              disabled={loading}
            >
              Cancel
            </button>
            <button 
              className="btn-save" 
              onClick={handleSaveChanges}
              disabled={loading || !hasChanges}
            >
              {loading ? (
                <span className="button-loading">
                  <LoadingSpinner size="small" />
                  Saving...
                </span>
              ) : (
                'Save Changes'
              )}
            </button>
          </div>
        </section>

        
      </div>
    </>
  );
};

export default ProfileSettings; 