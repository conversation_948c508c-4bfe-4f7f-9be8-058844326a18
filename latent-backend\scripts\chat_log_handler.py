import os
import gzip
import shutil
import logging
from logging.handlers import RotatingFileHandler
import threading
from datetime import datetime
from azure.storage.blob import BlobServiceClient

# Import environment variables  
AZURE_STORAGE_CONNECTION_STRING = os.getenv("AZURE_STORAGE_CONNECTION_STRING")
AZURE_STORAGE_CONTAINER = os.getenv("AZURE_STORAGE_CONTAINER", "logs")
AZURE_STORAGE_BLOB_PREFIX = "chatbot-logs"

class AzureBlobRotatingHandler(RotatingFileHandler):
    """
    A log handler that extends RotatingFileHandler to automatically upload
    rotated logs to Azure Blob Storage.
    """
    
    def __init__(
        self,
        filename,
        max_bytes=10*1024*1024, # 10MB
        backup_count=3,
        encoding=None,
        delay=False,
        conn_string=None,
        container_name=None,
        blob_prefix=None
    ):
        """Initialize the handler with Azure Blob Storage parameters."""
        # Initialize the parent RotatingFileHandler with UTF-8 encoding
        super().__init__(
            filename,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8',  
            delay=delay
        )
        
        # Azure Blob Storage configuration
        self.conn_string = conn_string or AZURE_STORAGE_CONNECTION_STRING
        self.container_name = container_name or AZURE_STORAGE_CONTAINER
        self.blob_prefix = blob_prefix or AZURE_STORAGE_BLOB_PREFIX
        
        # Create a logger for the handler itself
        self.handler_logger = logging.getLogger("AzureBlobRotatingHandler")
    
    def doRollover(self):
        """
        Override the RotatingFileHandler's doRollover method to add Azure upload functionality.
        """
        # Get the name of the file that will be rotated
        if self.stream:
            self.stream.close()
            self.stream = None  # type: ignore

        # Get rotated filename before rotation occurs
        rotated_filename = self.baseFilename + ".1"
        
        # Call parent class method to perform the actual rotation
        super().doRollover()
        
        # Upload the rotated file in a background thread
        thread = threading.Thread(
            target=self._upload_to_azure,
            args=(rotated_filename,),
            daemon=True
        )
        thread.start()

    def _upload_to_azure(self, log_file):
        """Upload the rotated log file to Azure Blob Storage"""
        try:
            if not os.path.exists(log_file):
                self.handler_logger.error(f"Rotated log file not found: {log_file}")
                return
            
            # Compress the log file with gzip
            compressed_file = f"{log_file}.gz"
            try:
                with open(log_file, 'rb') as f_in:
                    with gzip.open(compressed_file, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
            except Exception as e:
                self.handler_logger.error(f"Failed to compress log file: {e}")
                return
            
            # Format: app-log_MM-DD-YYYY_HHMMSS.gz
            current_date = datetime.now()
            date_str = current_date.strftime("%m-%d-%Y")
            time_str = current_date.strftime("%H%M%S")
            
            # Extract base name without path and extension
            base_name = os.path.splitext(os.path.basename(self.baseFilename))[0]
            blob_name = f"{self.blob_prefix}/{base_name}_{date_str}_{time_str}.gz"
            
            # Upload to Azure Blob Storage
            if not self.conn_string:
                self.handler_logger.error("Azure connection string is None")
                return
                
            blob_service_client = BlobServiceClient.from_connection_string(self.conn_string)
            blob_client = blob_service_client.get_blob_client(
                container=self.container_name,
                blob=blob_name
            )
            
            with open(compressed_file, "rb") as data:
                blob_client.upload_blob(data)
            
            # Clean up the compressed file after upload
            os.remove(compressed_file)
            self.handler_logger.info(f"Successfully uploaded log to Azure as {blob_name}")
            
        except Exception as e:
            self.handler_logger.error(f"Error uploading log to Azure: {str(e)}")


def setup_logging(
    name="app",
    log_dir="logs",
    log_level=logging.INFO,
    max_bytes=5*1024*1024,  # 5MB
    backup_count=3
):
    # Disable all logging to the terminal by removing handlers from the root logger
    logging.getLogger().handlers = []
     
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(log_level)
    
    # Clear any existing handlers to avoid duplicates
    logger.handlers = []
    
    # Ensure log directory exists
    os.makedirs(log_dir, exist_ok=True)
    
    # Log file path
    log_file_path = os.path.join(log_dir, f"{name}.log")
    
    # Create and add file handler with Azure support
    file_handler = AzureBlobRotatingHandler(
        filename=log_file_path,
        max_bytes=max_bytes,
        backup_count=backup_count,
        conn_string=AZURE_STORAGE_CONNECTION_STRING,
        container_name=AZURE_STORAGE_CONTAINER,
        blob_prefix=AZURE_STORAGE_BLOB_PREFIX
    )
    
    # Add console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    
    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # Add handlers to logger
    logger.addHandler(file_handler)
    #logger.addHandler(console_handler)
    
    logger.propagate = False
    # Also disable root logger handlers to prevent any console output
    logging.getLogger().handlers = []
    
    return logger