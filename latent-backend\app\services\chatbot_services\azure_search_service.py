from azure.search.documents import SearchClient
from azure.search.documents.models import VectorizedQuery
from azure.core.credentials import AzureKeyCredential
from app.models.chatbot_models import DocumentChunk
from typing import List, cast, Optional, Dict
from dotenv import load_dotenv
load_dotenv()

import logging
import os

logger = logging.getLogger(__name__)

# Environment variables at the top
AZURE_SEARCH_ENDPOINT = os.getenv("AZURE_SEARCH_ENDPOINT")
AZURE_SEARCH_API_KEY = os.getenv("AZURE_SEARCH_API_KEY") 
AZURE_SEARCH_INDEX_NAME = os.getenv("AZURE_SEARCH_INDEX_NAME", "documents-index")

def get_search_client():
    """Initialize and return Azure Search client"""
    
    if not AZURE_SEARCH_ENDPOINT:
        raise ValueError("AZURE_SEARCH_ENDPOINT is not set.")
    if not AZURE_SEARCH_API_KEY:
        raise ValueError("AZURE_SEARCH_API_KEY is not set.")
    
    #logger.info("Creating SearchClient...")
    client = SearchClient(
        endpoint=AZURE_SEARCH_ENDPOINT,
        index_name=AZURE_SEARCH_INDEX_NAME,
        credential=AzureKeyCredential(AZURE_SEARCH_API_KEY)
    )
    return client

async def search_documents(
    query_vector: List[float], 
    user_id: str = "", 
    top_k: int = 5, 
    titles: List[str] = [], 
    blob_to_title_mapping: Optional[Dict[str, str]] = None
) -> List[DocumentChunk]:
    """
    Search for documents using vector similarity
    
    Args:
        query_vector: Vector representation of the query
        user_id: User identifier for filtering
        top_k: Number of results to return
        titles: List of document titles to filter by
        blob_to_title_mapping: Mapping from blob names to display titles
        
    Returns:
        List of matching document chunks
    """
    try:
        search_client = get_search_client()
        
        #logger.info(f"Searching documents with query_vector (first 10): {query_vector[:10]}..., top_k: {top_k}, titles: {titles}")
        
        # Create vector query
        vector_query = VectorizedQuery(
            kind="vector", 
            vector=query_vector, 
            k_nearest_neighbors=top_k, 
            fields="text_vector"
        )
        
        # Create search options
        search_options = {
            "select": ["chunk_id", "chunk", "parent_id", "title"],
            "top": top_k
        }
        
        # Only filter by allowed titles if provided
        if titles and len(titles) > 0:
            # Escape single quotes in titles for OData filter
            safe_titles = [t.replace("'", "''") for t in titles]
            titles_filter = " or ".join([f"title eq '{t}'" for t in safe_titles])
            search_options["filter"] = titles_filter
            logger.info(f"Applying titles filter: {titles_filter}")
        
        # Perform vector search
        #logger.info(f"Search options: {search_options}")
        results = search_client.search(
            search_text="*",
            vector_queries=[vector_query],
            **search_options
        )
        
        documents = []
        for result in results:
            blob_file_name = result.get("title", "Unknown Document")
            doc_name = blob_file_name
            if blob_to_title_mapping and blob_file_name in blob_to_title_mapping:
                doc_name = blob_to_title_mapping[blob_file_name]
            
            doc_chunk = DocumentChunk(
                id=result.get("chunk_id", ""),
                content=result.get("chunk", ""),
                doc_id=result.get("parent_id", ""),
                doc_name=doc_name,
                score=result.get("@search.score"),
                metadata={}
            )
            documents.append(doc_chunk)
            
        #logger.info(f"Retrieved {len(documents)} documents from Azure AI Search.")
        #logger.info(f"Retrieved docs : {documents[:3]}")
        return documents
        
    except Exception as e:
        logger.error(f"Error searching documents: {str(e)}", exc_info=True)
        raise Exception(f"Search failed: {str(e)}")

class AzureSearchService:
    """
    Legacy class wrapper for backward compatibility
    Delegates to the functional approach above
    """
    def __init__(self):
        pass
    
    async def search_documents(self, query_vector: List[float], user_id: str = "", top_k: int = 5, titles: List[str] = [], blob_to_title_mapping: Optional[Dict[str, str]] = None) -> List[DocumentChunk]:
        return await search_documents(query_vector, user_id, top_k, titles, blob_to_title_mapping)