import {User} from "lucide-react";
import "./QueryResponse.css";

const QueryResponse = ({ query, response, timestamp }) => {
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return "Unknown time";
    try {
      return new Date(timestamp).toLocaleString();
    } catch {
      return timestamp;
    }
  };

  return (
    <div className="query-response-container">
      {/* User Query */}
      <div className="chat-entry  chat-entry-user">
        <div className="chat-avatar user-avatar">
          <User className="chat-icon user-icon" />
        </div>
        <div className="chat-content">
          <div className="chat-bubble user-bubble">
            <p>{query}</p>
          </div>
          <div className="chat-timestamp">
             
            <span>{formatTimestamp(timestamp)}</span>
          </div>
        </div>
      </div>

      {/* AI Response */}
      <div className="chat-entry chat-entry-ai">
        <div className="chat-avatar ai-avatar">
           <img src="/logoSquare.png" alt="Latent AI" className="ai-logo" />
        </div>
        <div className="chat-content">
          <div className="chat-bubble ai-bubble">
            <h3 className="response-heading">Response</h3>
            <p className="response-text">{response.answer}</p>
          </div>

          {/* Sources (if needed in future) */}
          {/* {response.sources && response.sources.length > 0 && (
            <div className="sources-section">
              <h3 className="sources-heading">
                <FileText className="source-icon" />
                Sources ({response.total_sources})
              </h3>
              <div className="source-list">
                {response.sources.map((source, index) => (
                  <div key={source.id || index} className="source-entry">
                    <div className="source-header">
                      <h4 className="source-title">{source.doc_name}</h4>
                      {source.score && (
                        <span className="source-score">
                          Score: {source.score.toFixed(3)}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )} */}
        </div>
      </div>
    </div>
  );
};

export default QueryResponse;