.query-input-form {
    display: flex;
    gap: 0.5rem;
    padding-left: 3rem;
    padding-right: 3rem;
    padding-top: 1rem;
    padding-bottom: 1rem;
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    width: 90%;
    max-width: 70%;
     
}
 
  
  .query-input-field {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    outline: none;
    font-size: 1rem;
  }
  
  .query-input-field:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 2px var(--primary-blue-hover);
  }
  
  .query-input-button {
    padding: 0.75rem 1rem;
    background-color: var(--primary-blue);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: background-color 0.2s ease-in-out;
    cursor: pointer;
    white-space: nowrap;
    flex-shrink: 0;
    opacity: 1 !important; 
     
  }
  
  .query-input-button:hover:not(:disabled) {
    background-color: #5a52d4;
  }
  
  .query-input-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .query-icon {
    width: 1rem;
    height: 1rem;
  }
  
  .spin {
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
  
  /* Responsive design for smaller screens */
  @media (max-width: 768px) {
    .query-input-form {
      max-width: 100%;
      padding-left: 1rem;
      padding-right: 1rem;
    }
  }
  