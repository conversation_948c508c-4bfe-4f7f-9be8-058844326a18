{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\zipp\\\\latent-frontend\\\\src\\\\components\\\\common\\\\QueryResponse\\\\QueryResponse.js\";\nimport { User } from \"lucide-react\";\nimport \"./QueryResponse.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QueryResponse = ({\n  query,\n  response,\n  timestamp\n}) => {\n  const formatTimestamp = timestamp => {\n    if (!timestamp) return \"Unknown time\";\n    try {\n      return new Date(timestamp).toLocaleString();\n    } catch {\n      return timestamp;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"query-response-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-entry  chat-entry-user\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chat-avatar user-avatar\",\n        children: /*#__PURE__*/_jsxDEV(User, {\n          className: \"chat-icon user-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chat-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chat-bubble user-bubble\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: query\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chat-timestamp\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: formatTimestamp(timestamp)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-entry chat-entry-ai\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chat-avatar ai-avatar\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/logoSquare.png\",\n          alt: \"Latent AI\",\n          className: \"ai-logo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 12\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chat-content\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chat-bubble ai-bubble\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"response-heading\",\n            children: \"Response\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"response-text\",\n            children: response.answer\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_c = QueryResponse;\nexport default QueryResponse;\nvar _c;\n$RefreshReg$(_c, \"QueryResponse\");", "map": {"version": 3, "names": ["User", "jsxDEV", "_jsxDEV", "QueryResponse", "query", "response", "timestamp", "formatTimestamp", "Date", "toLocaleString", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "answer", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/components/common/QueryResponse/QueryResponse.js"], "sourcesContent": ["import {User} from \"lucide-react\";\r\nimport \"./QueryResponse.css\";\r\n\r\nconst QueryResponse = ({ query, response, timestamp }) => {\r\n  const formatTimestamp = (timestamp) => {\r\n    if (!timestamp) return \"Unknown time\";\r\n    try {\r\n      return new Date(timestamp).toLocaleString();\r\n    } catch {\r\n      return timestamp;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"query-response-container\">\r\n      {/* User Query */}\r\n      <div className=\"chat-entry  chat-entry-user\">\r\n        <div className=\"chat-avatar user-avatar\">\r\n          <User className=\"chat-icon user-icon\" />\r\n        </div>\r\n        <div className=\"chat-content\">\r\n          <div className=\"chat-bubble user-bubble\">\r\n            <p>{query}</p>\r\n          </div>\r\n          <div className=\"chat-timestamp\">\r\n             \r\n            <span>{formatTimestamp(timestamp)}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* AI Response */}\r\n      <div className=\"chat-entry chat-entry-ai\">\r\n        <div className=\"chat-avatar ai-avatar\">\r\n           <img src=\"/logoSquare.png\" alt=\"Latent AI\" className=\"ai-logo\" />\r\n        </div>\r\n        <div className=\"chat-content\">\r\n          <div className=\"chat-bubble ai-bubble\">\r\n            <h3 className=\"response-heading\">Response</h3>\r\n            <p className=\"response-text\">{response.answer}</p>\r\n          </div>\r\n\r\n          {/* Sources (if needed in future) */}\r\n          {/* {response.sources && response.sources.length > 0 && (\r\n            <div className=\"sources-section\">\r\n              <h3 className=\"sources-heading\">\r\n                <FileText className=\"source-icon\" />\r\n                Sources ({response.total_sources})\r\n              </h3>\r\n              <div className=\"source-list\">\r\n                {response.sources.map((source, index) => (\r\n                  <div key={source.id || index} className=\"source-entry\">\r\n                    <div className=\"source-header\">\r\n                      <h4 className=\"source-title\">{source.doc_name}</h4>\r\n                      {source.score && (\r\n                        <span className=\"source-score\">\r\n                          Score: {source.score.toFixed(3)}\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          )} */}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QueryResponse;"], "mappings": ";AAAA,SAAQA,IAAI,QAAO,cAAc;AACjC,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAAU,CAAC,KAAK;EACxD,MAAMC,eAAe,GAAID,SAAS,IAAK;IACrC,IAAI,CAACA,SAAS,EAAE,OAAO,cAAc;IACrC,IAAI;MACF,OAAO,IAAIE,IAAI,CAACF,SAAS,CAAC,CAACG,cAAc,CAAC,CAAC;IAC7C,CAAC,CAAC,MAAM;MACN,OAAOH,SAAS;IAClB;EACF,CAAC;EAED,oBACEJ,OAAA;IAAKQ,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBAEvCT,OAAA;MAAKQ,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CT,OAAA;QAAKQ,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACtCT,OAAA,CAACF,IAAI;UAACU,SAAS,EAAC;QAAqB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACNb,OAAA;QAAKQ,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BT,OAAA;UAAKQ,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eACtCT,OAAA;YAAAS,QAAA,EAAIP;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACNb,OAAA;UAAKQ,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAE7BT,OAAA;YAAAS,QAAA,EAAOJ,eAAe,CAACD,SAAS;UAAC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNb,OAAA;MAAKQ,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvCT,OAAA;QAAKQ,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACnCT,OAAA;UAAKc,GAAG,EAAC,iBAAiB;UAACC,GAAG,EAAC,WAAW;UAACP,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eACNb,OAAA;QAAKQ,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BT,OAAA;UAAKQ,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpCT,OAAA;YAAIQ,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9Cb,OAAA;YAAGQ,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEN,QAAQ,CAACa;UAAM;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAyBH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACI,EAAA,GAlEIhB,aAAa;AAoEnB,eAAeA,aAAa;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}