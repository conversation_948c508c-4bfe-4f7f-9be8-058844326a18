{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\zipp\\\\latent-frontend\\\\src\\\\components\\\\pages\\\\SOPLibrary\\\\AddSOP.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport sopService from '../../../services/sopService';\nimport departmentService from '../../../services/departmentService';\nimport regulationService from '../../../services/regulationService';\nimport { toast } from 'react-toastify';\nimport LoadingSpinner from '../../common/LoadingSpinner/LoadingSpinner';\nimport './AddSOP.css';\nimport { useNavigate } from 'react-router-dom';\nimport { FaFilePdf, FaFileWord, FaFile } from 'react-icons/fa';\nimport { DummyDepartments } from '../../../constants/constants';\nimport { sanitizeText } from '../../../utils/sanitize';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AddSOP = ({\n  onClose,\n  onSuccess\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    title: '',\n    selectedDepartment: null,\n    description: '',\n    file: null\n  });\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Department state\n  const [departments, setDepartments] = useState([]);\n  const [filteredDepartments, setFilteredDepartments] = useState([]);\n  const [departmentLoading, setDepartmentLoading] = useState(true);\n  const [departmentError, setDepartmentError] = useState('');\n  const [departmentSearchTerm, setDepartmentSearchTerm] = useState('');\n  const [showDepartmentDropdown, setShowDepartmentDropdown] = useState(false);\n\n  // Compliance standards state\n  const [selectedComplianceStandards, setSelectedComplianceStandards] = useState([]);\n  const [regulations, setRegulations] = useState([]);\n  const [showRegulationDropdown, setShowRegulationDropdown] = useState(false);\n  const [regulationSearchTerm, setRegulationSearchTerm] = useState('');\n  const [filteredRegulations, setFilteredRegulations] = useState([]);\n  const [regulationLoading, setRegulationLoading] = useState(false);\n  const [regulationError, setRegulationError] = useState(null);\n  const fileInputRef = useRef(null);\n  const departmentDropdownRef = useRef(null);\n  const regulationDropdownRef = useRef(null);\n\n  // Ref for the modal content\n  const modalRef = useRef(null);\n\n  // Prevent body scroll when modal is open\n  useEffect(() => {\n    // Prevent scrolling when modal is open\n    document.body.style.overflow = 'hidden';\n\n    // Cleanup function to restore scrolling when component unmounts\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, []);\n\n  // Fetch Departments and Regulations on component mount\n  useEffect(() => {\n    const fetchDepartments = async () => {\n      try {\n        setDepartmentLoading(true);\n        setDepartmentError('');\n\n        // const response = await departmentService.getAllDepartments();\n        const response = DummyDepartments;\n        console.log('API Response:', response);\n\n        // Make sure departments is an array\n        let departmentArray = [];\n\n        // if (response && response.departments) {\n        if (response) {\n          // If response.departments is an array, use it\n          if (Array.isArray(response.departments)) {\n            // Filter out \"All\" option and map to objects with id and name\n            departmentArray = response.departments.filter(dept => dept !== 'All').map((dept, index) => ({\n              id: index.toString(),\n              name: dept\n            }));\n          }\n        }\n        console.log('Processed departments:', departmentArray);\n\n        // Set departments regardless of array length\n        setDepartments(departmentArray);\n      } catch (error) {\n        console.error('Error fetching departments:', error);\n        setDepartmentError('Failed to load departments');\n\n        // Set a minimal fallback in case of error\n        setDepartments([{\n          id: 'research',\n          name: 'Research'\n        }, {\n          id: 'other',\n          name: 'Other'\n        }]);\n      } finally {\n        // Ensure loading state is set to false\n        setDepartmentLoading(false);\n      }\n    };\n    const fetchRegulations = async () => {\n      try {\n        setRegulationLoading(true);\n        const regulationData = await regulationService.getOrganizationRegulations();\n        setRegulations(regulationData);\n        setFilteredRegulations(regulationData);\n      } catch (err) {\n        console.error('Error fetching regulations:', err);\n        setRegulationError('Failed to load compliance standards. Please try again later.');\n      } finally {\n        setRegulationLoading(false);\n      }\n    };\n    fetchDepartments();\n    fetchRegulations();\n  }, []);\n\n  // Filter Departments and Regulations based on search term\n  useEffect(() => {\n    if (departmentSearchTerm.trim() === '') {\n      setFilteredDepartments(departments);\n    } else {\n      const filtered = departments.filter(department => department.name.toLowerCase().includes(departmentSearchTerm.toLowerCase()));\n      setFilteredDepartments(filtered);\n    }\n  }, [departmentSearchTerm, departments]);\n  useEffect(() => {\n    if (regulationSearchTerm.trim() === '') {\n      setFilteredRegulations(regulations);\n    } else {\n      const filtered = regulations.filter(regulation => regulation.name.toLowerCase().includes(regulationSearchTerm.toLowerCase()));\n      setFilteredRegulations(filtered);\n    }\n  }, [regulations, regulationSearchTerm]);\n\n  // Close dropdowns when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (departmentDropdownRef.current && !departmentDropdownRef.current.contains(event.target)) {\n        setShowDepartmentDropdown(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  // Update the debug useEffect to not override our departments data\n  useEffect(() => {\n    // Log the departments data to see its structure\n    console.log('Departments data:', departments);\n\n    // Only set default if departments is truly empty\n    // if (!Array.isArray(departments) || departments.length === 0) {\n    //   // Set a minimal fallback\n    //   setDepartments([\n    //     { id: 'research', name: 'Research' },\n    //     { id: 'other', name: 'Other' }\n    //   ]);\n    // }\n\n    // Ensure selectedDepartment is null by default\n    if (formData.selectedDepartment === undefined) {\n      setFormData(prev => ({\n        ...prev,\n        selectedDepartment: null\n      }));\n    }\n  }, [departments]);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleDepartmentSearch = e => {\n    setDepartmentSearchTerm(e.target.value);\n  };\n  const selectDepartment = department => {\n    setFormData({\n      ...formData,\n      selectedDepartment: department\n    });\n    setShowDepartmentDropdown(false);\n  };\n  const toggleDepartmentDropdown = () => {\n    setShowDepartmentDropdown(!showDepartmentDropdown);\n  };\n  const handleFileChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n\n      // Create a preview URL for the file\n      if (file.type === 'application/pdf') {\n        // For PDF files, we can use the URL directly\n        const fileUrl = URL.createObjectURL(file);\n        setPreviewUrl(fileUrl);\n      } else {\n        // For other file types, we'll just show an icon\n        setPreviewUrl(null);\n      }\n    } else {\n      setSelectedFile(null);\n      setPreviewUrl(null);\n    }\n  };\n  const getFileIcon = file => {\n    if (!file) return /*#__PURE__*/_jsxDEV(FaFile, {\n      size: 48\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 23\n    }, this);\n    const fileType = file.type;\n    if (fileType === 'application/pdf') {\n      return /*#__PURE__*/_jsxDEV(FaFilePdf, {\n        size: 48,\n        color: \"#e74c3c\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 14\n      }, this);\n    } else if (fileType.includes('word') || file.name.endsWith('.doc') || file.name.endsWith('.docx')) {\n      return /*#__PURE__*/_jsxDEV(FaFileWord, {\n        size: 48,\n        color: \"#2b579a\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 14\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(FaFile, {\n        size: 48,\n        color: \"#95a5a6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 14\n      }, this);\n    }\n  };\n  const removeFile = () => {\n    setSelectedFile(null);\n    setPreviewUrl(null);\n\n    // Reset the file input\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n  const handleDragOver = e => {\n    e.preventDefault();\n    e.stopPropagation();\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      const file = e.dataTransfer.files[0];\n      setSelectedFile(file);\n\n      // Auto-fill title with filename if title is empty\n      if (!formData.title) {\n        setFormData(prev => ({\n          ...prev,\n          title: file.name.split('.')[0] // Remove file extension\n        }));\n      }\n\n      // Create preview URL for the file\n      if (file.type === 'application/pdf') {\n        // For PDF files, we can use the URL directly\n        const fileUrl = URL.createObjectURL(file);\n        setPreviewUrl(fileUrl);\n      } else {\n        // For other file types, we'll just show an icon\n        setPreviewUrl(null);\n      }\n    }\n  };\n  const handleBrowseClick = () => {\n    if (fileInputRef.current) {\n      fileInputRef.current.click();\n    } else {\n      console.error(\"File input reference is null\");\n      // Fallback method if ref is null\n      const input = document.getElementById('sop-file');\n      if (input) {\n        input.click();\n      } else {\n        toast.error(\"Couldn't access file input. Please try again.\");\n      }\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // Validate form\n    if (!formData.title.trim()) {\n      setError('Please enter a title for the SOP');\n      return;\n    }\n    if (!formData.selectedDepartment) {\n      setError('Please select a category');\n      return;\n    }\n    if (!selectedFile) {\n      setError('Please select a file to upload');\n      return;\n    }\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Sanitize the form data before submission\n      const sanitizedTitle = sanitizeText(formData.title);\n      const sanitizedDepartment = sanitizeText(formData.selectedDepartment.name);\n\n      // Create form data for file upload\n      const uploadData = new FormData();\n      uploadData.append('file', selectedFile);\n\n      // Only add these fields if they have values\n      if (sanitizedTitle) {\n        uploadData.append('title', sanitizedTitle);\n      }\n      if (sanitizedDepartment) {\n        uploadData.append('department', sanitizedDepartment);\n      }\n      if (formData.description) {\n        uploadData.append('description', formData.description);\n      }\n\n      // Add selected compliance standards\n      if (selectedComplianceStandards.length > 0) {\n        const standardIds = selectedComplianceStandards.map(standard => standard.id);\n        uploadData.append('compliance_standards', JSON.stringify(standardIds));\n      }\n\n      // Make the API call\n      const response = await sopService.uploadSOP(uploadData);\n      toast.success('SOP uploaded successfully!');\n      if (onSuccess) {\n        onSuccess();\n      }\n\n      // Close the form\n      onClose();\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Error uploading SOP:', err);\n      let errorMessage = 'Failed to upload SOP. Please try again.';\n\n      // Check if the error is related to file format\n      if (err !== null && err !== void 0 && (_err$response = err.response) !== null && _err$response !== void 0 && (_err$response$data = _err$response.data) !== null && _err$response$data !== void 0 && _err$response$data.detail) {\n        var _err$response2, _err$response2$data;\n        errorMessage = err === null || err === void 0 ? void 0 : (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.detail;\n      }\n      setError(errorMessage);\n      toast.error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Clean up preview URL when component unmounts\n  React.useEffect(() => {\n    return () => {\n      if (previewUrl) {\n        URL.revokeObjectURL(previewUrl);\n      }\n    };\n  }, [previewUrl]);\n\n  // Handle regulation selection (toggle selection)\n\n  // Handle regulation search\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"add-sop-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"add-sop-modal\",\n      ref: modalRef,\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Upload SOP Document\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"add-sop-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"title\",\n            children: \"SOP Title\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"title\",\n            name: \"title\",\n            value: formData.title,\n            onChange: handleInputChange,\n            placeholder: \"Enter SOP title\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"department\",\n            children: \"Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this), departmentLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"department-loading\",\n            children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Loading categories...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"department-dropdown-container\",\n            ref: departmentDropdownRef,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"department-selected-display\",\n              onClick: () => setShowDepartmentDropdown(!showDepartmentDropdown),\n              children: [formData.selectedDepartment ? /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"selected-value\",\n                children: formData.selectedDepartment.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"placeholder\",\n                children: \"Select a category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"dropdown-arrow\",\n                children: \"\\u25BC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 17\n            }, this), showDepartmentDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"department-dropdown\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"department-search\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Search categorie...\",\n                  value: departmentSearchTerm,\n                  onChange: e => setDepartmentSearchTerm(e.target.value),\n                  onClick: e => e.stopPropagation()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"department-options\",\n                children: [Array.isArray(departments) && departments.length > 0 ? departments.filter(dept => dept.name.toLowerCase().includes(departmentSearchTerm.toLowerCase())).map(dept => {\n                  var _formData$selectedDep;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `department-option ${((_formData$selectedDep = formData.selectedDepartment) === null || _formData$selectedDep === void 0 ? void 0 : _formData$selectedDep.id) === dept.id ? 'selected' : ''}`,\n                    onClick: () => {\n                      setFormData({\n                        ...formData,\n                        selectedDepartment: dept\n                      });\n                      setShowDepartmentDropdown(false);\n                      setDepartmentSearchTerm('');\n                    },\n                    children: dept.name\n                  }, dept.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 29\n                  }, this);\n                }) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"no-results\",\n                  children: \"No departments available\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 25\n                }, this), Array.isArray(departments) && departments.length > 0 && departments.filter(dept => dept.name.toLowerCase().includes(departmentSearchTerm.toLowerCase())).length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"no-results\",\n                  children: [\"No departments found matching \\\"\", departmentSearchTerm, \"\\\"\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"description\",\n            children: \"Description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"description\",\n            name: \"description\",\n            value: formData.description,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"file\",\n            children: \"Upload SOP Document\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 13\n          }, this), selectedFile ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"file-preview-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"file-info-bar\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: selectedFile.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"(\", (selectedFile.size / 1024).toFixed(2), \" KB)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"remove-file-btn\",\n                onClick: removeFile,\n                \"aria-label\": \"Remove file\",\n                children: \"\\xD7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 17\n            }, this), previewUrl && selectedFile.type === 'application/pdf' ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pdf-preview-container\",\n              children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n                src: previewUrl,\n                className: \"pdf-preview\",\n                title: \"PDF Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"generic-file-preview\",\n              children: [getFileIcon(selectedFile), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: selectedFile.type === 'application/pdf' ? 'PDF preview not available' : `Preview not available for ${selectedFile.name.split('.').pop().toUpperCase()} files`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"file-upload-container\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              id: \"file\",\n              ref: fileInputRef,\n              onChange: handleFileChange,\n              accept: \".pdf,.doc,.docx,.txt, .js\",\n              className: \"file-input\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"cancel-btn\",\n            onClick: onClose,\n            disabled: loading,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"submit-btn\",\n            disabled: loading,\n            children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Uploading...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : 'Upload SOP'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 403,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 402,\n    columnNumber: 5\n  }, this);\n};\n_s(AddSOP, \"yppaKe8oICaOitqElW9IFT0Q20c=\", false, function () {\n  return [useNavigate];\n});\n_c = AddSOP;\nexport default AddSOP;\nvar _c;\n$RefreshReg$(_c, \"AddSOP\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "sopService", "departmentService", "regulationService", "toast", "LoadingSpinner", "useNavigate", "FaFilePdf", "FaFileWord", "FaFile", "DummyDepartments", "sanitizeText", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddSOP", "onClose", "onSuccess", "_s", "navigate", "formData", "setFormData", "title", "selectedDepartment", "description", "file", "selectedFile", "setSelectedFile", "previewUrl", "setPreviewUrl", "loading", "setLoading", "error", "setError", "departments", "setDepartments", "filteredDepartments", "setFilteredDepartments", "departmentLoading", "setDepartmentLoading", "departmentError", "setDepartmentError", "departmentSearchTerm", "setDepartmentSearchTerm", "showDepartmentDropdown", "setShowDepartmentDropdown", "selectedComplianceStandards", "setSelectedComplianceStandards", "regulations", "setRegulations", "showRegulationDropdown", "setShowRegulationDropdown", "regulationSearchTerm", "setRegulationSearchTerm", "filteredRegulations", "setFilteredRegulations", "regulationLoading", "setRegulationLoading", "regulationError", "setRegulationError", "fileInputRef", "departmentDropdownRef", "regulationDropdownRef", "modalRef", "document", "body", "style", "overflow", "fetchDepartments", "response", "console", "log", "departmentArray", "Array", "isArray", "filter", "dept", "map", "index", "id", "toString", "name", "fetchRegulations", "regulationData", "getOrganizationRegulations", "err", "trim", "filtered", "department", "toLowerCase", "includes", "regulation", "handleClickOutside", "event", "current", "contains", "target", "addEventListener", "removeEventListener", "undefined", "prev", "handleInputChange", "e", "value", "handleDepartmentSearch", "selectDepartment", "toggleDepartmentDropdown", "handleFileChange", "files", "type", "fileUrl", "URL", "createObjectURL", "getFileIcon", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fileType", "color", "endsWith", "removeFile", "handleDragOver", "preventDefault", "stopPropagation", "handleDrop", "dataTransfer", "split", "handleBrowseClick", "click", "input", "getElementById", "handleSubmit", "sanitizedTitle", "sanitizedDepartment", "uploadData", "FormData", "append", "length", "standardIds", "standard", "JSON", "stringify", "uploadSOP", "success", "_err$response", "_err$response$data", "errorMessage", "data", "detail", "_err$response2", "_err$response2$data", "revokeObjectURL", "className", "children", "ref", "onSubmit", "htmlFor", "onChange", "placeholder", "required", "onClick", "_formData$selectedDep", "toFixed", "src", "pop", "toUpperCase", "accept", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/components/pages/SOPLibrary/AddSOP.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport sopService from '../../../services/sopService';\r\nimport departmentService from '../../../services/departmentService';\r\nimport regulationService from '../../../services/regulationService';\r\nimport { toast } from 'react-toastify';\r\nimport LoadingSpinner from '../../common/LoadingSpinner/LoadingSpinner';\r\nimport './AddSOP.css';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { FaFilePdf, FaFileWord, FaFile } from 'react-icons/fa';\r\nimport { DummyDepartments } from '../../../constants/constants';\r\nimport { sanitizeText } from '../../../utils/sanitize';\r\n\r\nconst AddSOP = ({ onClose, onSuccess }) => {\r\n  const navigate = useNavigate();\r\n  const [formData, setFormData] = useState({\r\n    title: '',\r\n    selectedDepartment: null,\r\n    description: '',\r\n    file: null\r\n  });\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n  const [previewUrl, setPreviewUrl] = useState(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  \r\n  // Department state\r\n  const [departments, setDepartments] = useState([]);\r\n  const [filteredDepartments, setFilteredDepartments] = useState([]);\r\n  const [departmentLoading, setDepartmentLoading] = useState(true);\r\n  const [departmentError, setDepartmentError] = useState('');\r\n  const [departmentSearchTerm, setDepartmentSearchTerm] = useState('');\r\n  const [showDepartmentDropdown, setShowDepartmentDropdown] = useState(false);\r\n  \r\n  // Compliance standards state\r\n  const [selectedComplianceStandards, setSelectedComplianceStandards] = useState([]);\r\n  const [regulations, setRegulations] = useState([]);\r\n  const [showRegulationDropdown, setShowRegulationDropdown] = useState(false);\r\n  const [regulationSearchTerm, setRegulationSearchTerm] = useState('');\r\n  const [filteredRegulations, setFilteredRegulations] = useState([]);\r\n  const [regulationLoading, setRegulationLoading] = useState(false);\r\n  const [regulationError, setRegulationError] = useState(null);\r\n  \r\n  const fileInputRef = useRef(null);\r\n  const departmentDropdownRef = useRef(null);\r\n  const regulationDropdownRef = useRef(null);\r\n\r\n  // Ref for the modal content\r\n  const modalRef = useRef(null);\r\n\r\n  // Prevent body scroll when modal is open\r\n  useEffect(() => {\r\n    // Prevent scrolling when modal is open\r\n    document.body.style.overflow = 'hidden';\r\n\r\n    // Cleanup function to restore scrolling when component unmounts\r\n    return () => {\r\n      document.body.style.overflow = 'unset';\r\n    };\r\n  }, []);\r\n\r\n  // Fetch Departments and Regulations on component mount\r\n  useEffect(() => {\r\n    const fetchDepartments = async () => {\r\n      try {\r\n        setDepartmentLoading(true);\r\n        setDepartmentError('');\r\n        \r\n        // const response = await departmentService.getAllDepartments();\r\n        const response = DummyDepartments;\r\n        console.log('API Response:', response);\r\n        \r\n        // Make sure departments is an array\r\n        let departmentArray = [];\r\n        \r\n        // if (response && response.departments) {\r\n          if (response) {\r\n          // If response.departments is an array, use it\r\n          if (Array.isArray(response.departments)) {\r\n            // Filter out \"All\" option and map to objects with id and name\r\n            departmentArray = response.departments\r\n              .filter(dept => dept !== 'All')\r\n              .map((dept, index) => ({\r\n                id: index.toString(),\r\n                name: dept\r\n              }));\r\n          }\r\n        }\r\n        \r\n        \r\n        console.log('Processed departments:', departmentArray);\r\n        \r\n        // Set departments regardless of array length\r\n        setDepartments(departmentArray);\r\n        \r\n      } catch (error) {\r\n        console.error('Error fetching departments:', error);\r\n        setDepartmentError('Failed to load departments');\r\n        \r\n        // Set a minimal fallback in case of error\r\n        setDepartments([\r\n          { id: 'research', name: 'Research' },\r\n          { id: 'other', name: 'Other' }\r\n        ]);\r\n      } finally {\r\n        // Ensure loading state is set to false\r\n        setDepartmentLoading(false);\r\n      }\r\n    };\r\n\r\n    const fetchRegulations = async () => {\r\n      try {\r\n        setRegulationLoading(true);\r\n        const regulationData = await regulationService.getOrganizationRegulations();\r\n        setRegulations(regulationData);\r\n        setFilteredRegulations(regulationData);\r\n      } catch (err) {\r\n        console.error('Error fetching regulations:', err);\r\n        setRegulationError('Failed to load compliance standards. Please try again later.');\r\n      } finally {\r\n        setRegulationLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchDepartments();\r\n    fetchRegulations();\r\n  }, []);\r\n\r\n  // Filter Departments and Regulations based on search term\r\n  useEffect(() => {\r\n    if (departmentSearchTerm.trim() === '') {\r\n      setFilteredDepartments(departments);\r\n    } else {\r\n      const filtered = departments.filter(department => \r\n        department.name.toLowerCase().includes(departmentSearchTerm.toLowerCase())\r\n      );\r\n      setFilteredDepartments(filtered);\r\n    }\r\n  }, [departmentSearchTerm, departments]);\r\n\r\n  useEffect(() => {\r\n    if (regulationSearchTerm.trim() === '') {\r\n      setFilteredRegulations(regulations);\r\n    } else {\r\n      const filtered = regulations.filter(regulation => \r\n        regulation.name.toLowerCase().includes(regulationSearchTerm.toLowerCase())\r\n      );\r\n      setFilteredRegulations(filtered);\r\n    }\r\n  }, [regulations, regulationSearchTerm]);\r\n\r\n  // Close dropdowns when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (departmentDropdownRef.current && !departmentDropdownRef.current.contains(event.target)) {\r\n        setShowDepartmentDropdown(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  // Update the debug useEffect to not override our departments data\r\n  useEffect(() => {\r\n    // Log the departments data to see its structure\r\n    console.log('Departments data:', departments);\r\n    \r\n    // Only set default if departments is truly empty\r\n    // if (!Array.isArray(departments) || departments.length === 0) {\r\n    //   // Set a minimal fallback\r\n    //   setDepartments([\r\n    //     { id: 'research', name: 'Research' },\r\n    //     { id: 'other', name: 'Other' }\r\n    //   ]);\r\n    // }\r\n    \r\n    // Ensure selectedDepartment is null by default\r\n    if (formData.selectedDepartment === undefined) {\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        selectedDepartment: null\r\n      }));\r\n    }\r\n  }, [departments]);\r\n\r\n\r\n\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: value\r\n    }));\r\n  };\r\n\r\n  const handleDepartmentSearch = (e) => {\r\n    setDepartmentSearchTerm(e.target.value);\r\n  };\r\n\r\n  const selectDepartment = (department) => {\r\n    setFormData({\r\n      ...formData,\r\n      selectedDepartment: department\r\n    });\r\n    setShowDepartmentDropdown(false);\r\n  };\r\n\r\n  const toggleDepartmentDropdown = () => {\r\n    setShowDepartmentDropdown(!showDepartmentDropdown);\r\n  };\r\n\r\n  const handleFileChange = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      setSelectedFile(file);\r\n      \r\n      // Create a preview URL for the file\r\n      if (file.type === 'application/pdf') {\r\n        // For PDF files, we can use the URL directly\r\n        const fileUrl = URL.createObjectURL(file);\r\n        setPreviewUrl(fileUrl);\r\n      } else {\r\n        // For other file types, we'll just show an icon\r\n        setPreviewUrl(null);\r\n      }\r\n    } else {\r\n      setSelectedFile(null);\r\n      setPreviewUrl(null);\r\n    }\r\n  };\r\n\r\n  const getFileIcon = (file) => {\r\n    if (!file) return <FaFile size={48} />;\r\n    \r\n    const fileType = file.type;\r\n    \r\n    if (fileType === 'application/pdf') {\r\n      return <FaFilePdf size={48} color=\"#e74c3c\" />;\r\n    } else if (fileType.includes('word') || file.name.endsWith('.doc') || file.name.endsWith('.docx')) {\r\n      return <FaFileWord size={48} color=\"#2b579a\" />;\r\n    } else {\r\n      return <FaFile size={48} color=\"#95a5a6\" />;\r\n    }\r\n  };\r\n\r\n  const removeFile = () => {\r\n    setSelectedFile(null);\r\n    setPreviewUrl(null);\r\n    \r\n    // Reset the file input\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  const handleDragOver = (e) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n  };\r\n\r\n  const handleDrop = (e) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    \r\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\r\n      const file = e.dataTransfer.files[0];\r\n      setSelectedFile(file);\r\n      \r\n      // Auto-fill title with filename if title is empty\r\n      if (!formData.title) {\r\n        setFormData(prev => ({\r\n          ...prev,\r\n          title: file.name.split('.')[0] // Remove file extension\r\n        }));\r\n      }\r\n      \r\n      // Create preview URL for the file\r\n      if (file.type === 'application/pdf') {\r\n        // For PDF files, we can use the URL directly\r\n        const fileUrl = URL.createObjectURL(file);\r\n        setPreviewUrl(fileUrl);\r\n      } else {\r\n        // For other file types, we'll just show an icon\r\n        setPreviewUrl(null);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleBrowseClick = () => {\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.click();\r\n    } else {\r\n      console.error(\"File input reference is null\");\r\n      // Fallback method if ref is null\r\n      const input = document.getElementById('sop-file');\r\n      if (input) {\r\n        input.click();\r\n      } else {\r\n        toast.error(\"Couldn't access file input. Please try again.\");\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    \r\n    // Validate form\r\n    if (!formData.title.trim()) {\r\n      setError('Please enter a title for the SOP');\r\n      return;\r\n    }\r\n    \r\n    if (!formData.selectedDepartment) {\r\n      setError('Please select a category');\r\n      return;\r\n    }\r\n    \r\n    if (!selectedFile) {\r\n      setError('Please select a file to upload');\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      \r\n      // Sanitize the form data before submission\r\n      const sanitizedTitle = sanitizeText(formData.title);\r\n      const sanitizedDepartment = sanitizeText(formData.selectedDepartment.name);\r\n      \r\n      // Create form data for file upload\r\n      const uploadData = new FormData();\r\n      uploadData.append('file', selectedFile);\r\n      \r\n      // Only add these fields if they have values\r\n      if (sanitizedTitle) {\r\n        uploadData.append('title', sanitizedTitle);\r\n      }\r\n      \r\n      if (sanitizedDepartment) {\r\n        uploadData.append('department', sanitizedDepartment);\r\n      }\r\n      \r\n      if (formData.description) {\r\n        uploadData.append('description', formData.description);\r\n      }\r\n      \r\n      // Add selected compliance standards\r\n      if (selectedComplianceStandards.length > 0) {\r\n        const standardIds = selectedComplianceStandards.map(standard => standard.id);\r\n        uploadData.append('compliance_standards', JSON.stringify(standardIds));\r\n      }\r\n      \r\n      // Make the API call\r\n      const response = await sopService.uploadSOP(uploadData);\r\n    \r\n      toast.success('SOP uploaded successfully!');\r\n      \r\n      if (onSuccess) {\r\n        onSuccess();\r\n      }\r\n      \r\n      // Close the form\r\n      onClose();\r\n    } catch (err) {\r\n      console.error('Error uploading SOP:', err);\r\n      \r\n      let errorMessage = 'Failed to upload SOP. Please try again.';\r\n      \r\n      // Check if the error is related to file format\r\n      if (err?.response?.data?.detail) {\r\n        errorMessage = err?.response?.data?.detail;\r\n      }\r\n      \r\n      setError(errorMessage);\r\n      \r\n      toast.error(errorMessage);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Clean up preview URL when component unmounts\r\n  React.useEffect(() => {\r\n    return () => {\r\n      if (previewUrl) {\r\n        URL.revokeObjectURL(previewUrl);\r\n      }\r\n    };\r\n  }, [previewUrl]);\r\n\r\n  // Handle regulation selection (toggle selection)\r\n\r\n\r\n  // Handle regulation search\r\n\r\n\r\n  return (\r\n    <div className=\"add-sop-container\">\r\n      <div className=\"add-sop-modal\" ref={modalRef}>\r\n        <h2>Upload SOP Document</h2>\r\n        \r\n        {error && <div className=\"error-message\">{error}</div>}\r\n        \r\n        <form onSubmit={handleSubmit} className=\"add-sop-form\">\r\n          <div className=\"form-group\">\r\n            <label htmlFor=\"title\">SOP Title</label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"title\"\r\n              name=\"title\"\r\n              value={formData.title}\r\n              onChange={handleInputChange}\r\n              placeholder=\"Enter SOP title\"\r\n              required\r\n            />\r\n          </div>\r\n          \r\n          <div className=\"form-group\">\r\n            <label htmlFor=\"department\">Category</label>\r\n            {departmentLoading ? (\r\n              <div className=\"department-loading\">\r\n                <LoadingSpinner size=\"small\" />\r\n                <span>Loading categories...</span>\r\n              </div>\r\n            ) : (\r\n              <div className=\"department-dropdown-container\" ref={departmentDropdownRef}>\r\n                <div \r\n                  className=\"department-selected-display\"\r\n                  onClick={() => setShowDepartmentDropdown(!showDepartmentDropdown)}\r\n                >\r\n                  {formData.selectedDepartment ? (\r\n                    <span className=\"selected-value\">{formData.selectedDepartment.name}</span>\r\n                  ) : (\r\n                    <span className=\"placeholder\">Select a category</span>\r\n                  )}\r\n                  <span className=\"dropdown-arrow\">▼</span>\r\n                </div>\r\n                \r\n                {showDepartmentDropdown && (\r\n                  <div className=\"department-dropdown\">\r\n                    <div className=\"department-search\">\r\n                      <input\r\n                        type=\"text\"\r\n                        placeholder=\"Search categorie...\"\r\n                        value={departmentSearchTerm}\r\n                        onChange={(e) => setDepartmentSearchTerm(e.target.value)}\r\n                        onClick={(e) => e.stopPropagation()}\r\n                      />\r\n                    </div>\r\n                    \r\n                    <div className=\"department-options\">\r\n                      {Array.isArray(departments) && departments.length > 0 ? (\r\n                        departments\r\n                          .filter(dept => \r\n                            dept.name.toLowerCase().includes(departmentSearchTerm.toLowerCase())\r\n                          )\r\n                          .map((dept) => (\r\n                            <div \r\n                              key={dept.id} \r\n                              className={`department-option ${formData.selectedDepartment?.id === dept.id ? 'selected' : ''}`}\r\n                              onClick={() => {\r\n                                setFormData({\r\n                                  ...formData,\r\n                                  selectedDepartment: dept\r\n                                });\r\n                                setShowDepartmentDropdown(false);\r\n                                setDepartmentSearchTerm('');\r\n                              }}\r\n                            >\r\n                              {dept.name}\r\n                            </div>\r\n                          ))\r\n                      ) : (\r\n                        <div className=\"no-results\">No departments available</div>\r\n                      )}\r\n                      \r\n                      {Array.isArray(departments) && departments.length > 0 && \r\n                        departments.filter(dept => \r\n                          dept.name.toLowerCase().includes(departmentSearchTerm.toLowerCase())\r\n                        ).length === 0 && (\r\n                          <div className=\"no-results\">No departments found matching \"{departmentSearchTerm}\"</div>\r\n                        )\r\n                      }\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n          \r\n          <div className=\"form-group\">\r\n            <label htmlFor=\"description\">Description</label>\r\n            <textarea\r\n              id=\"description\"\r\n              name=\"description\"\r\n              value={formData.description}\r\n              onChange={handleInputChange}\r\n\r\n              required\r\n            />\r\n          </div>\r\n          \r\n          <div className=\"form-group\">\r\n            <label htmlFor=\"file\">Upload SOP Document</label>\r\n            \r\n            {selectedFile ? (\r\n              <div className=\"file-preview-container\">\r\n                <div className=\"file-info-bar\">\r\n                  <span>{selectedFile.name}</span>\r\n                  <span>({(selectedFile.size / 1024).toFixed(2)} KB)</span>\r\n                  <button \r\n                    type=\"button\" \r\n                    className=\"remove-file-btn\"\r\n                    onClick={removeFile}\r\n                    aria-label=\"Remove file\"\r\n                  >\r\n                    ×\r\n                  </button>\r\n                </div>\r\n                \r\n                {previewUrl && selectedFile.type === 'application/pdf' ? (\r\n                  <div className=\"pdf-preview-container\">\r\n                    <iframe \r\n                      src={previewUrl} \r\n                      className=\"pdf-preview\" \r\n                      title=\"PDF Preview\"\r\n                    />\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"generic-file-preview\">\r\n                    {getFileIcon(selectedFile)}\r\n                    <p>\r\n                      {selectedFile.type === 'application/pdf' \r\n                        ? 'PDF preview not available' \r\n                        : `Preview not available for ${selectedFile.name.split('.').pop().toUpperCase()} files`}\r\n                    </p>\r\n                  </div>\r\n                )}\r\n                \r\n              \r\n              </div>\r\n            ) : (\r\n              <div className=\"file-upload-container\">\r\n                <input\r\n                  type=\"file\"\r\n                  id=\"file\"\r\n                  ref={fileInputRef}\r\n                  onChange={handleFileChange}\r\n                  accept=\".pdf,.doc,.docx,.txt, .js\"\r\n                  className=\"file-input\"\r\n                  required\r\n                />\r\n\r\n              </div>\r\n            )}\r\n          </div>\r\n          \r\n          <div className=\"form-actions\">\r\n            <button \r\n              type=\"button\" \r\n              className=\"cancel-btn\"\r\n              onClick={onClose}\r\n              disabled={loading}\r\n            >\r\n              Cancel\r\n            </button>\r\n            <button \r\n              type=\"submit\" \r\n              className=\"submit-btn\"\r\n              disabled={loading}\r\n            >\r\n              {loading ? (\r\n                <>\r\n                  <LoadingSpinner size=\"small\" />\r\n                  <span>Uploading...</span>\r\n                </>\r\n              ) : 'Upload SOP'}\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AddSOP; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAOC,UAAU,MAAM,8BAA8B;AACrD,OAAOC,iBAAiB,MAAM,qCAAqC;AACnE,OAAOC,iBAAiB,MAAM,qCAAqC;AACnE,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,cAAc,MAAM,4CAA4C;AACvE,OAAO,cAAc;AACrB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,SAAS,EAAEC,UAAU,EAAEC,MAAM,QAAQ,gBAAgB;AAC9D,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,YAAY,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvD,MAAMC,MAAM,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC;IACvCyB,KAAK,EAAE,EAAE;IACTC,kBAAkB,EAAE,IAAI;IACxBC,WAAW,EAAE,EAAE;IACfC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACyC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC6C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAAC+C,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;;EAE3E;EACA,MAAM,CAACiD,2BAA2B,EAAEC,8BAA8B,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAClF,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqD,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACuD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACyD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAAC2D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC6D,eAAe,EAAEC,kBAAkB,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EAE5D,MAAM+D,YAAY,GAAG9D,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM+D,qBAAqB,GAAG/D,MAAM,CAAC,IAAI,CAAC;EAC1C,MAAMgE,qBAAqB,GAAGhE,MAAM,CAAC,IAAI,CAAC;;EAE1C;EACA,MAAMiE,QAAQ,GAAGjE,MAAM,CAAC,IAAI,CAAC;;EAE7B;EACAC,SAAS,CAAC,MAAM;IACd;IACAiE,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;;IAEvC;IACA,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApE,SAAS,CAAC,MAAM;IACd,MAAMqE,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF7B,oBAAoB,CAAC,IAAI,CAAC;QAC1BE,kBAAkB,CAAC,EAAE,CAAC;;QAEtB;QACA,MAAM4B,QAAQ,GAAG5D,gBAAgB;QACjC6D,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEF,QAAQ,CAAC;;QAEtC;QACA,IAAIG,eAAe,GAAG,EAAE;;QAExB;QACE,IAAIH,QAAQ,EAAE;UACd;UACA,IAAII,KAAK,CAACC,OAAO,CAACL,QAAQ,CAACnC,WAAW,CAAC,EAAE;YACvC;YACAsC,eAAe,GAAGH,QAAQ,CAACnC,WAAW,CACnCyC,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,CAC9BC,GAAG,CAAC,CAACD,IAAI,EAAEE,KAAK,MAAM;cACrBC,EAAE,EAAED,KAAK,CAACE,QAAQ,CAAC,CAAC;cACpBC,IAAI,EAAEL;YACR,CAAC,CAAC,CAAC;UACP;QACF;QAGAN,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEC,eAAe,CAAC;;QAEtD;QACArC,cAAc,CAACqC,eAAe,CAAC;MAEjC,CAAC,CAAC,OAAOxC,KAAK,EAAE;QACdsC,OAAO,CAACtC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnDS,kBAAkB,CAAC,4BAA4B,CAAC;;QAEhD;QACAN,cAAc,CAAC,CACb;UAAE4C,EAAE,EAAE,UAAU;UAAEE,IAAI,EAAE;QAAW,CAAC,EACpC;UAAEF,EAAE,EAAE,OAAO;UAAEE,IAAI,EAAE;QAAQ,CAAC,CAC/B,CAAC;MACJ,CAAC,SAAS;QACR;QACA1C,oBAAoB,CAAC,KAAK,CAAC;MAC7B;IACF,CAAC;IAED,MAAM2C,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACFzB,oBAAoB,CAAC,IAAI,CAAC;QAC1B,MAAM0B,cAAc,GAAG,MAAMjF,iBAAiB,CAACkF,0BAA0B,CAAC,CAAC;QAC3EnC,cAAc,CAACkC,cAAc,CAAC;QAC9B5B,sBAAsB,CAAC4B,cAAc,CAAC;MACxC,CAAC,CAAC,OAAOE,GAAG,EAAE;QACZf,OAAO,CAACtC,KAAK,CAAC,6BAA6B,EAAEqD,GAAG,CAAC;QACjD1B,kBAAkB,CAAC,8DAA8D,CAAC;MACpF,CAAC,SAAS;QACRF,oBAAoB,CAAC,KAAK,CAAC;MAC7B;IACF,CAAC;IAEDW,gBAAgB,CAAC,CAAC;IAClBc,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnF,SAAS,CAAC,MAAM;IACd,IAAI2C,oBAAoB,CAAC4C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACtCjD,sBAAsB,CAACH,WAAW,CAAC;IACrC,CAAC,MAAM;MACL,MAAMqD,QAAQ,GAAGrD,WAAW,CAACyC,MAAM,CAACa,UAAU,IAC5CA,UAAU,CAACP,IAAI,CAACQ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChD,oBAAoB,CAAC+C,WAAW,CAAC,CAAC,CAC3E,CAAC;MACDpD,sBAAsB,CAACkD,QAAQ,CAAC;IAClC;EACF,CAAC,EAAE,CAAC7C,oBAAoB,EAAER,WAAW,CAAC,CAAC;EAEvCnC,SAAS,CAAC,MAAM;IACd,IAAIqD,oBAAoB,CAACkC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACtC/B,sBAAsB,CAACP,WAAW,CAAC;IACrC,CAAC,MAAM;MACL,MAAMuC,QAAQ,GAAGvC,WAAW,CAAC2B,MAAM,CAACgB,UAAU,IAC5CA,UAAU,CAACV,IAAI,CAACQ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtC,oBAAoB,CAACqC,WAAW,CAAC,CAAC,CAC3E,CAAC;MACDlC,sBAAsB,CAACgC,QAAQ,CAAC;IAClC;EACF,CAAC,EAAE,CAACvC,WAAW,EAAEI,oBAAoB,CAAC,CAAC;;EAEvC;EACArD,SAAS,CAAC,MAAM;IACd,MAAM6F,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIhC,qBAAqB,CAACiC,OAAO,IAAI,CAACjC,qBAAqB,CAACiC,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QAC1FnD,yBAAyB,CAAC,KAAK,CAAC;MAClC;IACF,CAAC;IAEDmB,QAAQ,CAACiC,gBAAgB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACX5B,QAAQ,CAACkC,mBAAmB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7F,SAAS,CAAC,MAAM;IACd;IACAuE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAErC,WAAW,CAAC;;IAE7C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA,IAAId,QAAQ,CAACG,kBAAkB,KAAK4E,SAAS,EAAE;MAC7C9E,WAAW,CAAC+E,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP7E,kBAAkB,EAAE;MACtB,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACW,WAAW,CAAC,CAAC;EAKjB,MAAMmE,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAErB,IAAI;MAAEsB;IAAM,CAAC,GAAGD,CAAC,CAACN,MAAM;IAChC3E,WAAW,CAAC+E,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACnB,IAAI,GAAGsB;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,sBAAsB,GAAIF,CAAC,IAAK;IACpC3D,uBAAuB,CAAC2D,CAAC,CAACN,MAAM,CAACO,KAAK,CAAC;EACzC,CAAC;EAED,MAAME,gBAAgB,GAAIjB,UAAU,IAAK;IACvCnE,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXG,kBAAkB,EAAEiE;IACtB,CAAC,CAAC;IACF3C,yBAAyB,CAAC,KAAK,CAAC;EAClC,CAAC;EAED,MAAM6D,wBAAwB,GAAGA,CAAA,KAAM;IACrC7D,yBAAyB,CAAC,CAACD,sBAAsB,CAAC;EACpD,CAAC;EAED,MAAM+D,gBAAgB,GAAIL,CAAC,IAAK;IAC9B,MAAM7E,IAAI,GAAG6E,CAAC,CAACN,MAAM,CAACY,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAInF,IAAI,EAAE;MACRE,eAAe,CAACF,IAAI,CAAC;;MAErB;MACA,IAAIA,IAAI,CAACoF,IAAI,KAAK,iBAAiB,EAAE;QACnC;QACA,MAAMC,OAAO,GAAGC,GAAG,CAACC,eAAe,CAACvF,IAAI,CAAC;QACzCI,aAAa,CAACiF,OAAO,CAAC;MACxB,CAAC,MAAM;QACL;QACAjF,aAAa,CAAC,IAAI,CAAC;MACrB;IACF,CAAC,MAAM;MACLF,eAAe,CAAC,IAAI,CAAC;MACrBE,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMoF,WAAW,GAAIxF,IAAI,IAAK;IAC5B,IAAI,CAACA,IAAI,EAAE,oBAAOb,OAAA,CAACJ,MAAM;MAAC0G,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAEtC,MAAMC,QAAQ,GAAG9F,IAAI,CAACoF,IAAI;IAE1B,IAAIU,QAAQ,KAAK,iBAAiB,EAAE;MAClC,oBAAO3G,OAAA,CAACN,SAAS;QAAC4G,IAAI,EAAE,EAAG;QAACM,KAAK,EAAC;MAAS;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAChD,CAAC,MAAM,IAAIC,QAAQ,CAAC7B,QAAQ,CAAC,MAAM,CAAC,IAAIjE,IAAI,CAACwD,IAAI,CAACwC,QAAQ,CAAC,MAAM,CAAC,IAAIhG,IAAI,CAACwD,IAAI,CAACwC,QAAQ,CAAC,OAAO,CAAC,EAAE;MACjG,oBAAO7G,OAAA,CAACL,UAAU;QAAC2G,IAAI,EAAE,EAAG;QAACM,KAAK,EAAC;MAAS;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACjD,CAAC,MAAM;MACL,oBAAO1G,OAAA,CAACJ,MAAM;QAAC0G,IAAI,EAAE,EAAG;QAACM,KAAK,EAAC;MAAS;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC7C;EACF,CAAC;EAED,MAAMI,UAAU,GAAGA,CAAA,KAAM;IACvB/F,eAAe,CAAC,IAAI,CAAC;IACrBE,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,IAAI+B,YAAY,CAACkC,OAAO,EAAE;MACxBlC,YAAY,CAACkC,OAAO,CAACS,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;EAED,MAAMoB,cAAc,GAAIrB,CAAC,IAAK;IAC5BA,CAAC,CAACsB,cAAc,CAAC,CAAC;IAClBtB,CAAC,CAACuB,eAAe,CAAC,CAAC;EACrB,CAAC;EAED,MAAMC,UAAU,GAAIxB,CAAC,IAAK;IACxBA,CAAC,CAACsB,cAAc,CAAC,CAAC;IAClBtB,CAAC,CAACuB,eAAe,CAAC,CAAC;IAEnB,IAAIvB,CAAC,CAACyB,YAAY,CAACnB,KAAK,IAAIN,CAAC,CAACyB,YAAY,CAACnB,KAAK,CAAC,CAAC,CAAC,EAAE;MACnD,MAAMnF,IAAI,GAAG6E,CAAC,CAACyB,YAAY,CAACnB,KAAK,CAAC,CAAC,CAAC;MACpCjF,eAAe,CAACF,IAAI,CAAC;;MAErB;MACA,IAAI,CAACL,QAAQ,CAACE,KAAK,EAAE;QACnBD,WAAW,CAAC+E,IAAI,KAAK;UACnB,GAAGA,IAAI;UACP9E,KAAK,EAAEG,IAAI,CAACwD,IAAI,CAAC+C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;MACL;;MAEA;MACA,IAAIvG,IAAI,CAACoF,IAAI,KAAK,iBAAiB,EAAE;QACnC;QACA,MAAMC,OAAO,GAAGC,GAAG,CAACC,eAAe,CAACvF,IAAI,CAAC;QACzCI,aAAa,CAACiF,OAAO,CAAC;MACxB,CAAC,MAAM;QACL;QACAjF,aAAa,CAAC,IAAI,CAAC;MACrB;IACF;EACF,CAAC;EAED,MAAMoG,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIrE,YAAY,CAACkC,OAAO,EAAE;MACxBlC,YAAY,CAACkC,OAAO,CAACoC,KAAK,CAAC,CAAC;IAC9B,CAAC,MAAM;MACL5D,OAAO,CAACtC,KAAK,CAAC,8BAA8B,CAAC;MAC7C;MACA,MAAMmG,KAAK,GAAGnE,QAAQ,CAACoE,cAAc,CAAC,UAAU,CAAC;MACjD,IAAID,KAAK,EAAE;QACTA,KAAK,CAACD,KAAK,CAAC,CAAC;MACf,CAAC,MAAM;QACL/H,KAAK,CAAC6B,KAAK,CAAC,+CAA+C,CAAC;MAC9D;IACF;EACF,CAAC;EAED,MAAMqG,YAAY,GAAG,MAAO/B,CAAC,IAAK;IAChCA,CAAC,CAACsB,cAAc,CAAC,CAAC;;IAElB;IACA,IAAI,CAACxG,QAAQ,CAACE,KAAK,CAACgE,IAAI,CAAC,CAAC,EAAE;MAC1BrD,QAAQ,CAAC,kCAAkC,CAAC;MAC5C;IACF;IAEA,IAAI,CAACb,QAAQ,CAACG,kBAAkB,EAAE;MAChCU,QAAQ,CAAC,0BAA0B,CAAC;MACpC;IACF;IAEA,IAAI,CAACP,YAAY,EAAE;MACjBO,QAAQ,CAAC,gCAAgC,CAAC;MAC1C;IACF;IAEA,IAAI;MACFF,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMqG,cAAc,GAAG5H,YAAY,CAACU,QAAQ,CAACE,KAAK,CAAC;MACnD,MAAMiH,mBAAmB,GAAG7H,YAAY,CAACU,QAAQ,CAACG,kBAAkB,CAAC0D,IAAI,CAAC;;MAE1E;MACA,MAAMuD,UAAU,GAAG,IAAIC,QAAQ,CAAC,CAAC;MACjCD,UAAU,CAACE,MAAM,CAAC,MAAM,EAAEhH,YAAY,CAAC;;MAEvC;MACA,IAAI4G,cAAc,EAAE;QAClBE,UAAU,CAACE,MAAM,CAAC,OAAO,EAAEJ,cAAc,CAAC;MAC5C;MAEA,IAAIC,mBAAmB,EAAE;QACvBC,UAAU,CAACE,MAAM,CAAC,YAAY,EAAEH,mBAAmB,CAAC;MACtD;MAEA,IAAInH,QAAQ,CAACI,WAAW,EAAE;QACxBgH,UAAU,CAACE,MAAM,CAAC,aAAa,EAAEtH,QAAQ,CAACI,WAAW,CAAC;MACxD;;MAEA;MACA,IAAIsB,2BAA2B,CAAC6F,MAAM,GAAG,CAAC,EAAE;QAC1C,MAAMC,WAAW,GAAG9F,2BAA2B,CAAC+B,GAAG,CAACgE,QAAQ,IAAIA,QAAQ,CAAC9D,EAAE,CAAC;QAC5EyD,UAAU,CAACE,MAAM,CAAC,sBAAsB,EAAEI,IAAI,CAACC,SAAS,CAACH,WAAW,CAAC,CAAC;MACxE;;MAEA;MACA,MAAMvE,QAAQ,GAAG,MAAMrE,UAAU,CAACgJ,SAAS,CAACR,UAAU,CAAC;MAEvDrI,KAAK,CAAC8I,OAAO,CAAC,4BAA4B,CAAC;MAE3C,IAAIhI,SAAS,EAAE;QACbA,SAAS,CAAC,CAAC;MACb;;MAEA;MACAD,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAOqE,GAAG,EAAE;MAAA,IAAA6D,aAAA,EAAAC,kBAAA;MACZ7E,OAAO,CAACtC,KAAK,CAAC,sBAAsB,EAAEqD,GAAG,CAAC;MAE1C,IAAI+D,YAAY,GAAG,yCAAyC;;MAE5D;MACA,IAAI/D,GAAG,aAAHA,GAAG,gBAAA6D,aAAA,GAAH7D,GAAG,CAAEhB,QAAQ,cAAA6E,aAAA,gBAAAC,kBAAA,GAAbD,aAAA,CAAeG,IAAI,cAAAF,kBAAA,eAAnBA,kBAAA,CAAqBG,MAAM,EAAE;QAAA,IAAAC,cAAA,EAAAC,mBAAA;QAC/BJ,YAAY,GAAG/D,GAAG,aAAHA,GAAG,wBAAAkE,cAAA,GAAHlE,GAAG,CAAEhB,QAAQ,cAAAkF,cAAA,wBAAAC,mBAAA,GAAbD,cAAA,CAAeF,IAAI,cAAAG,mBAAA,uBAAnBA,mBAAA,CAAqBF,MAAM;MAC5C;MAEArH,QAAQ,CAACmH,YAAY,CAAC;MAEtBjJ,KAAK,CAAC6B,KAAK,CAACoH,YAAY,CAAC;IAC3B,CAAC,SAAS;MACRrH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAnC,KAAK,CAACG,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACX,IAAI6B,UAAU,EAAE;QACdmF,GAAG,CAAC0C,eAAe,CAAC7H,UAAU,CAAC;MACjC;IACF,CAAC;EACH,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;;EAGA;;EAGA,oBACEhB,OAAA;IAAK8I,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAChC/I,OAAA;MAAK8I,SAAS,EAAC,eAAe;MAACE,GAAG,EAAE7F,QAAS;MAAA4F,QAAA,gBAC3C/I,OAAA;QAAA+I,QAAA,EAAI;MAAmB;QAAAxC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAE3BtF,KAAK,iBAAIpB,OAAA;QAAK8I,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAE3H;MAAK;QAAAmF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEtD1G,OAAA;QAAMiJ,QAAQ,EAAExB,YAAa;QAACqB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACpD/I,OAAA;UAAK8I,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB/I,OAAA;YAAOkJ,OAAO,EAAC,OAAO;YAAAH,QAAA,EAAC;UAAS;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxC1G,OAAA;YACEiG,IAAI,EAAC,MAAM;YACX9B,EAAE,EAAC,OAAO;YACVE,IAAI,EAAC,OAAO;YACZsB,KAAK,EAAEnF,QAAQ,CAACE,KAAM;YACtByI,QAAQ,EAAE1D,iBAAkB;YAC5B2D,WAAW,EAAC,iBAAiB;YAC7BC,QAAQ;UAAA;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN1G,OAAA;UAAK8I,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB/I,OAAA;YAAOkJ,OAAO,EAAC,YAAY;YAAAH,QAAA,EAAC;UAAQ;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAC3ChF,iBAAiB,gBAChB1B,OAAA;YAAK8I,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC/I,OAAA,CAACR,cAAc;cAAC8G,IAAI,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/B1G,OAAA;cAAA+I,QAAA,EAAM;YAAqB;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,gBAEN1G,OAAA;YAAK8I,SAAS,EAAC,+BAA+B;YAACE,GAAG,EAAE/F,qBAAsB;YAAA8F,QAAA,gBACxE/I,OAAA;cACE8I,SAAS,EAAC,6BAA6B;cACvCQ,OAAO,EAAEA,CAAA,KAAMrH,yBAAyB,CAAC,CAACD,sBAAsB,CAAE;cAAA+G,QAAA,GAEjEvI,QAAQ,CAACG,kBAAkB,gBAC1BX,OAAA;gBAAM8I,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAEvI,QAAQ,CAACG,kBAAkB,CAAC0D;cAAI;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,gBAE1E1G,OAAA;gBAAM8I,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAiB;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACtD,eACD1G,OAAA;gBAAM8I,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAC;cAAC;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,EAEL1E,sBAAsB,iBACrBhC,OAAA;cAAK8I,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAClC/I,OAAA;gBAAK8I,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,eAChC/I,OAAA;kBACEiG,IAAI,EAAC,MAAM;kBACXmD,WAAW,EAAC,qBAAqB;kBACjCzD,KAAK,EAAE7D,oBAAqB;kBAC5BqH,QAAQ,EAAGzD,CAAC,IAAK3D,uBAAuB,CAAC2D,CAAC,CAACN,MAAM,CAACO,KAAK,CAAE;kBACzD2D,OAAO,EAAG5D,CAAC,IAAKA,CAAC,CAACuB,eAAe,CAAC;gBAAE;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN1G,OAAA;gBAAK8I,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GAChClF,KAAK,CAACC,OAAO,CAACxC,WAAW,CAAC,IAAIA,WAAW,CAACyG,MAAM,GAAG,CAAC,GACnDzG,WAAW,CACRyC,MAAM,CAACC,IAAI,IACVA,IAAI,CAACK,IAAI,CAACQ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChD,oBAAoB,CAAC+C,WAAW,CAAC,CAAC,CACrE,CAAC,CACAZ,GAAG,CAAED,IAAI;kBAAA,IAAAuF,qBAAA;kBAAA,oBACRvJ,OAAA;oBAEE8I,SAAS,EAAE,qBAAqB,EAAAS,qBAAA,GAAA/I,QAAQ,CAACG,kBAAkB,cAAA4I,qBAAA,uBAA3BA,qBAAA,CAA6BpF,EAAE,MAAKH,IAAI,CAACG,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;oBAChGmF,OAAO,EAAEA,CAAA,KAAM;sBACb7I,WAAW,CAAC;wBACV,GAAGD,QAAQ;wBACXG,kBAAkB,EAAEqD;sBACtB,CAAC,CAAC;sBACF/B,yBAAyB,CAAC,KAAK,CAAC;sBAChCF,uBAAuB,CAAC,EAAE,CAAC;oBAC7B,CAAE;oBAAAgH,QAAA,EAED/E,IAAI,CAACK;kBAAI,GAXLL,IAAI,CAACG,EAAE;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAYT,CAAC;gBAAA,CACP,CAAC,gBAEJ1G,OAAA;kBAAK8I,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAwB;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAC1D,EAEA7C,KAAK,CAACC,OAAO,CAACxC,WAAW,CAAC,IAAIA,WAAW,CAACyG,MAAM,GAAG,CAAC,IACnDzG,WAAW,CAACyC,MAAM,CAACC,IAAI,IACrBA,IAAI,CAACK,IAAI,CAACQ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChD,oBAAoB,CAAC+C,WAAW,CAAC,CAAC,CACrE,CAAC,CAACkD,MAAM,KAAK,CAAC,iBACZ/H,OAAA;kBAAK8I,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAC,kCAA+B,EAACjH,oBAAoB,EAAC,IAAC;gBAAA;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACxF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN1G,OAAA;UAAK8I,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB/I,OAAA;YAAOkJ,OAAO,EAAC,aAAa;YAAAH,QAAA,EAAC;UAAW;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChD1G,OAAA;YACEmE,EAAE,EAAC,aAAa;YAChBE,IAAI,EAAC,aAAa;YAClBsB,KAAK,EAAEnF,QAAQ,CAACI,WAAY;YAC5BuI,QAAQ,EAAE1D,iBAAkB;YAE5B4D,QAAQ;UAAA;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN1G,OAAA;UAAK8I,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB/I,OAAA;YAAOkJ,OAAO,EAAC,MAAM;YAAAH,QAAA,EAAC;UAAmB;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAEhD5F,YAAY,gBACXd,OAAA;YAAK8I,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC/I,OAAA;cAAK8I,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B/I,OAAA;gBAAA+I,QAAA,EAAOjI,YAAY,CAACuD;cAAI;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChC1G,OAAA;gBAAA+I,QAAA,GAAM,GAAC,EAAC,CAACjI,YAAY,CAACwF,IAAI,GAAG,IAAI,EAAEkD,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;cAAA;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzD1G,OAAA;gBACEiG,IAAI,EAAC,QAAQ;gBACb6C,SAAS,EAAC,iBAAiB;gBAC3BQ,OAAO,EAAExC,UAAW;gBACpB,cAAW,aAAa;gBAAAiC,QAAA,EACzB;cAED;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAEL1F,UAAU,IAAIF,YAAY,CAACmF,IAAI,KAAK,iBAAiB,gBACpDjG,OAAA;cAAK8I,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eACpC/I,OAAA;gBACEyJ,GAAG,EAAEzI,UAAW;gBAChB8H,SAAS,EAAC,aAAa;gBACvBpI,KAAK,EAAC;cAAa;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,gBAEN1G,OAAA;cAAK8I,SAAS,EAAC,sBAAsB;cAAAC,QAAA,GAClC1C,WAAW,CAACvF,YAAY,CAAC,eAC1Bd,OAAA;gBAAA+I,QAAA,EACGjI,YAAY,CAACmF,IAAI,KAAK,iBAAiB,GACpC,2BAA2B,GAC3B,6BAA6BnF,YAAY,CAACuD,IAAI,CAAC+C,KAAK,CAAC,GAAG,CAAC,CAACsC,GAAG,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;cAAQ;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGE,CAAC,gBAEN1G,OAAA;YAAK8I,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eACpC/I,OAAA;cACEiG,IAAI,EAAC,MAAM;cACX9B,EAAE,EAAC,MAAM;cACT6E,GAAG,EAAEhG,YAAa;cAClBmG,QAAQ,EAAEpD,gBAAiB;cAC3B6D,MAAM,EAAC,2BAA2B;cAClCd,SAAS,EAAC,YAAY;cACtBO,QAAQ;YAAA;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN1G,OAAA;UAAK8I,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B/I,OAAA;YACEiG,IAAI,EAAC,QAAQ;YACb6C,SAAS,EAAC,YAAY;YACtBQ,OAAO,EAAElJ,OAAQ;YACjByJ,QAAQ,EAAE3I,OAAQ;YAAA6H,QAAA,EACnB;UAED;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1G,OAAA;YACEiG,IAAI,EAAC,QAAQ;YACb6C,SAAS,EAAC,YAAY;YACtBe,QAAQ,EAAE3I,OAAQ;YAAA6H,QAAA,EAEjB7H,OAAO,gBACNlB,OAAA,CAAAE,SAAA;cAAA6I,QAAA,gBACE/I,OAAA,CAACR,cAAc;gBAAC8G,IAAI,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/B1G,OAAA;gBAAA+I,QAAA,EAAM;cAAY;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eACzB,CAAC,GACD;UAAY;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpG,EAAA,CA/jBIH,MAAM;EAAA,QACOV,WAAW;AAAA;AAAAqK,EAAA,GADxB3J,MAAM;AAikBZ,eAAeA,MAAM;AAAC,IAAA2J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}