.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: var(--bg-white);
}

.login-box {
  width: 100%;
  max-width: 400px;
  padding: 2rem;
}

.login-box h1 {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--text-slate-900);
  font-size: 24px;
}

.login-header {
  background-color: var(--stat-blue-bg);
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.login-header h2 {
  margin: 0;
  font-size: 18px;
  color: var(--text-slate-900);
  margin-bottom: 0.5rem;
}

.login-header p {
  margin: 0;
  color: var(--text-slate-600);
  font-size: 14px;
}

.container {
  max-width: 400px;
  margin: 2rem auto;
  padding: 2rem;
}

.tab-container {
  display: flex;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border-slate-200);
}

.tab-button {
  padding: 1rem 2rem;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 16px;
  color: var(--text-slate-600);
  width: 100%;
}

.tab-button.active {
  color: var(--primary-blue);
  border-bottom: 2px solid var(--primary-blue);
}

.tab-content {
  display: none;
  width: 100%;
}

.tab-content.active {
  display: block;
  width: 100%;
}

.tab-content form {
  width: 100%;
}

.form-group {
  margin-bottom: 1.5rem;
  width: 100%;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-slate-900);
}

.form-group input {
  width: 100%;
  box-sizing: border-box;
  padding: 0.75rem;
  border: 1px solid var(--border-slate-200);
  border-radius: 6px;
  font-size: 14px;
}

.btn {
  width: 100%;
  padding: 0.75rem;
  background-color: var(--primary-blue);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
}

.btn:disabled {
  background-color: var(--text-slate-400);
  cursor: not-allowed;
}

.info-text {
  margin-top: 1rem;
  font-size: 14px;
  color: var(--text-slate-600);
}

.message {
  margin-top: 1rem;
  padding: 1rem;
  border-radius: 6px;
  font-size: 14px;
  text-align: center;
}

.message.error {
  background-color: var(--severity-high-bg);
  color: var(--severity-high-text);
}

.message.success {
  background-color: var(--severity-low-bg);
  color: var(--severity-low-text);
}

.login-button {
  width: 100%;
  padding: 0.75rem;
  background-color: var(--primary-blue);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.login-button:hover {
  background-color: var(--primary-blue);
  opacity: 0.9;
}

.forgot-password {
  text-align: center;
  margin-top: 1rem;
}

.forgot-password a {
  color: var(--primary-blue);
  text-decoration: none;
  font-size: 14px;
}

.forgot-password a:hover {
  text-decoration: underline;
}

.error-message {
  background-color: var(--severity-high-bg);
  color: var(--severity-high-text);
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 1rem;
  font-size: 14px;
  text-align: center;
}

.login-button:disabled {
  background-color: var(--text-slate-400);
  cursor: not-allowed;
}

.forgot-password-btn {
  background: none;
  border: none;
  color: var(--primary-blue);
  cursor: pointer;
  padding: 0;
  font-size: 14px;
  text-decoration: underline;
}

.forgot-password-btn:hover {
  opacity: 0.8;
}

/* Add styles for the logo container and image */
.logo-container {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.login-logo {
  height: 60px; /* Adjust height as needed */
  width: auto;
}

/* Ensure consistent width for auth container and forms */
#auth-container {
  width: 400px !important;
  margin: 0 auto;
} 