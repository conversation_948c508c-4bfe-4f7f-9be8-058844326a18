import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import supabase from './supabase';
import routes from './routes';
import Toast from './components/common/Toast/Toast';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import LoadingSpinner from './components/common/LoadingSpinner/LoadingSpinner';
import AutoLogout from './components/common/AutoLogout/AutoLogout';
import URLParamChecker from './components/common/URLParamChecker/URLParamChecker';
import { autoLogoutMinutes } from './constants/constants';

function App() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    supabase.auth.getUser().then(({ data: { user } }) => {
      setUser(user);
      setLoading(false);
    });
  }, []);

  if (loading) {
    return <div style={{display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh'}}>
      <LoadingSpinner/>
      Loading...
      </div>;
  }

  return (
    <Router>
      <URLParamChecker>
      {user && <AutoLogout timeoutMinutes={autoLogoutMinutes} />}
      <Routes>
        {routes(user).map((route, index) => (
          <Route 
            key={index}
            path={route.path}
            element={route.element}
          />
        ))}
      </Routes>
      <Toast />
      <ToastContainer 
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
      </URLParamChecker>
    </Router>
  );
}

export default App;
