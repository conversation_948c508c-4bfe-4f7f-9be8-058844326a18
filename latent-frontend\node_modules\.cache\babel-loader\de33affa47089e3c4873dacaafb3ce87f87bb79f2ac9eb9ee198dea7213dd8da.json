{"ast": null, "code": "import apiService from './api';\nimport API_URLS from '../config/apiUrls';\nconst userService = {\n  // Get user profile\n  getUserProfile: async () => {\n    return await apiService.get(API_URLS.USER.PROFILE);\n  },\n  // Update user profile\n  updateUserProfile: async (userData, userId) => {\n    try {\n      return await apiService.patch(API_URLS.USER.UPDATE_USER(userId), userData);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error updating user profile:', error);\n      let errorMessage = 'Failed to update user profile.';\n      if (error !== null && error !== void 0 && (_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.detail) {\n        var _error$response2, _error$response2$data;\n        errorMessage = error === null || error === void 0 ? void 0 : (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail;\n      }\n      const errorToThrow = new Error(errorMessage);\n      errorToThrow.response = error.response;\n      throw errorToThrow;\n    }\n  },\n  // Get user recent activity\n  getRecentActivity: async () => {\n    try {\n      return await apiService.get(API_URLS.USER.RECENT_ACTIVITY);\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('Error fetching recent activity:', error);\n      let errorMessage = 'Failed to fetch recent activity.';\n      if (error !== null && error !== void 0 && (_error$response3 = error.response) !== null && _error$response3 !== void 0 && (_error$response3$data = _error$response3.data) !== null && _error$response3$data !== void 0 && _error$response3$data.detail) {\n        var _error$response4, _error$response4$data;\n        errorMessage = error === null || error === void 0 ? void 0 : (_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail;\n      }\n      const errorToThrow = new Error(errorMessage);\n      errorToThrow.response = error.response;\n      throw errorToThrow;\n    }\n  }\n\n  // Other user-related API methods can be added here\n};\nexport default userService;", "map": {"version": 3, "names": ["apiService", "API_URLS", "userService", "getUserProfile", "get", "USER", "PROFILE", "updateUserProfile", "userData", "userId", "patch", "UPDATE_USER", "error", "_error$response", "_error$response$data", "console", "errorMessage", "response", "data", "detail", "_error$response2", "_error$response2$data", "errorToThrow", "Error", "getRecentActivity", "RECENT_ACTIVITY", "_error$response3", "_error$response3$data", "_error$response4", "_error$response4$data"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/services/userService.js"], "sourcesContent": ["import apiService from './api';\r\nimport API_URLS from '../config/apiUrls';\r\n\r\nconst userService = {\r\n  // Get user profile\r\n  getUserProfile: async () => {\r\n    return await apiService.get(API_URLS.USER.PROFILE);\r\n  },\r\n\r\n  // Update user profile\r\n  updateUserProfile: async (userData, userId) => {\r\n    try {\r\n      return await apiService.patch(API_URLS.USER.UPDATE_USER(userId), userData);\r\n    } catch (error) {\r\n      console.error('Error updating user profile:', error);\r\n      \r\n      let errorMessage = 'Failed to update user profile.';\r\n      if (error?.response?.data?.detail) {\r\n        errorMessage = error?.response?.data?.detail;\r\n      }\r\n      \r\n      const errorToThrow = new Error(errorMessage);\r\n      errorToThrow.response = error.response;\r\n      throw errorToThrow;\r\n    }\r\n  },\r\n\r\n  // Get user recent activity\r\n  getRecentActivity: async () => {\r\n    try {\r\n      return await apiService.get(API_URLS.USER.RECENT_ACTIVITY);\r\n    } catch (error) {\r\n      console.error('Error fetching recent activity:', error);\r\n      \r\n      let errorMessage = 'Failed to fetch recent activity.';\r\n      if (error?.response?.data?.detail) {\r\n        errorMessage = error?.response?.data?.detail;\r\n      }\r\n      \r\n      const errorToThrow = new Error(errorMessage);\r\n      errorToThrow.response = error.response;\r\n      throw errorToThrow;\r\n    }\r\n  },\r\n\r\n  // Other user-related API methods can be added here\r\n};\r\n\r\nexport default userService; "], "mappings": "AAAA,OAAOA,UAAU,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,mBAAmB;AAExC,MAAMC,WAAW,GAAG;EAClB;EACAC,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,OAAO,MAAMH,UAAU,CAACI,GAAG,CAACH,QAAQ,CAACI,IAAI,CAACC,OAAO,CAAC;EACpD,CAAC;EAED;EACAC,iBAAiB,EAAE,MAAAA,CAAOC,QAAQ,EAAEC,MAAM,KAAK;IAC7C,IAAI;MACF,OAAO,MAAMT,UAAU,CAACU,KAAK,CAACT,QAAQ,CAACI,IAAI,CAACM,WAAW,CAACF,MAAM,CAAC,EAAED,QAAQ,CAAC;IAC5E,CAAC,CAAC,OAAOI,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACH,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MAEpD,IAAII,YAAY,GAAG,gCAAgC;MACnD,IAAIJ,KAAK,aAALA,KAAK,gBAAAC,eAAA,GAALD,KAAK,CAAEK,QAAQ,cAAAJ,eAAA,gBAAAC,oBAAA,GAAfD,eAAA,CAAiBK,IAAI,cAAAJ,oBAAA,eAArBA,oBAAA,CAAuBK,MAAM,EAAE;QAAA,IAAAC,gBAAA,EAAAC,qBAAA;QACjCL,YAAY,GAAGJ,KAAK,aAALA,KAAK,wBAAAQ,gBAAA,GAALR,KAAK,CAAEK,QAAQ,cAAAG,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBF,IAAI,cAAAG,qBAAA,uBAArBA,qBAAA,CAAuBF,MAAM;MAC9C;MAEA,MAAMG,YAAY,GAAG,IAAIC,KAAK,CAACP,YAAY,CAAC;MAC5CM,YAAY,CAACL,QAAQ,GAAGL,KAAK,CAACK,QAAQ;MACtC,MAAMK,YAAY;IACpB;EACF,CAAC;EAED;EACAE,iBAAiB,EAAE,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,OAAO,MAAMxB,UAAU,CAACI,GAAG,CAACH,QAAQ,CAACI,IAAI,CAACoB,eAAe,CAAC;IAC5D,CAAC,CAAC,OAAOb,KAAK,EAAE;MAAA,IAAAc,gBAAA,EAAAC,qBAAA;MACdZ,OAAO,CAACH,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MAEvD,IAAII,YAAY,GAAG,kCAAkC;MACrD,IAAIJ,KAAK,aAALA,KAAK,gBAAAc,gBAAA,GAALd,KAAK,CAAEK,QAAQ,cAAAS,gBAAA,gBAAAC,qBAAA,GAAfD,gBAAA,CAAiBR,IAAI,cAAAS,qBAAA,eAArBA,qBAAA,CAAuBR,MAAM,EAAE;QAAA,IAAAS,gBAAA,EAAAC,qBAAA;QACjCb,YAAY,GAAGJ,KAAK,aAALA,KAAK,wBAAAgB,gBAAA,GAALhB,KAAK,CAAEK,QAAQ,cAAAW,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBV,IAAI,cAAAW,qBAAA,uBAArBA,qBAAA,CAAuBV,MAAM;MAC9C;MAEA,MAAMG,YAAY,GAAG,IAAIC,KAAK,CAACP,YAAY,CAAC;MAC5CM,YAAY,CAACL,QAAQ,GAAGL,KAAK,CAACK,QAAQ;MACtC,MAAMK,YAAY;IACpB;EACF;;EAEA;AACF,CAAC;AAED,eAAepB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}