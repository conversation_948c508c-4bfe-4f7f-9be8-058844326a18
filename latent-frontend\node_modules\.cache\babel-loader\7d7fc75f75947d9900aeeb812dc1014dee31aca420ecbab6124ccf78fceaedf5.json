{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\zipp\\\\latent-frontend\\\\src\\\\components\\\\common\\\\QueryInput\\\\QueryInput.js\",\n  _s = $RefreshSig$();\nimport { useState } from \"react\";\nimport { Send, Loader2 } from \"lucide-react\";\nimport \"./QueryInput.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QueryInput = ({\n  onSubmit,\n  isLoading\n}) => {\n  _s();\n  const [query, setQuery] = useState(\"\");\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (query.trim() && !isLoading) {\n      onSubmit(query.trim());\n      setQuery(\"\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleSubmit,\n    className: \"query-input-form\",\n    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"text\",\n      value: query,\n      onChange: e => setQuery(e.target.value),\n      placeholder: \"Ask a question about your SOPs and Regulations...\",\n      className: \"query-input-field\",\n      disabled: isLoading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      type: \"submit\",\n      disabled: !query.trim() || isLoading,\n      className: \"query-input-button\",\n      children: [isLoading ? /*#__PURE__*/_jsxDEV(Loader2, {\n        className: \"query-icon spin\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Send, {\n        className: \"query-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 11\n      }, this), isLoading ? \"Processing...\" : \"Send\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_s(QueryInput, \"HYX2QbDDdTtlu7GfoQbAPZOIM6k=\");\n_c = QueryInput;\nexport default QueryInput;\nvar _c;\n$RefreshReg$(_c, \"QueryInput\");", "map": {"version": 3, "names": ["useState", "Send", "Loader2", "jsxDEV", "_jsxDEV", "QueryInput", "onSubmit", "isLoading", "_s", "query", "<PERSON><PERSON><PERSON><PERSON>", "handleSubmit", "e", "preventDefault", "trim", "className", "children", "type", "value", "onChange", "target", "placeholder", "disabled", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/components/common/QueryInput/QueryInput.js"], "sourcesContent": ["import { useState } from \"react\";\r\nimport { Send, Loader2 } from \"lucide-react\";\r\nimport \"./QueryInput.css\";\r\n\r\nconst QueryInput = ({ onSubmit, isLoading }) => {\r\n  const [query, setQuery] = useState(\"\");\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    if (query.trim() && !isLoading) {\r\n      onSubmit(query.trim());\r\n      setQuery(\"\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <form onSubmit={handleSubmit} className=\"query-input-form\">\r\n      <input\r\n        type=\"text\"\r\n        value={query}\r\n        onChange={(e) => setQuery(e.target.value)}\r\n        placeholder=\"Ask a question about your SOPs and Regulations...\"\r\n        className=\"query-input-field\"\r\n        disabled={isLoading}\r\n      />\r\n      <button\r\n        type=\"submit\"\r\n        disabled={!query.trim() || isLoading}\r\n        className=\"query-input-button\"\r\n      >\r\n        {isLoading ? (\r\n          <Loader2 className=\"query-icon spin\" />\r\n        ) : (\r\n          <Send className=\"query-icon\" />\r\n        )}\r\n        {isLoading ? \"Processing...\" : \"Send\"}\r\n      </button>\r\n    </form>\r\n  );\r\n};\r\n\r\nexport default QueryInput;"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,IAAI,EAAEC,OAAO,QAAQ,cAAc;AAC5C,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMW,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIJ,KAAK,CAACK,IAAI,CAAC,CAAC,IAAI,CAACP,SAAS,EAAE;MAC9BD,QAAQ,CAACG,KAAK,CAACK,IAAI,CAAC,CAAC,CAAC;MACtBJ,QAAQ,CAAC,EAAE,CAAC;IACd;EACF,CAAC;EAED,oBACEN,OAAA;IAAME,QAAQ,EAAEK,YAAa;IAACI,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBACxDZ,OAAA;MACEa,IAAI,EAAC,MAAM;MACXC,KAAK,EAAET,KAAM;MACbU,QAAQ,EAAGP,CAAC,IAAKF,QAAQ,CAACE,CAAC,CAACQ,MAAM,CAACF,KAAK,CAAE;MAC1CG,WAAW,EAAC,mDAAmD;MAC/DN,SAAS,EAAC,mBAAmB;MAC7BO,QAAQ,EAAEf;IAAU;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC,eACFtB,OAAA;MACEa,IAAI,EAAC,QAAQ;MACbK,QAAQ,EAAE,CAACb,KAAK,CAACK,IAAI,CAAC,CAAC,IAAIP,SAAU;MACrCQ,SAAS,EAAC,oBAAoB;MAAAC,QAAA,GAE7BT,SAAS,gBACRH,OAAA,CAACF,OAAO;QAACa,SAAS,EAAC;MAAiB;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEvCtB,OAAA,CAACH,IAAI;QAACc,SAAS,EAAC;MAAY;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAC/B,EACAnB,SAAS,GAAG,eAAe,GAAG,MAAM;IAAA;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEX,CAAC;AAAClB,EAAA,CAnCIH,UAAU;AAAAsB,EAAA,GAAVtB,UAAU;AAqChB,eAAeA,UAAU;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}