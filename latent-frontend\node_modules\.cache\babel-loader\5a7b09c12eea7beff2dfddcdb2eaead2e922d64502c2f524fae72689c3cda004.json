{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\zipp\\\\latent-frontend\\\\src\\\\components\\\\pages\\\\GapAnalysis\\\\GapAnalysis.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Navigation from '../../common/Navigation/Navigation';\nimport DepartmentFilters from '../../common/DepartmentFilters/DepartmentFilters';\nimport Modal from '../../common/Modal/Modal';\nimport AssessmentForm from './AssessmentForm';\nimport GapDetailsSidebar from './GapDetailsSidebar';\nimport sopService from '../../../services/sopService';\nimport apiService from '../../../services/api';\nimport API_URLS from '../../../config/apiUrls';\nimport LoadingSpinner from '../../common/LoadingSpinner/LoadingSpinner';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\nimport './GapAnalysis.css';\nimport { toast } from 'react-toastify';\nimport { sanitizeText } from '../../../utils/sanitize';\nimport { formatDate } from '../../../utils/dateUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst GapAnalysis = () => {\n  _s();\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [selectedRowIndex, setSelectedRowIndex] = useState(null);\n  const [selectedDepartment, setSelectedDepartment] = useState('All');\n  const [gapResults, setGapResults] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [analysisResults, setAnalysisResults] = useState(null);\n  const [analysisLoading, setAnalysisLoading] = useState(false);\n  const [analysisError, setAnalysisError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n  const [processingSopId, setProcessingSopId] = useState(null);\n\n  // Add sorting and filtering states\n  const [sortField, setSortField] = useState('date');\n  const [sortDirection, setSortDirection] = useState('desc');\n  const [dateFilter, setDateFilter] = useState('all');\n\n  // const stats = [\n  //   {\n  //     title: 'Compliant SOPs',\n  //     value: '76%',\n  //     description: '38 of 50 SOPs meet requirements',\n  //     action: 'View Details',\n  //     disabled: true\n  //   },\n  //   {\n  //     title: 'Non-Compliant',\n  //     value: '24%',\n  //     description: '12 SOPs require attention',\n  //     action: 'View Issues',\n  //     disabled: true\n  //   },\n  //   {\n  //     title: 'Critical Gaps',\n  //     value: '5',\n  //     description: 'High-priority items needing immediate action',\n  //     action: 'Resolve Now',\n  //     disabled: true\n  //   },\n  //   {\n  //     title: 'Last Assessment',\n  //     value: 'June 12, 2023',\n  //     description: 'Next scheduled review in 14 days',\n  //     action: 'Schedule Review',\n  //     disabled: true\n  //   }\n  // ];\n\n  // Modify the useEffect that fetches SOPs to also fetch analysis results\n  useEffect(() => {\n    const fetchSOPs = async () => {\n      try {\n        setLoading(true);\n        const sops = await sopService.getAllSOPs();\n\n        // Transform SOPs into gap analysis results\n        const transformedResults = sops.sops_data.map(sop => {\n          var _sop$metadata, _sop$metadata2;\n          const department = ((_sop$metadata = sop.metadata) === null || _sop$metadata === void 0 ? void 0 : _sop$metadata.department) || 'Unassigned';\n          return {\n            id: sop.id,\n            title: sop.title || 'Unnamed SOP',\n            department: department,\n            date: ((_sop$metadata2 = sop.metadata) === null || _sop$metadata2 === void 0 ? void 0 : _sop$metadata2.date) || sop.created_at || new Date().toISOString(),\n            // Extract gap metrics directly from SOP data\n            totalGaps: sop.total_gaps,\n            highPriorityGaps: sop.high_priority_gaps,\n            mediumPriorityGaps: sop.medium_priority_gaps,\n            lowPriorityGaps: sop.low_priority_gaps\n          };\n        });\n        setGapResults(transformedResults);\n\n        // After fetching SOPs, fetch analysis results for all SOPs\n        if (transformedResults.length > 0) {\n          fetchAnalysisResults(transformedResults.map(sop => sop.id));\n        }\n      } catch (err) {\n        var _err$response, _err$response$data;\n        console.error('Error fetching SOPs:', err);\n        let errorMessage = 'Failed to load gap analysis results. Please try again later.';\n        if (err !== null && err !== void 0 && (_err$response = err.response) !== null && _err$response !== void 0 && (_err$response$data = _err$response.data) !== null && _err$response$data !== void 0 && _err$response$data.detail) {\n          var _err$response2, _err$response2$data;\n          errorMessage = err === null || err === void 0 ? void 0 : (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.detail;\n        }\n        setError(errorMessage);\n      } finally {\n        setTimeout(() => {\n          setLoading(false);\n        }, 1000);\n      }\n    };\n    fetchSOPs();\n  }, []);\n\n  // Update the fetchAnalysisResults function to properly check for changed_gap_details\n  const fetchAnalysisResults = async sopIds => {\n    try {\n      setAnalysisLoading(true);\n      setAnalysisError(null);\n\n      // Call the /results endpoint with the SOP IDs as payload\n      const response = await apiService.post(API_URLS.ANALYSIS.RESULTS, sopIds);\n\n      // Store the analysis results in state\n      if (response && response.results) {\n        // Map the results to the corresponding SOPs\n        const resultsMap = {};\n        response.results.forEach(result => {\n          if (result.sop_id) {\n            // Determine which gap details to use - prefer changed_gap_detail if available\n            let gapDetailsToUse = [];\n            let isValidated = false;\n            if (result.metadata && result.metadata.changed_gap_detail && Array.isArray(result.metadata.changed_gap_detail) && result.metadata.changed_gap_detail.length > 0) {\n              // Use the validated/changed gap details if available\n              gapDetailsToUse = result.metadata.changed_gap_detail;\n              isValidated = true;\n              console.log('Using changed_gap_detail for SOP:', result.sop_id, '- Validated: true');\n            } else if (result.gap_details) {\n              // Fall back to original gap details\n              gapDetailsToUse = result.gap_details;\n              isValidated = false;\n              console.log('Using original gap_details for SOP:', result.sop_id, '- Validated: false');\n            }\n\n            // Ensure gap_details is always an array\n            if (!Array.isArray(gapDetailsToUse)) {\n              gapDetailsToUse = [];\n            }\n\n            // Use direct values from API response instead of client-side calculations\n            console.log(`SOP ${result.sop_id}: Analysis data available`);\n\n            // Store the result with gap details and validation status\n            resultsMap[result.sop_id] = {\n              ...result,\n              // Store the gap details we're using for consistency\n              gap_details: gapDetailsToUse,\n              // Store validation status for display logic\n              isValidated: isValidated\n            };\n          }\n        });\n\n        // Update the gap results with analysis data\n        setGapResults(prevResults => prevResults.map(sop => ({\n          ...sop,\n          analysis: resultsMap[sop.id] || {\n            gap_details: [],\n            isValidated: false\n          }\n        })));\n        console.log('Updated gap results with analysis data and validation status');\n      }\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      console.error('Error fetching analysis results:', err);\n      let errorMessage = 'Failed to load analysis results. Please try again later.';\n      if (err !== null && err !== void 0 && (_err$response3 = err.response) !== null && _err$response3 !== void 0 && (_err$response3$data = _err$response3.data) !== null && _err$response3$data !== void 0 && _err$response3$data.detail) {\n        var _err$response4, _err$response4$data;\n        errorMessage = err === null || err === void 0 ? void 0 : (_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.detail;\n      }\n      setAnalysisError(errorMessage);\n    } finally {\n      setAnalysisLoading(false);\n    }\n  };\n\n  // Add a silent update function that doesn't show loading states\n  const silentUpdateAnalysisData = async sopId => {\n    try {\n      console.log('Silently updating analysis data for SOP:', sopId);\n\n      // Fetch updated analysis results for the specific SOP\n      const response = await apiService.post(API_URLS.ANALYSIS.RESULTS, [sopId]);\n      if (response && response.results && response.results.length > 0) {\n        const result = response.results[0];\n        if (result.sop_id === sopId) {\n          // Determine which gap details to use\n          let gapDetailsToUse = [];\n          let isValidated = false;\n          if (result.metadata && result.metadata.changed_gap_detail && Array.isArray(result.metadata.changed_gap_detail) && result.metadata.changed_gap_detail.length > 0) {\n            gapDetailsToUse = result.metadata.changed_gap_detail;\n            isValidated = true;\n          } else if (result.gap_details) {\n            gapDetailsToUse = result.gap_details;\n            isValidated = false;\n          }\n          if (!Array.isArray(gapDetailsToUse)) {\n            gapDetailsToUse = [];\n          }\n\n          // Add a small delay to ensure backend has processed the changes\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Also fetch updated SOP data to get latest gap metrics\n          console.log('Fetching updated SOP data for ID:', sopId);\n          const sops = await sopService.getAllSOPs();\n          const updatedSop = sops.sops_data.find(sop => sop.id === sopId);\n          console.log('Updated SOP data:', updatedSop);\n          console.log('Gap metrics - Total:', updatedSop === null || updatedSop === void 0 ? void 0 : updatedSop.total_gaps, 'High:', updatedSop === null || updatedSop === void 0 ? void 0 : updatedSop.high_priority_gaps, 'Medium:', updatedSop === null || updatedSop === void 0 ? void 0 : updatedSop.medium_priority_gaps, 'Low:', updatedSop === null || updatedSop === void 0 ? void 0 : updatedSop.low_priority_gaps);\n\n          // Update the specific SOP's analysis data and gap metrics\n          setGapResults(prevResults => {\n            const updatedResults = prevResults.map(sop => sop.id === sopId ? {\n              ...sop,\n              // Update gap metrics from SOP data\n              totalGaps: (updatedSop === null || updatedSop === void 0 ? void 0 : updatedSop.total_gaps) || sop.totalGaps,\n              highPriorityGaps: (updatedSop === null || updatedSop === void 0 ? void 0 : updatedSop.high_priority_gaps) || sop.highPriorityGaps,\n              mediumPriorityGaps: (updatedSop === null || updatedSop === void 0 ? void 0 : updatedSop.medium_priority_gaps) || sop.mediumPriorityGaps,\n              lowPriorityGaps: (updatedSop === null || updatedSop === void 0 ? void 0 : updatedSop.low_priority_gaps) || sop.lowPriorityGaps,\n              // Update analysis data\n              analysis: {\n                ...result,\n                gap_details: gapDetailsToUse,\n                isValidated: isValidated\n              }\n            } : sop);\n            console.log('Updated gap results:', updatedResults);\n            return updatedResults;\n          });\n\n          // Update analysisResults if it's for the same SOP\n          setAnalysisResults(prevResults => {\n            if (prevResults && prevResults.sop_id === sopId) {\n              return {\n                ...result,\n                gap_details: gapDetailsToUse,\n                isValidated: isValidated\n              };\n            }\n            return prevResults;\n          });\n          console.log('Successfully updated analysis data and gap metrics silently for SOP:', sopId);\n        }\n      }\n    } catch (err) {\n      var _err$response5, _err$response5$data;\n      console.error('Error silently updating analysis data:', err);\n      let errorMessage = 'Failed to update analysis data.';\n      if (err !== null && err !== void 0 && (_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.detail) {\n        var _err$response6, _err$response6$data;\n        errorMessage = err === null || err === void 0 ? void 0 : (_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.detail;\n      }\n\n      // Don't show error toast for silent updates to avoid disrupting UX\n    }\n  };\n\n  // Improve the refreshAllData function to ensure it properly updates the UI\n  const refreshAllData = async (silent = false) => {\n    try {\n      console.log('Refreshing gap analysis data...');\n      if (!silent) {\n        setLoading(true);\n      }\n\n      // Fetch SOPs\n      const sops = await sopService.getAllSOPs();\n      console.log('Fetched SOPs:', sops);\n\n      // Transform SOPs into gap analysis results\n      const transformedResults = sops.sops_data.map(sop => {\n        var _sop$metadata3, _sop$metadata4;\n        const department = ((_sop$metadata3 = sop.metadata) === null || _sop$metadata3 === void 0 ? void 0 : _sop$metadata3.department) || 'Unassigned';\n        return {\n          id: sop.id,\n          title: sop.title || 'Unnamed SOP',\n          department: department,\n          date: ((_sop$metadata4 = sop.metadata) === null || _sop$metadata4 === void 0 ? void 0 : _sop$metadata4.date) || sop.created_at || new Date().toISOString(),\n          // Extract gap metrics directly from SOP data\n          totalGaps: sop.total_gaps,\n          highPriorityGaps: sop.high_priority_gaps,\n          mediumPriorityGaps: sop.medium_priority_gaps,\n          lowPriorityGaps: sop.low_priority_gaps\n        };\n      });\n      setGapResults(transformedResults);\n      console.log('Updated gap results:', transformedResults);\n\n      // Fetch analysis results for all SOPs\n      if (transformedResults.length > 0) {\n        const sopIds = transformedResults.map(sop => sop.id);\n        console.log('Fetching analysis results for SOPs:', sopIds);\n        await fetchAnalysisResults(sopIds);\n      }\n      setError(null);\n      console.log('Gap analysis data refresh complete');\n    } catch (err) {\n      var _err$response7, _err$response7$data;\n      console.error('Error refreshing gap analysis data:', err);\n      let errorMessage = 'Failed to load gap analysis results. Please try again later.';\n      if (err !== null && err !== void 0 && (_err$response7 = err.response) !== null && _err$response7 !== void 0 && (_err$response7$data = _err$response7.data) !== null && _err$response7$data !== void 0 && _err$response7$data.detail) {\n        var _err$response8, _err$response8$data;\n        errorMessage = err === null || err === void 0 ? void 0 : (_err$response8 = err.response) === null || _err$response8 === void 0 ? void 0 : (_err$response8$data = _err$response8.data) === null || _err$response8$data === void 0 ? void 0 : _err$response8$data.detail;\n      }\n      setError(errorMessage);\n    } finally {\n      if (!silent) {\n        setLoading(false);\n      }\n    }\n  };\n\n  // Filter results based on selected department\n  const filteredResultsMemo = React.useMemo(() => {\n    // Helper function to get the highest date from available dates (shared between filtering and sorting)\n    const getHighestDate = item => {\n      const dates = [];\n\n      // Add analysis dates if they exist\n      if (item.analysis && item.analysis.analyzed_at) {\n        dates.push(new Date(item.analysis.analyzed_at));\n      }\n      if (item.analysis && item.analysis.updated_at) {\n        dates.push(new Date(item.analysis.updated_at));\n      }\n\n      // Add SOP creation date\n      if (item.date) {\n        dates.push(new Date(item.date));\n      }\n\n      // Return the highest date, or epoch if no valid dates\n      return dates.length > 0 ? new Date(Math.max(...dates)) : new Date(0);\n    };\n\n    // Start with department filtering\n    let filtered = gapResults;\n    if (selectedDepartment !== 'All') {\n      filtered = filtered.filter(item => item.department === selectedDepartment);\n    }\n\n    // Apply search term filter\n    if (searchTerm.trim() !== '') {\n      const term = searchTerm.toLowerCase();\n      filtered = filtered.filter(item => (item.title || '').toLowerCase().includes(term) || (item.department || '').toLowerCase().includes(term));\n    }\n\n    // Apply date filter\n    if (dateFilter !== 'all') {\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      const lastWeek = new Date(today);\n      lastWeek.setDate(lastWeek.getDate() - 7);\n      const lastMonth = new Date(today);\n      lastMonth.setMonth(lastMonth.getMonth() - 1);\n      filtered = filtered.filter(item => {\n        // Use the highest date among all available dates\n        const dateToUse = getHighestDate(item);\n        switch (dateFilter) {\n          case 'today':\n            return dateToUse >= today;\n          case 'yesterday':\n            return dateToUse >= yesterday && dateToUse < today;\n          case 'week':\n            return dateToUse >= lastWeek;\n          case 'month':\n            return dateToUse >= lastMonth;\n          default:\n            return true;\n        }\n      });\n    }\n\n    // Sort the filtered results\n    return filtered.sort((a, b) => {\n      if (sortField === 'date') {\n        const dateA = getHighestDate(a);\n        const dateB = getHighestDate(b);\n        return sortDirection === 'asc' ? dateA - dateB : dateB - dateA;\n      } else if (sortField === 'title') {\n        const titleA = (a.title || '').toLowerCase();\n        const titleB = (b.title || '').toLowerCase();\n        return sortDirection === 'asc' ? titleA.localeCompare(titleB) : titleB.localeCompare(titleA);\n      } else if (sortField === 'priority') {\n        // First, check if items have analysis data (analyzed vs not analyzed)\n        const aHasAnalysis = a.analysis && a.analysis.analyzed_at;\n        const bHasAnalysis = b.analysis && b.analysis.analyzed_at;\n\n        // If one has analysis and the other doesn't, prioritize the analyzed one\n        if (aHasAnalysis && !bHasAnalysis) {\n          return -1; // a comes first (analyzed items on top)\n        } else if (!aHasAnalysis && bHasAnalysis) {\n          return 1; // b comes first (analyzed items on top)\n        }\n\n        // Helper function to determine priority level of an item\n        const getPriorityLevel = item => {\n          if ((item.highPriorityGaps || 0) > 0) return 'high';\n          if ((item.mediumPriorityGaps || 0) > 0) return 'medium';\n          if ((item.lowPriorityGaps || 0) > 0) return 'low';\n          return 'none'; // No gaps\n        };\n        const priorityA = getPriorityLevel(a);\n        const priorityB = getPriorityLevel(b);\n\n        // Define priority order: high > medium > low > none\n        const priorityOrder = {\n          'high': 3,\n          'medium': 2,\n          'low': 1,\n          'none': 0\n        };\n        const valueA = priorityOrder[priorityA];\n        const valueB = priorityOrder[priorityB];\n\n        // Sort: desc = high to low (3,2,1,0), asc = low to high (0,1,2,3)\n        return sortDirection === 'asc' ? valueA - valueB : valueB - valueA;\n      }\n      return 0;\n    });\n  }, [gapResults, selectedDepartment, searchTerm, sortField, sortDirection, dateFilter]);\n  const handleDepartmentChange = department => {\n    setSelectedDepartment(department);\n    setSelectedRowIndex(null); // Clear selected row when changing departments\n    setIsSidebarOpen(false); // Close sidebar when changing departments\n  };\n  const handleViewDetails = index => {\n    setSelectedRowIndex(index);\n    const selectedSop = filteredResultsMemo[index];\n\n    // Check if we already have analysis data for this SOP\n    if (selectedSop && selectedSop.analysis) {\n      // Use the existing analysis data\n      setAnalysisResults(selectedSop.analysis);\n      setIsSidebarOpen(true);\n    } else {\n      // Only make API call if we don't have the data already\n      setAnalysisLoading(true);\n      setAnalysisError(null);\n      apiService.post(API_URLS.ANALYSIS.ANALYZE, {\n        sop_id: selectedSop.id\n      }).then(response => {\n        setAnalysisResults(response);\n\n        // Also update the cached results for this SOP\n        setGapResults(prevResults => prevResults.map(sop => sop.id === selectedSop.id ? {\n          ...sop,\n          analysis: response\n        } : sop));\n      }).catch(err => {\n        console.error('Error analyzing SOP:', err);\n        setAnalysisError('Failed to analyze SOP. Please try again later.');\n        toast.error('Failed to analyze SOP. Please try again later.', {\n          toastId: `assessment-error-${Date.now()}`\n        });\n      }).finally(() => {\n        setAnalysisLoading(false);\n        setIsSidebarOpen(true);\n      });\n    }\n  };\n  const renderCreateAssessmentButton = () => {\n    return /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"create-assessment-btn\",\n      onClick: () => setIsModalOpen(true),\n      children: \"Create Assessment\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 513,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Function to handle sort changes\n  const handleSortChange = field => {\n    if (sortField === field) {\n      // Toggle direction if clicking the same field\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      // Set new field and default to descending for date, ascending for title\n      setSortField(field);\n      setSortDirection(field === 'date' ? 'desc' : 'asc');\n    }\n  };\n\n  // Add a new function to reset all filters\n  const resetFilters = () => {\n    setSearchTerm('');\n    setDateFilter('all');\n    setSortField('date');\n    setSortDirection('desc');\n    // Keep the department filter as is, since it's a primary filter\n  };\n\n  // Update the callback functions to track processing state with logging\n  const handleBeforeUpload = () => {\n    console.log('Setting isUploading to true');\n    setIsUploading(true);\n  };\n  const handleAfterUpload = () => {\n    console.log('Setting isUploading to false');\n    setIsUploading(false);\n  };\n\n  // Add functions to handle assessment processing\n  const handleAssessmentStart = sopId => {\n    console.log('Starting assessment processing for SOP:', sopId);\n    setProcessingSopId(sopId);\n    setIsModalOpen(false); // Close the modal when processing starts\n  };\n  const handleAssessmentComplete = () => {\n    console.log('Assessment processing complete');\n    setProcessingSopId(null);\n    refreshAllData(true); // Refresh data silently to show new assessment\n    handleAfterUpload();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"gap-analysis\",\n    children: [/*#__PURE__*/_jsxDEV(Navigation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 571,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"gap-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Gap Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 573,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DepartmentFilters, {\n        onDepartmentChange: handleDepartmentChange,\n        defaultSelected: \"All\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 577,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search gap analysis...\",\n            className: \"search-input\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 15\n          }, this), renderCreateAssessmentButton()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-sort-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Sort by:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sort-buttons\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: `sort-btn ${sortField === 'date' ? 'active' : ''}`,\n                onClick: () => handleSortChange('date'),\n                children: [\"Date\", sortField === 'date' && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sort-direction\",\n                  children: sortDirection === 'asc' ? '↑' : '↓'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 605,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `sort-btn ${sortField === 'title' ? 'active' : ''}`,\n                onClick: () => handleSortChange('title'),\n                children: [\"Title\", sortField === 'title' && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sort-direction\",\n                  children: sortDirection === 'asc' ? '↑' : '↓'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 610,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `sort-btn ${sortField === 'priority' ? 'active' : ''}`,\n                onClick: () => handleSortChange('priority'),\n                children: [\"Priority\", sortField === 'priority' && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sort-direction\",\n                  children: sortDirection === 'asc' ? '↑' : '↓'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 627,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Date:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: dateFilter,\n              onChange: e => setDateFilter(e.target.value),\n              className: \"filter-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Time\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"today\",\n                children: \"Today\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 643,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"yesterday\",\n                children: \"Yesterday\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"week\",\n                children: \"Last 7 Days\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"month\",\n                children: \"Last 30 Days\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 637,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"reset-filters-btn\",\n            onClick: resetFilters,\n            disabled: dateFilter === 'all' && sortField === 'date' && sortDirection === 'desc' && searchTerm === '',\n            children: \"Reset Filters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 595,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-container\",\n        children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"loading-text\",\n          children: \"Loading Gap Analysis Results...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 664,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 662,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"gap-results\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Gap Analysis Results\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 669,\n            columnNumber: 15\n          }, this), error ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 671,\n            columnNumber: 17\n          }, this) : filteredResultsMemo.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"empty-state\",\n            children: searchTerm || dateFilter !== 'all' ? `No results found matching your filters. Try different filter settings.` : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [\"No gap analysis results found. Create your first assessment by clicking \", renderCreateAssessmentButton()]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 673,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"gap-items\",\n            children: filteredResultsMemo.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `gap-item ${selectedRowIndex === index ? 'selected' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"gap-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `analysis-date ${!item.analysis || !item.analysis.analyzed_at ? 'no-analysis' : ''} ${item.analysis && item.analysis.analyzed_at && item.analysis.isValidated ? 'updated' : item.analysis && item.analysis.analyzed_at ? 'analyzed' : ''}`,\n                  children: item.analysis && item.analysis.analyzed_at ? item.analysis.isValidated ? `Updated on ${formatDate(item.analysis.updated_at || item.analysis.analyzed_at)}` : `Analyzed on ${formatDate(item.analysis.analyzed_at)}` : `Created on ${formatDate(item.date)}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 689,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: sanitizeText(item.title)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 699,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"gap-details\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"department\",\n                    children: sanitizeText(item.department)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 701,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 23\n              }, this), processingSopId !== item.id && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"gap-metrics-container\",\n                children: /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [item.totalGaps !== undefined && item.totalGaps !== null && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"gap-metric total-gaps\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"metric-label\",\n                      children: \"Total Gaps:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 709,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"metric-value\",\n                      children: item.totalGaps\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 710,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 708,\n                    columnNumber: 31\n                  }, this), item.highPriorityGaps !== undefined && item.highPriorityGaps !== null && item.highPriorityGaps > 0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"gap-metric high-priority\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"metric-label\",\n                      children: \"High Priority:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 716,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"metric-value\",\n                      children: item.highPriorityGaps\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 717,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 715,\n                    columnNumber: 31\n                  }, this) : item.mediumPriorityGaps !== undefined && item.mediumPriorityGaps !== null && item.mediumPriorityGaps > 0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"gap-metric medium-priority\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"metric-label\",\n                      children: \"Medium Priority:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 721,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"metric-value\",\n                      children: item.mediumPriorityGaps\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 722,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 720,\n                    columnNumber: 31\n                  }, this) : item.lowPriorityGaps !== undefined && item.lowPriorityGaps !== null && item.lowPriorityGaps > 0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"gap-metric low-priority\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"metric-label\",\n                      children: \"Low Priority:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 726,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"metric-value\",\n                      children: item.lowPriorityGaps\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 727,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 725,\n                    columnNumber: 31\n                  }, this) : null]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"gap-meta\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"gap-actions\",\n                  children: processingSopId === item.id ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"processing-state\",\n                    children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n                      size: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 739,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"processing-text\",\n                      children: \"Processing\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 740,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 738,\n                    columnNumber: 29\n                  }, this) : item.totalGaps !== undefined && item.totalGaps !== null ? /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"details-btn\",\n                    onClick: () => handleViewDetails(index),\n                    children: \"Details\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 743,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"no-details\",\n                    children: \"No Assessment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 750,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 735,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 23\n              }, this)]\n            }, item.id || index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 682,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 668,\n          columnNumber: 13\n        }, this)\n      }, void 0, false), /*#__PURE__*/_jsxDEV(Modal, {\n        isOpen: isModalOpen,\n        onClose: () => setIsModalOpen(false),\n        closeOnOutsideClick: true,\n        isProcessing: isUploading,\n        children: /*#__PURE__*/_jsxDEV(AssessmentForm, {\n          onClose: () => setIsModalOpen(false),\n          onSuccess: handleAssessmentComplete,\n          onBeforeUpload: handleBeforeUpload,\n          onProcessingStart: handleAssessmentStart\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 768,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 762,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(GapDetailsSidebar, {\n        closeOnOutsideClick: true,\n        isOpen: isSidebarOpen,\n        onClose: () => {\n          setIsSidebarOpen(false);\n          setSelectedRowIndex(null);\n          setAnalysisResults(null);\n        },\n        gapDetails: selectedRowIndex !== null ? filteredResultsMemo[selectedRowIndex] : null,\n        analysisResults: analysisResults,\n        loading: analysisLoading,\n        error: analysisError,\n        onRefreshData: refreshAllData,\n        onSilentUpdate: silentUpdateAnalysisData\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 776,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n        position: \"top-right\",\n        autoClose: 4000,\n        hideProgressBar: false,\n        newestOnTop: true,\n        closeOnClick: true,\n        rtl: false,\n        pauseOnFocusLoss: true,\n        draggable: true,\n        pauseOnHover: true,\n        theme: \"light\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 793,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 572,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 570,\n    columnNumber: 5\n  }, this);\n};\n_s(GapAnalysis, \"Cn5PlgucphalLWq6I2SRArfU6Fk=\");\n_c = GapAnalysis;\nexport default GapAnalysis;\nvar _c;\n$RefreshReg$(_c, \"GapAnalysis\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Navigation", "DepartmentFilters", "Modal", "AssessmentForm", "GapDetailsSidebar", "sopService", "apiService", "API_URLS", "LoadingSpinner", "ToastContainer", "toast", "sanitizeText", "formatDate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "GapAnalysis", "_s", "isModalOpen", "setIsModalOpen", "isSidebarOpen", "setIsSidebarOpen", "selectedRowIndex", "setSelectedRowIndex", "selectedDepartment", "setSelectedDepartment", "gapResults", "setGapResults", "loading", "setLoading", "error", "setError", "analysisResults", "setAnalysisResults", "analysisLoading", "setAnalysisLoading", "analysisError", "setAnalysisError", "searchTerm", "setSearchTerm", "isUploading", "setIsUploading", "processingSopId", "setProcessingSopId", "sortField", "setSortField", "sortDirection", "setSortDirection", "dateFilter", "setDateFilter", "fetchSOPs", "sops", "getAllSOPs", "transformedResults", "sops_data", "map", "sop", "_sop$metadata", "_sop$metadata2", "department", "metadata", "id", "title", "date", "created_at", "Date", "toISOString", "totalGaps", "total_gaps", "highPriorityGaps", "high_priority_gaps", "mediumPriorityGaps", "medium_priority_gaps", "lowPriorityGaps", "low_priority_gaps", "length", "fetchAnalysisResults", "err", "_err$response", "_err$response$data", "console", "errorMessage", "response", "data", "detail", "_err$response2", "_err$response2$data", "setTimeout", "sopIds", "post", "ANALYSIS", "RESULTS", "results", "resultsMap", "for<PERSON>ach", "result", "sop_id", "gapDetailsToUse", "isValidated", "changed_gap_detail", "Array", "isArray", "log", "gap_details", "prevResults", "analysis", "_err$response3", "_err$response3$data", "_err$response4", "_err$response4$data", "silentUpdateAnalysisData", "sopId", "Promise", "resolve", "updatedSop", "find", "updatedResults", "_err$response5", "_err$response5$data", "_err$response6", "_err$response6$data", "refreshAllData", "silent", "_sop$metadata3", "_sop$metadata4", "_err$response7", "_err$response7$data", "_err$response8", "_err$response8$data", "filteredResultsMemo", "useMemo", "getHighestDate", "item", "dates", "analyzed_at", "push", "updated_at", "Math", "max", "filtered", "filter", "trim", "term", "toLowerCase", "includes", "now", "today", "getFullYear", "getMonth", "getDate", "yesterday", "setDate", "lastWeek", "lastM<PERSON>h", "setMonth", "dateToUse", "sort", "a", "b", "dateA", "dateB", "titleA", "titleB", "localeCompare", "aHasAnalysis", "bHasAnalysis", "getPriorityLevel", "priorityA", "priorityB", "priorityOrder", "valueA", "valueB", "handleDepartmentChange", "handleViewDetails", "index", "selectedSop", "ANALYZE", "then", "catch", "toastId", "finally", "renderCreateAssessmentButton", "className", "onClick", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleSortChange", "field", "resetFilters", "handleBeforeUpload", "handleAfterUpload", "handleAssessmentStart", "handleAssessmentComplete", "onDepartmentChange", "defaultSelected", "type", "placeholder", "value", "onChange", "e", "target", "disabled", "size", "undefined", "isOpen", "onClose", "closeOnOutsideClick", "isProcessing", "onSuccess", "onBeforeUpload", "onProcessingStart", "gapDetails", "onRefreshData", "onSilentUpdate", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/components/pages/GapAnalysis/GapAnalysis.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport Navigation from '../../common/Navigation/Navigation';\r\nimport DepartmentFilters from '../../common/DepartmentFilters/DepartmentFilters';\r\nimport Modal from '../../common/Modal/Modal';\r\nimport AssessmentForm from './AssessmentForm';\r\nimport GapDetailsSidebar from './GapDetailsSidebar';\r\nimport sopService from '../../../services/sopService';\r\nimport apiService from '../../../services/api';\r\nimport API_URLS from '../../../config/apiUrls';\r\nimport LoadingSpinner from '../../common/LoadingSpinner/LoadingSpinner';\r\nimport { ToastContainer } from 'react-toastify';\r\nimport 'react-toastify/dist/ReactToastify.css';\r\nimport './GapAnalysis.css';\r\nimport { toast } from 'react-toastify';\r\nimport { sanitizeText } from '../../../utils/sanitize';\r\nimport { formatDate } from '../../../utils/dateUtils';\r\n\r\nconst GapAnalysis = () => {\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\r\n  const [selectedRowIndex, setSelectedRowIndex] = useState(null);\r\n  const [selectedDepartment, setSelectedDepartment] = useState('All');\r\n  const [gapResults, setGapResults] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [analysisResults, setAnalysisResults] = useState(null);\r\n  const [analysisLoading, setAnalysisLoading] = useState(false);\r\n  const [analysisError, setAnalysisError] = useState(null);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [processingSopId, setProcessingSopId] = useState(null);\r\n  \r\n  // Add sorting and filtering states\r\n  const [sortField, setSortField] = useState('date');\r\n  const [sortDirection, setSortDirection] = useState('desc');\r\n  const [dateFilter, setDateFilter] = useState('all');\r\n\r\n  // const stats = [\r\n  //   {\r\n  //     title: 'Compliant SOPs',\r\n  //     value: '76%',\r\n  //     description: '38 of 50 SOPs meet requirements',\r\n  //     action: 'View Details',\r\n  //     disabled: true\r\n  //   },\r\n  //   {\r\n  //     title: 'Non-Compliant',\r\n  //     value: '24%',\r\n  //     description: '12 SOPs require attention',\r\n  //     action: 'View Issues',\r\n  //     disabled: true\r\n  //   },\r\n  //   {\r\n  //     title: 'Critical Gaps',\r\n  //     value: '5',\r\n  //     description: 'High-priority items needing immediate action',\r\n  //     action: 'Resolve Now',\r\n  //     disabled: true\r\n  //   },\r\n  //   {\r\n  //     title: 'Last Assessment',\r\n  //     value: 'June 12, 2023',\r\n  //     description: 'Next scheduled review in 14 days',\r\n  //     action: 'Schedule Review',\r\n  //     disabled: true\r\n  //   }\r\n  // ];\r\n\r\n  // Modify the useEffect that fetches SOPs to also fetch analysis results\r\n  useEffect(() => {\r\n    const fetchSOPs = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const sops = await sopService.getAllSOPs();\r\n        \r\n        // Transform SOPs into gap analysis results\r\n        const transformedResults = sops.sops_data.map(sop => {\r\n          const department = sop.metadata?.department || 'Unassigned';\r\n          \r\n          return {\r\n            id: sop.id,\r\n            title: sop.title || 'Unnamed SOP',\r\n            department: department,\r\n            date: sop.metadata?.date || sop.created_at || new Date().toISOString(),\r\n            // Extract gap metrics directly from SOP data\r\n            totalGaps: sop.total_gaps,\r\n            highPriorityGaps: sop.high_priority_gaps,\r\n            mediumPriorityGaps: sop.medium_priority_gaps,\r\n            lowPriorityGaps: sop.low_priority_gaps,\r\n          };\r\n        });\r\n        \r\n        setGapResults(transformedResults);\r\n        \r\n        // After fetching SOPs, fetch analysis results for all SOPs\r\n        if (transformedResults.length > 0) {\r\n          fetchAnalysisResults(transformedResults.map(sop => sop.id));\r\n        }\r\n      } catch (err) {\r\n        console.error('Error fetching SOPs:', err);\r\n        \r\n        let errorMessage = 'Failed to load gap analysis results. Please try again later.';\r\n        if (err?.response?.data?.detail) {\r\n          errorMessage = err?.response?.data?.detail;\r\n        }\r\n        \r\n        setError(errorMessage);\r\n      } finally {\r\n        setTimeout(() => {\r\n          setLoading(false);\r\n          \r\n        }, 1000);\r\n      }\r\n    };\r\n\r\n    fetchSOPs();\r\n  }, []);\r\n\r\n  // Update the fetchAnalysisResults function to properly check for changed_gap_details\r\n  const fetchAnalysisResults = async (sopIds) => {\r\n    try {\r\n      setAnalysisLoading(true);\r\n      setAnalysisError(null);\r\n      \r\n      // Call the /results endpoint with the SOP IDs as payload\r\n      const response = await apiService.post(API_URLS.ANALYSIS.RESULTS, sopIds);\r\n      \r\n      // Store the analysis results in state\r\n      if (response && response.results) {\r\n        // Map the results to the corresponding SOPs\r\n        const resultsMap = {};\r\n        response.results.forEach(result => {\r\n          if (result.sop_id) {\r\n            // Determine which gap details to use - prefer changed_gap_detail if available\r\n            let gapDetailsToUse = [];\r\n            let isValidated = false;\r\n            \r\n            if (result.metadata && result.metadata.changed_gap_detail && \r\n                Array.isArray(result.metadata.changed_gap_detail) && \r\n                result.metadata.changed_gap_detail.length > 0) {\r\n              // Use the validated/changed gap details if available\r\n              gapDetailsToUse = result.metadata.changed_gap_detail;\r\n              isValidated = true;\r\n              console.log('Using changed_gap_detail for SOP:', result.sop_id, '- Validated: true');\r\n            } else if (result.gap_details) {\r\n              // Fall back to original gap details\r\n              gapDetailsToUse = result.gap_details;\r\n              isValidated = false;\r\n              console.log('Using original gap_details for SOP:', result.sop_id, '- Validated: false');\r\n            }\r\n            \r\n            // Ensure gap_details is always an array\r\n            if (!Array.isArray(gapDetailsToUse)) {\r\n              gapDetailsToUse = [];\r\n            }\r\n            \r\n            // Use direct values from API response instead of client-side calculations\r\n            console.log(`SOP ${result.sop_id}: Analysis data available`);\r\n            \r\n            // Store the result with gap details and validation status\r\n            resultsMap[result.sop_id] = {\r\n              ...result,\r\n              // Store the gap details we're using for consistency\r\n              gap_details: gapDetailsToUse,\r\n              // Store validation status for display logic\r\n              isValidated: isValidated\r\n            };\r\n          }\r\n        });\r\n        \r\n        // Update the gap results with analysis data\r\n        setGapResults(prevResults => \r\n          prevResults.map(sop => ({\r\n            ...sop,\r\n            analysis: resultsMap[sop.id] || { gap_details: [], isValidated: false }\r\n          }))\r\n        );\r\n        \r\n        console.log('Updated gap results with analysis data and validation status');\r\n      }\r\n    } catch (err) {\r\n      console.error('Error fetching analysis results:', err);\r\n      \r\n      let errorMessage = 'Failed to load analysis results. Please try again later.';\r\n      if (err?.response?.data?.detail) {\r\n        errorMessage = err?.response?.data?.detail;\r\n      }\r\n      \r\n      setAnalysisError(errorMessage);\r\n    } finally {\r\n      setAnalysisLoading(false);\r\n    }\r\n  };\r\n\r\n  // Add a silent update function that doesn't show loading states\r\n  const silentUpdateAnalysisData = async (sopId) => {\r\n    try {\r\n      console.log('Silently updating analysis data for SOP:', sopId);\r\n      \r\n      // Fetch updated analysis results for the specific SOP\r\n      const response = await apiService.post(API_URLS.ANALYSIS.RESULTS, [sopId]);\r\n      \r\n      if (response && response.results && response.results.length > 0) {\r\n        const result = response.results[0];\r\n        \r\n        if (result.sop_id === sopId) {\r\n          // Determine which gap details to use\r\n          let gapDetailsToUse = [];\r\n          let isValidated = false;\r\n          \r\n          if (result.metadata && result.metadata.changed_gap_detail && \r\n              Array.isArray(result.metadata.changed_gap_detail) && \r\n              result.metadata.changed_gap_detail.length > 0) {\r\n            gapDetailsToUse = result.metadata.changed_gap_detail;\r\n            isValidated = true;\r\n          } else if (result.gap_details) {\r\n            gapDetailsToUse = result.gap_details;\r\n            isValidated = false;\r\n          }\r\n          \r\n          if (!Array.isArray(gapDetailsToUse)) {\r\n            gapDetailsToUse = [];\r\n          }\r\n          \r\n          // Add a small delay to ensure backend has processed the changes\r\n          await new Promise(resolve => setTimeout(resolve, 1000));\r\n          \r\n          // Also fetch updated SOP data to get latest gap metrics\r\n          console.log('Fetching updated SOP data for ID:', sopId);\r\n          const sops = await sopService.getAllSOPs();\r\n          const updatedSop = sops.sops_data.find(sop => sop.id === sopId);\r\n          \r\n          console.log('Updated SOP data:', updatedSop);\r\n          console.log('Gap metrics - Total:', updatedSop?.total_gaps, 'High:', updatedSop?.high_priority_gaps, 'Medium:', updatedSop?.medium_priority_gaps, 'Low:', updatedSop?.low_priority_gaps);\r\n          \r\n          // Update the specific SOP's analysis data and gap metrics\r\n          setGapResults(prevResults => {\r\n            const updatedResults = prevResults.map(sop => \r\n              sop.id === sopId \r\n                ? { \r\n                    ...sop, \r\n                    // Update gap metrics from SOP data\r\n                    totalGaps: updatedSop?.total_gaps || sop.totalGaps,\r\n                    highPriorityGaps: updatedSop?.high_priority_gaps || sop.highPriorityGaps,\r\n                    mediumPriorityGaps: updatedSop?.medium_priority_gaps || sop.mediumPriorityGaps,\r\n                    lowPriorityGaps: updatedSop?.low_priority_gaps || sop.lowPriorityGaps,\r\n                    // Update analysis data\r\n                    analysis: {\r\n                      ...result,\r\n                      gap_details: gapDetailsToUse,\r\n                      isValidated: isValidated\r\n                    }\r\n                  } \r\n                : sop\r\n            );\r\n            \r\n            console.log('Updated gap results:', updatedResults);\r\n            return updatedResults;\r\n          });\r\n          \r\n          // Update analysisResults if it's for the same SOP\r\n          setAnalysisResults(prevResults => {\r\n            if (prevResults && prevResults.sop_id === sopId) {\r\n              return {\r\n                ...result,\r\n                gap_details: gapDetailsToUse,\r\n                isValidated: isValidated\r\n              };\r\n            }\r\n            return prevResults;\r\n          });\r\n          \r\n          console.log('Successfully updated analysis data and gap metrics silently for SOP:', sopId);\r\n        }\r\n      }\r\n    } catch (err) {\r\n      console.error('Error silently updating analysis data:', err);\r\n      \r\n      let errorMessage = 'Failed to update analysis data.';\r\n      if (err?.response?.data?.detail) {\r\n        errorMessage = err?.response?.data?.detail;\r\n      }\r\n      \r\n      // Don't show error toast for silent updates to avoid disrupting UX\r\n    }\r\n  };\r\n\r\n  // Improve the refreshAllData function to ensure it properly updates the UI\r\n  const refreshAllData = async (silent = false) => {\r\n    try {\r\n      console.log('Refreshing gap analysis data...');\r\n      if (!silent) {\r\n        setLoading(true);\r\n      }\r\n      \r\n      // Fetch SOPs\r\n      const sops = await sopService.getAllSOPs();\r\n      console.log('Fetched SOPs:', sops);\r\n      \r\n      // Transform SOPs into gap analysis results\r\n      const transformedResults = sops.sops_data.map(sop => {\r\n        const department = sop.metadata?.department || 'Unassigned';\r\n        \r\n        return {\r\n          id: sop.id,\r\n          title: sop.title || 'Unnamed SOP',\r\n          department: department,\r\n          date: sop.metadata?.date || sop.created_at || new Date().toISOString(),\r\n          // Extract gap metrics directly from SOP data\r\n          totalGaps: sop.total_gaps,\r\n          highPriorityGaps: sop.high_priority_gaps,\r\n          mediumPriorityGaps: sop.medium_priority_gaps,\r\n          lowPriorityGaps: sop.low_priority_gaps,\r\n        };\r\n      });\r\n      \r\n      setGapResults(transformedResults);\r\n      console.log('Updated gap results:', transformedResults);\r\n      \r\n      // Fetch analysis results for all SOPs\r\n      if (transformedResults.length > 0) {\r\n        const sopIds = transformedResults.map(sop => sop.id);\r\n        console.log('Fetching analysis results for SOPs:', sopIds);\r\n        await fetchAnalysisResults(sopIds);\r\n      }\r\n      \r\n      setError(null);\r\n      console.log('Gap analysis data refresh complete');\r\n    } catch (err) {\r\n      console.error('Error refreshing gap analysis data:', err);\r\n      \r\n      let errorMessage = 'Failed to load gap analysis results. Please try again later.';\r\n      if (err?.response?.data?.detail) {\r\n        errorMessage = err?.response?.data?.detail;\r\n      }\r\n      \r\n      setError(errorMessage);\r\n    } finally {\r\n      if (!silent) {\r\n        setLoading(false);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Filter results based on selected department\r\n  const filteredResultsMemo = React.useMemo(() => {\r\n    // Helper function to get the highest date from available dates (shared between filtering and sorting)\r\n    const getHighestDate = (item) => {\r\n      const dates = [];\r\n      \r\n      // Add analysis dates if they exist\r\n      if (item.analysis && item.analysis.analyzed_at) {\r\n        dates.push(new Date(item.analysis.analyzed_at));\r\n      }\r\n      if (item.analysis && item.analysis.updated_at) {\r\n        dates.push(new Date(item.analysis.updated_at));\r\n      }\r\n      \r\n      // Add SOP creation date\r\n      if (item.date) {\r\n        dates.push(new Date(item.date));\r\n      }\r\n      \r\n      // Return the highest date, or epoch if no valid dates\r\n      return dates.length > 0 ? new Date(Math.max(...dates)) : new Date(0);\r\n    };\r\n\r\n    // Start with department filtering\r\n    let filtered = gapResults;\r\n    \r\n    if (selectedDepartment !== 'All') {\r\n      filtered = filtered.filter(item => item.department === selectedDepartment);\r\n    }\r\n    \r\n    // Apply search term filter\r\n    if (searchTerm.trim() !== '') {\r\n      const term = searchTerm.toLowerCase();\r\n      filtered = filtered.filter(item => \r\n        (item.title || '').toLowerCase().includes(term) ||\r\n        (item.department || '').toLowerCase().includes(term)\r\n      );\r\n    }\r\n    \r\n    // Apply date filter\r\n    if (dateFilter !== 'all') {\r\n      const now = new Date();\r\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\r\n      const yesterday = new Date(today);\r\n      yesterday.setDate(yesterday.getDate() - 1);\r\n      const lastWeek = new Date(today);\r\n      lastWeek.setDate(lastWeek.getDate() - 7);\r\n      const lastMonth = new Date(today);\r\n      lastMonth.setMonth(lastMonth.getMonth() - 1);\r\n      \r\n      filtered = filtered.filter(item => {\r\n        // Use the highest date among all available dates\r\n        const dateToUse = getHighestDate(item);\r\n        \r\n        switch (dateFilter) {\r\n          case 'today':\r\n            return dateToUse >= today;\r\n          case 'yesterday':\r\n            return dateToUse >= yesterday && dateToUse < today;\r\n          case 'week':\r\n            return dateToUse >= lastWeek;\r\n          case 'month':\r\n            return dateToUse >= lastMonth;\r\n          default:\r\n            return true;\r\n        }\r\n      });\r\n    }\r\n    \r\n    // Sort the filtered results\r\n    return filtered.sort((a, b) => {\r\n      if (sortField === 'date') {\r\n        const dateA = getHighestDate(a);\r\n        const dateB = getHighestDate(b);\r\n        \r\n        return sortDirection === 'asc' ? dateA - dateB : dateB - dateA;\r\n      } else if (sortField === 'title') {\r\n        const titleA = (a.title || '').toLowerCase();\r\n        const titleB = (b.title || '').toLowerCase();\r\n        \r\n        return sortDirection === 'asc' \r\n          ? titleA.localeCompare(titleB)\r\n          : titleB.localeCompare(titleA);\r\n      } else if (sortField === 'priority') {\r\n        // First, check if items have analysis data (analyzed vs not analyzed)\r\n        const aHasAnalysis = a.analysis && a.analysis.analyzed_at;\r\n        const bHasAnalysis = b.analysis && b.analysis.analyzed_at;\r\n        \r\n        // If one has analysis and the other doesn't, prioritize the analyzed one\r\n        if (aHasAnalysis && !bHasAnalysis) {\r\n          return -1; // a comes first (analyzed items on top)\r\n        } else if (!aHasAnalysis && bHasAnalysis) {\r\n          return 1; // b comes first (analyzed items on top)\r\n        }\r\n        \r\n        // Helper function to determine priority level of an item\r\n        const getPriorityLevel = (item) => {\r\n          if ((item.highPriorityGaps || 0) > 0) return 'high';\r\n          if ((item.mediumPriorityGaps || 0) > 0) return 'medium';\r\n          if ((item.lowPriorityGaps || 0) > 0) return 'low';\r\n          return 'none'; // No gaps\r\n        };\r\n        \r\n        const priorityA = getPriorityLevel(a);\r\n        const priorityB = getPriorityLevel(b);\r\n        \r\n        // Define priority order: high > medium > low > none\r\n        const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1, 'none': 0 };\r\n        const valueA = priorityOrder[priorityA];\r\n        const valueB = priorityOrder[priorityB];\r\n        \r\n        // Sort: desc = high to low (3,2,1,0), asc = low to high (0,1,2,3)\r\n        return sortDirection === 'asc' ? valueA - valueB : valueB - valueA;\r\n      }\r\n      return 0;\r\n    });\r\n  }, [gapResults, selectedDepartment, searchTerm, sortField, sortDirection, dateFilter]);\r\n\r\n  const handleDepartmentChange = (department) => {\r\n    setSelectedDepartment(department);\r\n    setSelectedRowIndex(null); // Clear selected row when changing departments\r\n    setIsSidebarOpen(false); // Close sidebar when changing departments\r\n  };\r\n\r\n  const handleViewDetails = (index) => {\r\n    setSelectedRowIndex(index);\r\n    \r\n    const selectedSop = filteredResultsMemo[index];\r\n    \r\n    // Check if we already have analysis data for this SOP\r\n    if (selectedSop && selectedSop.analysis) {\r\n      // Use the existing analysis data\r\n      setAnalysisResults(selectedSop.analysis);\r\n      setIsSidebarOpen(true);\r\n    } else {\r\n      // Only make API call if we don't have the data already\r\n      setAnalysisLoading(true);\r\n      setAnalysisError(null);\r\n      \r\n      apiService.post(API_URLS.ANALYSIS.ANALYZE, { sop_id: selectedSop.id })\r\n        .then(response => {\r\n          setAnalysisResults(response);\r\n          \r\n          // Also update the cached results for this SOP\r\n          setGapResults(prevResults => \r\n            prevResults.map(sop => \r\n              sop.id === selectedSop.id \r\n                ? { ...sop, analysis: response } \r\n                : sop\r\n            )\r\n          );\r\n        })\r\n        .catch(err => {\r\n          console.error('Error analyzing SOP:', err);\r\n          setAnalysisError('Failed to analyze SOP. Please try again later.');\r\n          toast.error('Failed to analyze SOP. Please try again later.', {\r\n            toastId: `assessment-error-${Date.now()}`\r\n          });\r\n        })\r\n        .finally(() => {\r\n          setAnalysisLoading(false);\r\n          setIsSidebarOpen(true);\r\n        });\r\n    }\r\n  };\r\n\r\n  const renderCreateAssessmentButton = () => {\r\n    return (\r\n      <button \r\n        className=\"create-assessment-btn\"\r\n        onClick={() => setIsModalOpen(true)}\r\n      >\r\n        Create Assessment\r\n      </button>\r\n    )\r\n  }\r\n\r\n  // Function to handle sort changes\r\n  const handleSortChange = (field) => {\r\n    if (sortField === field) {\r\n      // Toggle direction if clicking the same field\r\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\r\n    } else {\r\n      // Set new field and default to descending for date, ascending for title\r\n      setSortField(field);\r\n      setSortDirection(field === 'date' ? 'desc' : 'asc');\r\n    }\r\n  };\r\n\r\n  // Add a new function to reset all filters\r\n  const resetFilters = () => {\r\n    setSearchTerm('');\r\n    setDateFilter('all');\r\n    setSortField('date');\r\n    setSortDirection('desc');\r\n    // Keep the department filter as is, since it's a primary filter\r\n  };\r\n\r\n\r\n  // Update the callback functions to track processing state with logging\r\n  const handleBeforeUpload = () => {\r\n    console.log('Setting isUploading to true');\r\n    setIsUploading(true);\r\n  };\r\n\r\n  const handleAfterUpload = () => {\r\n    console.log('Setting isUploading to false');\r\n    setIsUploading(false);\r\n  };\r\n\r\n  // Add functions to handle assessment processing\r\n  const handleAssessmentStart = (sopId) => {\r\n    console.log('Starting assessment processing for SOP:', sopId);\r\n    setProcessingSopId(sopId);\r\n    setIsModalOpen(false); // Close the modal when processing starts\r\n  };\r\n\r\n  const handleAssessmentComplete = () => {\r\n    console.log('Assessment processing complete');\r\n    setProcessingSopId(null);\r\n    refreshAllData(true); // Refresh data silently to show new assessment\r\n    handleAfterUpload();\r\n  };\r\n\r\n  return (\r\n    <div className=\"gap-analysis\">\r\n      <Navigation />\r\n      <div className=\"gap-content\">\r\n        <div className=\"page-header\">\r\n          <h2>Gap Analysis</h2>\r\n        </div>\r\n\r\n        <DepartmentFilters \r\n          onDepartmentChange={handleDepartmentChange}\r\n          defaultSelected=\"All\"\r\n        />\r\n\r\n       \r\n          <>\r\n            <div className=\"search-section\">\r\n              <input \r\n                type=\"text\" \r\n                placeholder=\"Search gap analysis...\" \r\n                className=\"search-input\"\r\n                value={searchTerm}\r\n                onChange={(e) => setSearchTerm(e.target.value)}\r\n              />\r\n              {renderCreateAssessmentButton()}\r\n            </div>\r\n            \r\n            <div className=\"filter-sort-controls\">\r\n              <div className=\"filter-group\">\r\n                <label>Sort by:</label>\r\n                <div className=\"sort-buttons\">\r\n                  <button \r\n                    className={`sort-btn ${sortField === 'date' ? 'active' : ''}`}\r\n                    onClick={() => handleSortChange('date')}\r\n                  >\r\n                    Date\r\n                    {sortField === 'date' && (\r\n                      <span className=\"sort-direction\">\r\n                        {sortDirection === 'asc' ? '↑' : '↓'}\r\n                      </span>\r\n                    )}\r\n                  </button>\r\n                  <button \r\n                    className={`sort-btn ${sortField === 'title' ? 'active' : ''}`}\r\n                    onClick={() => handleSortChange('title')}\r\n                  >\r\n                    Title\r\n                    {sortField === 'title' && (\r\n                      <span className=\"sort-direction\">\r\n                        {sortDirection === 'asc' ? '↑' : '↓'}\r\n                      </span>\r\n                    )}\r\n                  </button>\r\n                  <button \r\n                    className={`sort-btn ${sortField === 'priority' ? 'active' : ''}`}\r\n                    onClick={() => handleSortChange('priority')}\r\n                  >\r\n                    Priority\r\n                    {sortField === 'priority' && (\r\n                      <span className=\"sort-direction\">\r\n                        {sortDirection === 'asc' ? '↑' : '↓'}\r\n                      </span>\r\n                    )}\r\n                  </button>\r\n                </div>\r\n              </div>\r\n              \r\n              <div className=\"filter-group\">\r\n                <label>Date:</label>\r\n                <select \r\n                  value={dateFilter} \r\n                  onChange={(e) => setDateFilter(e.target.value)}\r\n                  className=\"filter-select\"\r\n                >\r\n                  <option value=\"all\">All Time</option>\r\n                  <option value=\"today\">Today</option>\r\n                  <option value=\"yesterday\">Yesterday</option>\r\n                  <option value=\"week\">Last 7 Days</option>\r\n                  <option value=\"month\">Last 30 Days</option>\r\n                </select>\r\n              </div>\r\n              \r\n              <button \r\n                className=\"reset-filters-btn\"\r\n                onClick={resetFilters}\r\n                disabled={dateFilter === 'all' && sortField === 'date' && sortDirection === 'desc' && searchTerm === ''}\r\n              >\r\n                Reset Filters\r\n              </button>\r\n            </div>\r\n          </>\r\n       \r\n\r\n        {loading ? (\r\n          <div className=\"loading-container\">\r\n            <LoadingSpinner size=\"large\" />\r\n            <span className=\"loading-text\">Loading Gap Analysis Results...</span>\r\n          </div>\r\n        ) : (\r\n          <>\r\n            <div className=\"gap-results\">\r\n              <h2>Gap Analysis Results</h2>\r\n              {error ? (\r\n                <div className=\"error-message\">{error}</div>\r\n              ) : filteredResultsMemo.length === 0 ? (\r\n                <div className=\"empty-state\">\r\n                  {searchTerm || dateFilter !== 'all' ? \r\n                    `No results found matching your filters. Try different filter settings.` : \r\n                    <>\r\n                      No gap analysis results found. Create your first assessment by clicking {renderCreateAssessmentButton()}\r\n                    </>\r\n                  }\r\n                </div>\r\n              ) : (\r\n                <div className=\"gap-items\">\r\n                  {filteredResultsMemo.map((item, index) => (\r\n                    <div \r\n                      key={item.id || index} \r\n                      className={`gap-item ${selectedRowIndex === index ? 'selected' : ''}`}\r\n                    >\r\n                      <div className=\"gap-info\">\r\n                        <span className={`analysis-date ${!item.analysis || !item.analysis.analyzed_at ? 'no-analysis' : ''} ${item.analysis && item.analysis.analyzed_at && item.analysis.isValidated ? 'updated' : item.analysis && item.analysis.analyzed_at ? 'analyzed' : ''}`}>\r\n                         {/* Display logic based on validation status */}\r\n                          {item.analysis && item.analysis.analyzed_at \r\n                            ? (item.analysis.isValidated \r\n                                ? `Updated on ${formatDate(item.analysis.updated_at || item.analysis.analyzed_at)}` \r\n                                : `Analyzed on ${formatDate(item.analysis.analyzed_at)}`)\r\n                            : `Created on ${formatDate(item.date)}`\r\n                            \r\n                          }\r\n                        </span>\r\n                        <h3>{sanitizeText(item.title)}</h3>\r\n                        <div className=\"gap-details\">\r\n                          <span className=\"department\">{sanitizeText(item.department)}</span>\r\n                        </div>\r\n                      </div>\r\n                      {processingSopId !== item.id && (\r\n                        <div className=\"gap-metrics-container\">\r\n                          <>\r\n                            {item.totalGaps !== undefined && item.totalGaps !== null && (\r\n                              <span className=\"gap-metric total-gaps\">\r\n                                <span className=\"metric-label\">Total Gaps:</span> \r\n                                <span className=\"metric-value\">{item.totalGaps}</span>\r\n                              </span>\r\n                            )}\r\n                            \r\n                            {item.highPriorityGaps !== undefined && item.highPriorityGaps !== null && item.highPriorityGaps > 0 ? (\r\n                              <span className=\"gap-metric high-priority\">\r\n                                <span className=\"metric-label\">High Priority:</span> \r\n                                <span className=\"metric-value\">{item.highPriorityGaps}</span>\r\n                              </span>\r\n                            ) : item.mediumPriorityGaps !== undefined && item.mediumPriorityGaps !== null && item.mediumPriorityGaps > 0 ? (\r\n                              <span className=\"gap-metric medium-priority\">\r\n                                <span className=\"metric-label\">Medium Priority:</span> \r\n                                <span className=\"metric-value\">{item.mediumPriorityGaps}</span>\r\n                              </span>\r\n                            ) : item.lowPriorityGaps !== undefined && item.lowPriorityGaps !== null && item.lowPriorityGaps > 0 ? (\r\n                              <span className=\"gap-metric low-priority\">\r\n                                <span className=\"metric-label\">Low Priority:</span> \r\n                                <span className=\"metric-value\">{item.lowPriorityGaps}</span>\r\n                              </span>\r\n                            ) : null}\r\n                            \r\n                          </>\r\n                        </div>\r\n                      )}\r\n                      <div className=\"gap-meta\">\r\n                        <div className=\"gap-actions\">\r\n                          {/* Only show Details button if analysis exists */}\r\n                          {processingSopId === item.id ? (\r\n                            <div className=\"processing-state\">\r\n                              <LoadingSpinner size=\"small\" />\r\n                              <span className=\"processing-text\">Processing</span>\r\n                            </div>\r\n                          ) : item.totalGaps !== undefined && item.totalGaps !== null ? (\r\n                            <button \r\n                              className=\"details-btn\"\r\n                              onClick={() => handleViewDetails(index)}\r\n                            >\r\n                              Details\r\n                            </button>\r\n                          ) : (\r\n                            <span className=\"no-details\">No Assessment</span>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </>\r\n        )}\r\n\r\n        <Modal \r\n          isOpen={isModalOpen} \r\n          onClose={() => setIsModalOpen(false)}  \r\n          closeOnOutsideClick={true}\r\n          isProcessing={isUploading}\r\n        >\r\n          <AssessmentForm \r\n            onClose={() => setIsModalOpen(false)}\r\n            onSuccess={handleAssessmentComplete}\r\n            onBeforeUpload={handleBeforeUpload}\r\n            onProcessingStart={handleAssessmentStart}\r\n          />\r\n        </Modal>\r\n         \r\n            <GapDetailsSidebar \r\n            closeOnOutsideClick={true}  \r\n          isOpen={isSidebarOpen}\r\n          onClose={() => {\r\n            setIsSidebarOpen(false);\r\n            setSelectedRowIndex(null);\r\n            setAnalysisResults(null);\r\n          }}\r\n          gapDetails={selectedRowIndex !== null ? filteredResultsMemo[selectedRowIndex] : null}\r\n          analysisResults={analysisResults}\r\n          loading={analysisLoading}\r\n          error={analysisError}\r\n          onRefreshData={refreshAllData}\r\n          onSilentUpdate={silentUpdateAnalysisData}\r\n        />\r\n            \r\n        \r\n        <ToastContainer \r\n          position=\"top-right\"\r\n          autoClose={4000}\r\n          hideProgressBar={false}\r\n          newestOnTop\r\n          closeOnClick\r\n          rtl={false}\r\n          pauseOnFocusLoss\r\n          draggable\r\n          pauseOnHover\r\n          theme=\"light\"\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default GapAnalysis; \r\n "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,iBAAiB,MAAM,kDAAkD;AAChF,OAAOC,KAAK,MAAM,0BAA0B;AAC5C,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,UAAU,MAAM,8BAA8B;AACrD,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,OAAOC,cAAc,MAAM,4CAA4C;AACvE,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,uCAAuC;AAC9C,OAAO,mBAAmB;AAC1B,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,UAAU,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC2B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiC,KAAK,EAAEC,QAAQ,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACmC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6C,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAM,CAAC+C,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,MAAM,CAAC;EAClD,MAAM,CAACiD,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,MAAM,CAAC;EAC1D,MAAM,CAACmD,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoD,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFrB,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMsB,IAAI,GAAG,MAAM/C,UAAU,CAACgD,UAAU,CAAC,CAAC;;QAE1C;QACA,MAAMC,kBAAkB,GAAGF,IAAI,CAACG,SAAS,CAACC,GAAG,CAACC,GAAG,IAAI;UAAA,IAAAC,aAAA,EAAAC,cAAA;UACnD,MAAMC,UAAU,GAAG,EAAAF,aAAA,GAAAD,GAAG,CAACI,QAAQ,cAAAH,aAAA,uBAAZA,aAAA,CAAcE,UAAU,KAAI,YAAY;UAE3D,OAAO;YACLE,EAAE,EAAEL,GAAG,CAACK,EAAE;YACVC,KAAK,EAAEN,GAAG,CAACM,KAAK,IAAI,aAAa;YACjCH,UAAU,EAAEA,UAAU;YACtBI,IAAI,EAAE,EAAAL,cAAA,GAAAF,GAAG,CAACI,QAAQ,cAAAF,cAAA,uBAAZA,cAAA,CAAcK,IAAI,KAAIP,GAAG,CAACQ,UAAU,IAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACtE;YACAC,SAAS,EAAEX,GAAG,CAACY,UAAU;YACzBC,gBAAgB,EAAEb,GAAG,CAACc,kBAAkB;YACxCC,kBAAkB,EAAEf,GAAG,CAACgB,oBAAoB;YAC5CC,eAAe,EAAEjB,GAAG,CAACkB;UACvB,CAAC;QACH,CAAC,CAAC;QAEF/C,aAAa,CAAC0B,kBAAkB,CAAC;;QAEjC;QACA,IAAIA,kBAAkB,CAACsB,MAAM,GAAG,CAAC,EAAE;UACjCC,oBAAoB,CAACvB,kBAAkB,CAACE,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACK,EAAE,CAAC,CAAC;QAC7D;MACF,CAAC,CAAC,OAAOgB,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,kBAAA;QACZC,OAAO,CAAClD,KAAK,CAAC,sBAAsB,EAAE+C,GAAG,CAAC;QAE1C,IAAII,YAAY,GAAG,8DAA8D;QACjF,IAAIJ,GAAG,aAAHA,GAAG,gBAAAC,aAAA,GAAHD,GAAG,CAAEK,QAAQ,cAAAJ,aAAA,gBAAAC,kBAAA,GAAbD,aAAA,CAAeK,IAAI,cAAAJ,kBAAA,eAAnBA,kBAAA,CAAqBK,MAAM,EAAE;UAAA,IAAAC,cAAA,EAAAC,mBAAA;UAC/BL,YAAY,GAAGJ,GAAG,aAAHA,GAAG,wBAAAQ,cAAA,GAAHR,GAAG,CAAEK,QAAQ,cAAAG,cAAA,wBAAAC,mBAAA,GAAbD,cAAA,CAAeF,IAAI,cAAAG,mBAAA,uBAAnBA,mBAAA,CAAqBF,MAAM;QAC5C;QAEArD,QAAQ,CAACkD,YAAY,CAAC;MACxB,CAAC,SAAS;QACRM,UAAU,CAAC,MAAM;UACf1D,UAAU,CAAC,KAAK,CAAC;QAEnB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC;IAEDqB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM0B,oBAAoB,GAAG,MAAOY,MAAM,IAAK;IAC7C,IAAI;MACFrD,kBAAkB,CAAC,IAAI,CAAC;MACxBE,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,MAAM6C,QAAQ,GAAG,MAAM7E,UAAU,CAACoF,IAAI,CAACnF,QAAQ,CAACoF,QAAQ,CAACC,OAAO,EAAEH,MAAM,CAAC;;MAEzE;MACA,IAAIN,QAAQ,IAAIA,QAAQ,CAACU,OAAO,EAAE;QAChC;QACA,MAAMC,UAAU,GAAG,CAAC,CAAC;QACrBX,QAAQ,CAACU,OAAO,CAACE,OAAO,CAACC,MAAM,IAAI;UACjC,IAAIA,MAAM,CAACC,MAAM,EAAE;YACjB;YACA,IAAIC,eAAe,GAAG,EAAE;YACxB,IAAIC,WAAW,GAAG,KAAK;YAEvB,IAAIH,MAAM,CAACnC,QAAQ,IAAImC,MAAM,CAACnC,QAAQ,CAACuC,kBAAkB,IACrDC,KAAK,CAACC,OAAO,CAACN,MAAM,CAACnC,QAAQ,CAACuC,kBAAkB,CAAC,IACjDJ,MAAM,CAACnC,QAAQ,CAACuC,kBAAkB,CAACxB,MAAM,GAAG,CAAC,EAAE;cACjD;cACAsB,eAAe,GAAGF,MAAM,CAACnC,QAAQ,CAACuC,kBAAkB;cACpDD,WAAW,GAAG,IAAI;cAClBlB,OAAO,CAACsB,GAAG,CAAC,mCAAmC,EAAEP,MAAM,CAACC,MAAM,EAAE,mBAAmB,CAAC;YACtF,CAAC,MAAM,IAAID,MAAM,CAACQ,WAAW,EAAE;cAC7B;cACAN,eAAe,GAAGF,MAAM,CAACQ,WAAW;cACpCL,WAAW,GAAG,KAAK;cACnBlB,OAAO,CAACsB,GAAG,CAAC,qCAAqC,EAAEP,MAAM,CAACC,MAAM,EAAE,oBAAoB,CAAC;YACzF;;YAEA;YACA,IAAI,CAACI,KAAK,CAACC,OAAO,CAACJ,eAAe,CAAC,EAAE;cACnCA,eAAe,GAAG,EAAE;YACtB;;YAEA;YACAjB,OAAO,CAACsB,GAAG,CAAC,OAAOP,MAAM,CAACC,MAAM,2BAA2B,CAAC;;YAE5D;YACAH,UAAU,CAACE,MAAM,CAACC,MAAM,CAAC,GAAG;cAC1B,GAAGD,MAAM;cACT;cACAQ,WAAW,EAAEN,eAAe;cAC5B;cACAC,WAAW,EAAEA;YACf,CAAC;UACH;QACF,CAAC,CAAC;;QAEF;QACAvE,aAAa,CAAC6E,WAAW,IACvBA,WAAW,CAACjD,GAAG,CAACC,GAAG,KAAK;UACtB,GAAGA,GAAG;UACNiD,QAAQ,EAAEZ,UAAU,CAACrC,GAAG,CAACK,EAAE,CAAC,IAAI;YAAE0C,WAAW,EAAE,EAAE;YAAEL,WAAW,EAAE;UAAM;QACxE,CAAC,CAAC,CACJ,CAAC;QAEDlB,OAAO,CAACsB,GAAG,CAAC,8DAA8D,CAAC;MAC7E;IACF,CAAC,CAAC,OAAOzB,GAAG,EAAE;MAAA,IAAA6B,cAAA,EAAAC,mBAAA;MACZ3B,OAAO,CAAClD,KAAK,CAAC,kCAAkC,EAAE+C,GAAG,CAAC;MAEtD,IAAII,YAAY,GAAG,0DAA0D;MAC7E,IAAIJ,GAAG,aAAHA,GAAG,gBAAA6B,cAAA,GAAH7B,GAAG,CAAEK,QAAQ,cAAAwB,cAAA,gBAAAC,mBAAA,GAAbD,cAAA,CAAevB,IAAI,cAAAwB,mBAAA,eAAnBA,mBAAA,CAAqBvB,MAAM,EAAE;QAAA,IAAAwB,cAAA,EAAAC,mBAAA;QAC/B5B,YAAY,GAAGJ,GAAG,aAAHA,GAAG,wBAAA+B,cAAA,GAAH/B,GAAG,CAAEK,QAAQ,cAAA0B,cAAA,wBAAAC,mBAAA,GAAbD,cAAA,CAAezB,IAAI,cAAA0B,mBAAA,uBAAnBA,mBAAA,CAAqBzB,MAAM;MAC5C;MAEA/C,gBAAgB,CAAC4C,YAAY,CAAC;IAChC,CAAC,SAAS;MACR9C,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAM2E,wBAAwB,GAAG,MAAOC,KAAK,IAAK;IAChD,IAAI;MACF/B,OAAO,CAACsB,GAAG,CAAC,0CAA0C,EAAES,KAAK,CAAC;;MAE9D;MACA,MAAM7B,QAAQ,GAAG,MAAM7E,UAAU,CAACoF,IAAI,CAACnF,QAAQ,CAACoF,QAAQ,CAACC,OAAO,EAAE,CAACoB,KAAK,CAAC,CAAC;MAE1E,IAAI7B,QAAQ,IAAIA,QAAQ,CAACU,OAAO,IAAIV,QAAQ,CAACU,OAAO,CAACjB,MAAM,GAAG,CAAC,EAAE;QAC/D,MAAMoB,MAAM,GAAGb,QAAQ,CAACU,OAAO,CAAC,CAAC,CAAC;QAElC,IAAIG,MAAM,CAACC,MAAM,KAAKe,KAAK,EAAE;UAC3B;UACA,IAAId,eAAe,GAAG,EAAE;UACxB,IAAIC,WAAW,GAAG,KAAK;UAEvB,IAAIH,MAAM,CAACnC,QAAQ,IAAImC,MAAM,CAACnC,QAAQ,CAACuC,kBAAkB,IACrDC,KAAK,CAACC,OAAO,CAACN,MAAM,CAACnC,QAAQ,CAACuC,kBAAkB,CAAC,IACjDJ,MAAM,CAACnC,QAAQ,CAACuC,kBAAkB,CAACxB,MAAM,GAAG,CAAC,EAAE;YACjDsB,eAAe,GAAGF,MAAM,CAACnC,QAAQ,CAACuC,kBAAkB;YACpDD,WAAW,GAAG,IAAI;UACpB,CAAC,MAAM,IAAIH,MAAM,CAACQ,WAAW,EAAE;YAC7BN,eAAe,GAAGF,MAAM,CAACQ,WAAW;YACpCL,WAAW,GAAG,KAAK;UACrB;UAEA,IAAI,CAACE,KAAK,CAACC,OAAO,CAACJ,eAAe,CAAC,EAAE;YACnCA,eAAe,GAAG,EAAE;UACtB;;UAEA;UACA,MAAM,IAAIe,OAAO,CAACC,OAAO,IAAI1B,UAAU,CAAC0B,OAAO,EAAE,IAAI,CAAC,CAAC;;UAEvD;UACAjC,OAAO,CAACsB,GAAG,CAAC,mCAAmC,EAAES,KAAK,CAAC;UACvD,MAAM5D,IAAI,GAAG,MAAM/C,UAAU,CAACgD,UAAU,CAAC,CAAC;UAC1C,MAAM8D,UAAU,GAAG/D,IAAI,CAACG,SAAS,CAAC6D,IAAI,CAAC3D,GAAG,IAAIA,GAAG,CAACK,EAAE,KAAKkD,KAAK,CAAC;UAE/D/B,OAAO,CAACsB,GAAG,CAAC,mBAAmB,EAAEY,UAAU,CAAC;UAC5ClC,OAAO,CAACsB,GAAG,CAAC,sBAAsB,EAAEY,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE9C,UAAU,EAAE,OAAO,EAAE8C,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE5C,kBAAkB,EAAE,SAAS,EAAE4C,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE1C,oBAAoB,EAAE,MAAM,EAAE0C,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAExC,iBAAiB,CAAC;;UAExL;UACA/C,aAAa,CAAC6E,WAAW,IAAI;YAC3B,MAAMY,cAAc,GAAGZ,WAAW,CAACjD,GAAG,CAACC,GAAG,IACxCA,GAAG,CAACK,EAAE,KAAKkD,KAAK,GACZ;cACE,GAAGvD,GAAG;cACN;cACAW,SAAS,EAAE,CAAA+C,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE9C,UAAU,KAAIZ,GAAG,CAACW,SAAS;cAClDE,gBAAgB,EAAE,CAAA6C,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE5C,kBAAkB,KAAId,GAAG,CAACa,gBAAgB;cACxEE,kBAAkB,EAAE,CAAA2C,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE1C,oBAAoB,KAAIhB,GAAG,CAACe,kBAAkB;cAC9EE,eAAe,EAAE,CAAAyC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAExC,iBAAiB,KAAIlB,GAAG,CAACiB,eAAe;cACrE;cACAgC,QAAQ,EAAE;gBACR,GAAGV,MAAM;gBACTQ,WAAW,EAAEN,eAAe;gBAC5BC,WAAW,EAAEA;cACf;YACF,CAAC,GACD1C,GACN,CAAC;YAEDwB,OAAO,CAACsB,GAAG,CAAC,sBAAsB,EAAEc,cAAc,CAAC;YACnD,OAAOA,cAAc;UACvB,CAAC,CAAC;;UAEF;UACAnF,kBAAkB,CAACuE,WAAW,IAAI;YAChC,IAAIA,WAAW,IAAIA,WAAW,CAACR,MAAM,KAAKe,KAAK,EAAE;cAC/C,OAAO;gBACL,GAAGhB,MAAM;gBACTQ,WAAW,EAAEN,eAAe;gBAC5BC,WAAW,EAAEA;cACf,CAAC;YACH;YACA,OAAOM,WAAW;UACpB,CAAC,CAAC;UAEFxB,OAAO,CAACsB,GAAG,CAAC,sEAAsE,EAAES,KAAK,CAAC;QAC5F;MACF;IACF,CAAC,CAAC,OAAOlC,GAAG,EAAE;MAAA,IAAAwC,cAAA,EAAAC,mBAAA;MACZtC,OAAO,CAAClD,KAAK,CAAC,wCAAwC,EAAE+C,GAAG,CAAC;MAE5D,IAAII,YAAY,GAAG,iCAAiC;MACpD,IAAIJ,GAAG,aAAHA,GAAG,gBAAAwC,cAAA,GAAHxC,GAAG,CAAEK,QAAQ,cAAAmC,cAAA,gBAAAC,mBAAA,GAAbD,cAAA,CAAelC,IAAI,cAAAmC,mBAAA,eAAnBA,mBAAA,CAAqBlC,MAAM,EAAE;QAAA,IAAAmC,cAAA,EAAAC,mBAAA;QAC/BvC,YAAY,GAAGJ,GAAG,aAAHA,GAAG,wBAAA0C,cAAA,GAAH1C,GAAG,CAAEK,QAAQ,cAAAqC,cAAA,wBAAAC,mBAAA,GAAbD,cAAA,CAAepC,IAAI,cAAAqC,mBAAA,uBAAnBA,mBAAA,CAAqBpC,MAAM;MAC5C;;MAEA;IACF;EACF,CAAC;;EAED;EACA,MAAMqC,cAAc,GAAG,MAAAA,CAAOC,MAAM,GAAG,KAAK,KAAK;IAC/C,IAAI;MACF1C,OAAO,CAACsB,GAAG,CAAC,iCAAiC,CAAC;MAC9C,IAAI,CAACoB,MAAM,EAAE;QACX7F,UAAU,CAAC,IAAI,CAAC;MAClB;;MAEA;MACA,MAAMsB,IAAI,GAAG,MAAM/C,UAAU,CAACgD,UAAU,CAAC,CAAC;MAC1C4B,OAAO,CAACsB,GAAG,CAAC,eAAe,EAAEnD,IAAI,CAAC;;MAElC;MACA,MAAME,kBAAkB,GAAGF,IAAI,CAACG,SAAS,CAACC,GAAG,CAACC,GAAG,IAAI;QAAA,IAAAmE,cAAA,EAAAC,cAAA;QACnD,MAAMjE,UAAU,GAAG,EAAAgE,cAAA,GAAAnE,GAAG,CAACI,QAAQ,cAAA+D,cAAA,uBAAZA,cAAA,CAAchE,UAAU,KAAI,YAAY;QAE3D,OAAO;UACLE,EAAE,EAAEL,GAAG,CAACK,EAAE;UACVC,KAAK,EAAEN,GAAG,CAACM,KAAK,IAAI,aAAa;UACjCH,UAAU,EAAEA,UAAU;UACtBI,IAAI,EAAE,EAAA6D,cAAA,GAAApE,GAAG,CAACI,QAAQ,cAAAgE,cAAA,uBAAZA,cAAA,CAAc7D,IAAI,KAAIP,GAAG,CAACQ,UAAU,IAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACtE;UACAC,SAAS,EAAEX,GAAG,CAACY,UAAU;UACzBC,gBAAgB,EAAEb,GAAG,CAACc,kBAAkB;UACxCC,kBAAkB,EAAEf,GAAG,CAACgB,oBAAoB;UAC5CC,eAAe,EAAEjB,GAAG,CAACkB;QACvB,CAAC;MACH,CAAC,CAAC;MAEF/C,aAAa,CAAC0B,kBAAkB,CAAC;MACjC2B,OAAO,CAACsB,GAAG,CAAC,sBAAsB,EAAEjD,kBAAkB,CAAC;;MAEvD;MACA,IAAIA,kBAAkB,CAACsB,MAAM,GAAG,CAAC,EAAE;QACjC,MAAMa,MAAM,GAAGnC,kBAAkB,CAACE,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACK,EAAE,CAAC;QACpDmB,OAAO,CAACsB,GAAG,CAAC,qCAAqC,EAAEd,MAAM,CAAC;QAC1D,MAAMZ,oBAAoB,CAACY,MAAM,CAAC;MACpC;MAEAzD,QAAQ,CAAC,IAAI,CAAC;MACdiD,OAAO,CAACsB,GAAG,CAAC,oCAAoC,CAAC;IACnD,CAAC,CAAC,OAAOzB,GAAG,EAAE;MAAA,IAAAgD,cAAA,EAAAC,mBAAA;MACZ9C,OAAO,CAAClD,KAAK,CAAC,qCAAqC,EAAE+C,GAAG,CAAC;MAEzD,IAAII,YAAY,GAAG,8DAA8D;MACjF,IAAIJ,GAAG,aAAHA,GAAG,gBAAAgD,cAAA,GAAHhD,GAAG,CAAEK,QAAQ,cAAA2C,cAAA,gBAAAC,mBAAA,GAAbD,cAAA,CAAe1C,IAAI,cAAA2C,mBAAA,eAAnBA,mBAAA,CAAqB1C,MAAM,EAAE;QAAA,IAAA2C,cAAA,EAAAC,mBAAA;QAC/B/C,YAAY,GAAGJ,GAAG,aAAHA,GAAG,wBAAAkD,cAAA,GAAHlD,GAAG,CAAEK,QAAQ,cAAA6C,cAAA,wBAAAC,mBAAA,GAAbD,cAAA,CAAe5C,IAAI,cAAA6C,mBAAA,uBAAnBA,mBAAA,CAAqB5C,MAAM;MAC5C;MAEArD,QAAQ,CAACkD,YAAY,CAAC;IACxB,CAAC,SAAS;MACR,IAAI,CAACyC,MAAM,EAAE;QACX7F,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;;EAED;EACA,MAAMoG,mBAAmB,GAAGrI,KAAK,CAACsI,OAAO,CAAC,MAAM;IAC9C;IACA,MAAMC,cAAc,GAAIC,IAAI,IAAK;MAC/B,MAAMC,KAAK,GAAG,EAAE;;MAEhB;MACA,IAAID,IAAI,CAAC3B,QAAQ,IAAI2B,IAAI,CAAC3B,QAAQ,CAAC6B,WAAW,EAAE;QAC9CD,KAAK,CAACE,IAAI,CAAC,IAAItE,IAAI,CAACmE,IAAI,CAAC3B,QAAQ,CAAC6B,WAAW,CAAC,CAAC;MACjD;MACA,IAAIF,IAAI,CAAC3B,QAAQ,IAAI2B,IAAI,CAAC3B,QAAQ,CAAC+B,UAAU,EAAE;QAC7CH,KAAK,CAACE,IAAI,CAAC,IAAItE,IAAI,CAACmE,IAAI,CAAC3B,QAAQ,CAAC+B,UAAU,CAAC,CAAC;MAChD;;MAEA;MACA,IAAIJ,IAAI,CAACrE,IAAI,EAAE;QACbsE,KAAK,CAACE,IAAI,CAAC,IAAItE,IAAI,CAACmE,IAAI,CAACrE,IAAI,CAAC,CAAC;MACjC;;MAEA;MACA,OAAOsE,KAAK,CAAC1D,MAAM,GAAG,CAAC,GAAG,IAAIV,IAAI,CAACwE,IAAI,CAACC,GAAG,CAAC,GAAGL,KAAK,CAAC,CAAC,GAAG,IAAIpE,IAAI,CAAC,CAAC,CAAC;IACtE,CAAC;;IAED;IACA,IAAI0E,QAAQ,GAAGjH,UAAU;IAEzB,IAAIF,kBAAkB,KAAK,KAAK,EAAE;MAChCmH,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACR,IAAI,IAAIA,IAAI,CAACzE,UAAU,KAAKnC,kBAAkB,CAAC;IAC5E;;IAEA;IACA,IAAIc,UAAU,CAACuG,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC5B,MAAMC,IAAI,GAAGxG,UAAU,CAACyG,WAAW,CAAC,CAAC;MACrCJ,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACR,IAAI,IAC7B,CAACA,IAAI,CAACtE,KAAK,IAAI,EAAE,EAAEiF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,IAAI,CAAC,IAC/C,CAACV,IAAI,CAACzE,UAAU,IAAI,EAAE,EAAEoF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,IAAI,CACrD,CAAC;IACH;;IAEA;IACA,IAAI9F,UAAU,KAAK,KAAK,EAAE;MACxB,MAAMiG,GAAG,GAAG,IAAIhF,IAAI,CAAC,CAAC;MACtB,MAAMiF,KAAK,GAAG,IAAIjF,IAAI,CAACgF,GAAG,CAACE,WAAW,CAAC,CAAC,EAAEF,GAAG,CAACG,QAAQ,CAAC,CAAC,EAAEH,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC;MACxE,MAAMC,SAAS,GAAG,IAAIrF,IAAI,CAACiF,KAAK,CAAC;MACjCI,SAAS,CAACC,OAAO,CAACD,SAAS,CAACD,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;MAC1C,MAAMG,QAAQ,GAAG,IAAIvF,IAAI,CAACiF,KAAK,CAAC;MAChCM,QAAQ,CAACD,OAAO,CAACC,QAAQ,CAACH,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;MACxC,MAAMI,SAAS,GAAG,IAAIxF,IAAI,CAACiF,KAAK,CAAC;MACjCO,SAAS,CAACC,QAAQ,CAACD,SAAS,CAACL,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MAE5CT,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACR,IAAI,IAAI;QACjC;QACA,MAAMuB,SAAS,GAAGxB,cAAc,CAACC,IAAI,CAAC;QAEtC,QAAQpF,UAAU;UAChB,KAAK,OAAO;YACV,OAAO2G,SAAS,IAAIT,KAAK;UAC3B,KAAK,WAAW;YACd,OAAOS,SAAS,IAAIL,SAAS,IAAIK,SAAS,GAAGT,KAAK;UACpD,KAAK,MAAM;YACT,OAAOS,SAAS,IAAIH,QAAQ;UAC9B,KAAK,OAAO;YACV,OAAOG,SAAS,IAAIF,SAAS;UAC/B;YACE,OAAO,IAAI;QACf;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,OAAOd,QAAQ,CAACiB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC7B,IAAIlH,SAAS,KAAK,MAAM,EAAE;QACxB,MAAMmH,KAAK,GAAG5B,cAAc,CAAC0B,CAAC,CAAC;QAC/B,MAAMG,KAAK,GAAG7B,cAAc,CAAC2B,CAAC,CAAC;QAE/B,OAAOhH,aAAa,KAAK,KAAK,GAAGiH,KAAK,GAAGC,KAAK,GAAGA,KAAK,GAAGD,KAAK;MAChE,CAAC,MAAM,IAAInH,SAAS,KAAK,OAAO,EAAE;QAChC,MAAMqH,MAAM,GAAG,CAACJ,CAAC,CAAC/F,KAAK,IAAI,EAAE,EAAEiF,WAAW,CAAC,CAAC;QAC5C,MAAMmB,MAAM,GAAG,CAACJ,CAAC,CAAChG,KAAK,IAAI,EAAE,EAAEiF,WAAW,CAAC,CAAC;QAE5C,OAAOjG,aAAa,KAAK,KAAK,GAC1BmH,MAAM,CAACE,aAAa,CAACD,MAAM,CAAC,GAC5BA,MAAM,CAACC,aAAa,CAACF,MAAM,CAAC;MAClC,CAAC,MAAM,IAAIrH,SAAS,KAAK,UAAU,EAAE;QACnC;QACA,MAAMwH,YAAY,GAAGP,CAAC,CAACpD,QAAQ,IAAIoD,CAAC,CAACpD,QAAQ,CAAC6B,WAAW;QACzD,MAAM+B,YAAY,GAAGP,CAAC,CAACrD,QAAQ,IAAIqD,CAAC,CAACrD,QAAQ,CAAC6B,WAAW;;QAEzD;QACA,IAAI8B,YAAY,IAAI,CAACC,YAAY,EAAE;UACjC,OAAO,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,MAAM,IAAI,CAACD,YAAY,IAAIC,YAAY,EAAE;UACxC,OAAO,CAAC,CAAC,CAAC;QACZ;;QAEA;QACA,MAAMC,gBAAgB,GAAIlC,IAAI,IAAK;UACjC,IAAI,CAACA,IAAI,CAAC/D,gBAAgB,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,MAAM;UACnD,IAAI,CAAC+D,IAAI,CAAC7D,kBAAkB,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,QAAQ;UACvD,IAAI,CAAC6D,IAAI,CAAC3D,eAAe,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,KAAK;UACjD,OAAO,MAAM,CAAC,CAAC;QACjB,CAAC;QAED,MAAM8F,SAAS,GAAGD,gBAAgB,CAACT,CAAC,CAAC;QACrC,MAAMW,SAAS,GAAGF,gBAAgB,CAACR,CAAC,CAAC;;QAErC;QACA,MAAMW,aAAa,GAAG;UAAE,MAAM,EAAE,CAAC;UAAE,QAAQ,EAAE,CAAC;UAAE,KAAK,EAAE,CAAC;UAAE,MAAM,EAAE;QAAE,CAAC;QACrE,MAAMC,MAAM,GAAGD,aAAa,CAACF,SAAS,CAAC;QACvC,MAAMI,MAAM,GAAGF,aAAa,CAACD,SAAS,CAAC;;QAEvC;QACA,OAAO1H,aAAa,KAAK,KAAK,GAAG4H,MAAM,GAAGC,MAAM,GAAGA,MAAM,GAAGD,MAAM;MACpE;MACA,OAAO,CAAC;IACV,CAAC,CAAC;EACJ,CAAC,EAAE,CAAChJ,UAAU,EAAEF,kBAAkB,EAAEc,UAAU,EAAEM,SAAS,EAAEE,aAAa,EAAEE,UAAU,CAAC,CAAC;EAEtF,MAAM4H,sBAAsB,GAAIjH,UAAU,IAAK;IAC7ClC,qBAAqB,CAACkC,UAAU,CAAC;IACjCpC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3BF,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;EAC3B,CAAC;EAED,MAAMwJ,iBAAiB,GAAIC,KAAK,IAAK;IACnCvJ,mBAAmB,CAACuJ,KAAK,CAAC;IAE1B,MAAMC,WAAW,GAAG9C,mBAAmB,CAAC6C,KAAK,CAAC;;IAE9C;IACA,IAAIC,WAAW,IAAIA,WAAW,CAACtE,QAAQ,EAAE;MACvC;MACAxE,kBAAkB,CAAC8I,WAAW,CAACtE,QAAQ,CAAC;MACxCpF,gBAAgB,CAAC,IAAI,CAAC;IACxB,CAAC,MAAM;MACL;MACAc,kBAAkB,CAAC,IAAI,CAAC;MACxBE,gBAAgB,CAAC,IAAI,CAAC;MAEtBhC,UAAU,CAACoF,IAAI,CAACnF,QAAQ,CAACoF,QAAQ,CAACsF,OAAO,EAAE;QAAEhF,MAAM,EAAE+E,WAAW,CAAClH;MAAG,CAAC,CAAC,CACnEoH,IAAI,CAAC/F,QAAQ,IAAI;QAChBjD,kBAAkB,CAACiD,QAAQ,CAAC;;QAE5B;QACAvD,aAAa,CAAC6E,WAAW,IACvBA,WAAW,CAACjD,GAAG,CAACC,GAAG,IACjBA,GAAG,CAACK,EAAE,KAAKkH,WAAW,CAAClH,EAAE,GACrB;UAAE,GAAGL,GAAG;UAAEiD,QAAQ,EAAEvB;QAAS,CAAC,GAC9B1B,GACN,CACF,CAAC;MACH,CAAC,CAAC,CACD0H,KAAK,CAACrG,GAAG,IAAI;QACZG,OAAO,CAAClD,KAAK,CAAC,sBAAsB,EAAE+C,GAAG,CAAC;QAC1CxC,gBAAgB,CAAC,gDAAgD,CAAC;QAClE5B,KAAK,CAACqB,KAAK,CAAC,gDAAgD,EAAE;UAC5DqJ,OAAO,EAAE,oBAAoBlH,IAAI,CAACgF,GAAG,CAAC,CAAC;QACzC,CAAC,CAAC;MACJ,CAAC,CAAC,CACDmC,OAAO,CAAC,MAAM;QACbjJ,kBAAkB,CAAC,KAAK,CAAC;QACzBd,gBAAgB,CAAC,IAAI,CAAC;MACxB,CAAC,CAAC;IACN;EACF,CAAC;EAED,MAAMgK,4BAA4B,GAAGA,CAAA,KAAM;IACzC,oBACExK,OAAA;MACEyK,SAAS,EAAC,uBAAuB;MACjCC,OAAO,EAAEA,CAAA,KAAMpK,cAAc,CAAC,IAAI,CAAE;MAAAqK,QAAA,EACrC;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAEb,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIC,KAAK,IAAK;IAClC,IAAIlJ,SAAS,KAAKkJ,KAAK,EAAE;MACvB;MACA/I,gBAAgB,CAACD,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;IAC5D,CAAC,MAAM;MACL;MACAD,YAAY,CAACiJ,KAAK,CAAC;MACnB/I,gBAAgB,CAAC+I,KAAK,KAAK,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBxJ,aAAa,CAAC,EAAE,CAAC;IACjBU,aAAa,CAAC,KAAK,CAAC;IACpBJ,YAAY,CAAC,MAAM,CAAC;IACpBE,gBAAgB,CAAC,MAAM,CAAC;IACxB;EACF,CAAC;;EAGD;EACA,MAAMiJ,kBAAkB,GAAGA,CAAA,KAAM;IAC/BhH,OAAO,CAACsB,GAAG,CAAC,6BAA6B,CAAC;IAC1C7D,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMwJ,iBAAiB,GAAGA,CAAA,KAAM;IAC9BjH,OAAO,CAACsB,GAAG,CAAC,8BAA8B,CAAC;IAC3C7D,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;;EAED;EACA,MAAMyJ,qBAAqB,GAAInF,KAAK,IAAK;IACvC/B,OAAO,CAACsB,GAAG,CAAC,yCAAyC,EAAES,KAAK,CAAC;IAC7DpE,kBAAkB,CAACoE,KAAK,CAAC;IACzB5F,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;EACzB,CAAC;EAED,MAAMgL,wBAAwB,GAAGA,CAAA,KAAM;IACrCnH,OAAO,CAACsB,GAAG,CAAC,gCAAgC,CAAC;IAC7C3D,kBAAkB,CAAC,IAAI,CAAC;IACxB8E,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;IACtBwE,iBAAiB,CAAC,CAAC;EACrB,CAAC;EAED,oBACEpL,OAAA;IAAKyK,SAAS,EAAC,cAAc;IAAAE,QAAA,gBAC3B3K,OAAA,CAACd,UAAU;MAAA0L,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACd/K,OAAA;MAAKyK,SAAS,EAAC,aAAa;MAAAE,QAAA,gBAC1B3K,OAAA;QAAKyK,SAAS,EAAC,aAAa;QAAAE,QAAA,eAC1B3K,OAAA;UAAA2K,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eAEN/K,OAAA,CAACb,iBAAiB;QAChBoM,kBAAkB,EAAExB,sBAAuB;QAC3CyB,eAAe,EAAC;MAAK;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eAGA/K,OAAA,CAAAE,SAAA;QAAAyK,QAAA,gBACE3K,OAAA;UAAKyK,SAAS,EAAC,gBAAgB;UAAAE,QAAA,gBAC7B3K,OAAA;YACEyL,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,wBAAwB;YACpCjB,SAAS,EAAC,cAAc;YACxBkB,KAAK,EAAElK,UAAW;YAClBmK,QAAQ,EAAGC,CAAC,IAAKnK,aAAa,CAACmK,CAAC,CAACC,MAAM,CAACH,KAAK;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,EACDP,4BAA4B,CAAC,CAAC;QAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eAEN/K,OAAA;UAAKyK,SAAS,EAAC,sBAAsB;UAAAE,QAAA,gBACnC3K,OAAA;YAAKyK,SAAS,EAAC,cAAc;YAAAE,QAAA,gBAC3B3K,OAAA;cAAA2K,QAAA,EAAO;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvB/K,OAAA;cAAKyK,SAAS,EAAC,cAAc;cAAAE,QAAA,gBAC3B3K,OAAA;gBACEyK,SAAS,EAAE,YAAY1I,SAAS,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;gBAC9D2I,OAAO,EAAEA,CAAA,KAAMM,gBAAgB,CAAC,MAAM,CAAE;gBAAAL,QAAA,GACzC,MAEC,EAAC5I,SAAS,KAAK,MAAM,iBACnB/B,OAAA;kBAAMyK,SAAS,EAAC,gBAAgB;kBAAAE,QAAA,EAC7B1I,aAAa,KAAK,KAAK,GAAG,GAAG,GAAG;gBAAG;kBAAA2I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eACT/K,OAAA;gBACEyK,SAAS,EAAE,YAAY1I,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAE,EAAG;gBAC/D2I,OAAO,EAAEA,CAAA,KAAMM,gBAAgB,CAAC,OAAO,CAAE;gBAAAL,QAAA,GAC1C,OAEC,EAAC5I,SAAS,KAAK,OAAO,iBACpB/B,OAAA;kBAAMyK,SAAS,EAAC,gBAAgB;kBAAAE,QAAA,EAC7B1I,aAAa,KAAK,KAAK,GAAG,GAAG,GAAG;gBAAG;kBAAA2I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eACT/K,OAAA;gBACEyK,SAAS,EAAE,YAAY1I,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;gBAClE2I,OAAO,EAAEA,CAAA,KAAMM,gBAAgB,CAAC,UAAU,CAAE;gBAAAL,QAAA,GAC7C,UAEC,EAAC5I,SAAS,KAAK,UAAU,iBACvB/B,OAAA;kBAAMyK,SAAS,EAAC,gBAAgB;kBAAAE,QAAA,EAC7B1I,aAAa,KAAK,KAAK,GAAG,GAAG,GAAG;gBAAG;kBAAA2I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/K,OAAA;YAAKyK,SAAS,EAAC,cAAc;YAAAE,QAAA,gBAC3B3K,OAAA;cAAA2K,QAAA,EAAO;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpB/K,OAAA;cACE2L,KAAK,EAAExJ,UAAW;cAClByJ,QAAQ,EAAGC,CAAC,IAAKzJ,aAAa,CAACyJ,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/ClB,SAAS,EAAC,eAAe;cAAAE,QAAA,gBAEzB3K,OAAA;gBAAQ2L,KAAK,EAAC,KAAK;gBAAAhB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrC/K,OAAA;gBAAQ2L,KAAK,EAAC,OAAO;gBAAAhB,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC/K,OAAA;gBAAQ2L,KAAK,EAAC,WAAW;gBAAAhB,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C/K,OAAA;gBAAQ2L,KAAK,EAAC,MAAM;gBAAAhB,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACzC/K,OAAA;gBAAQ2L,KAAK,EAAC,OAAO;gBAAAhB,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN/K,OAAA;YACEyK,SAAS,EAAC,mBAAmB;YAC7BC,OAAO,EAAEQ,YAAa;YACtBa,QAAQ,EAAE5J,UAAU,KAAK,KAAK,IAAIJ,SAAS,KAAK,MAAM,IAAIE,aAAa,KAAK,MAAM,IAAIR,UAAU,KAAK,EAAG;YAAAkJ,QAAA,EACzG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA,eACN,CAAC,EAGJhK,OAAO,gBACNf,OAAA;QAAKyK,SAAS,EAAC,mBAAmB;QAAAE,QAAA,gBAChC3K,OAAA,CAACN,cAAc;UAACsM,IAAI,EAAC;QAAO;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/B/K,OAAA;UAAMyK,SAAS,EAAC,cAAc;UAAAE,QAAA,EAAC;QAA+B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,gBAEN/K,OAAA,CAAAE,SAAA;QAAAyK,QAAA,eACE3K,OAAA;UAAKyK,SAAS,EAAC,aAAa;UAAAE,QAAA,gBAC1B3K,OAAA;YAAA2K,QAAA,EAAI;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC5B9J,KAAK,gBACJjB,OAAA;YAAKyK,SAAS,EAAC,eAAe;YAAAE,QAAA,EAAE1J;UAAK;YAAA2J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,GAC1C3D,mBAAmB,CAACtD,MAAM,KAAK,CAAC,gBAClC9D,OAAA;YAAKyK,SAAS,EAAC,aAAa;YAAAE,QAAA,EACzBlJ,UAAU,IAAIU,UAAU,KAAK,KAAK,GACjC,wEAAwE,gBACxEnC,OAAA,CAAAE,SAAA;cAAAyK,QAAA,GAAE,0EACwE,EAACH,4BAA4B,CAAC,CAAC;YAAA,eACvG;UAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEF,CAAC,gBAEN/K,OAAA;YAAKyK,SAAS,EAAC,WAAW;YAAAE,QAAA,EACvBvD,mBAAmB,CAAC1E,GAAG,CAAC,CAAC6E,IAAI,EAAE0C,KAAK,kBACnCjK,OAAA;cAEEyK,SAAS,EAAE,YAAYhK,gBAAgB,KAAKwJ,KAAK,GAAG,UAAU,GAAG,EAAE,EAAG;cAAAU,QAAA,gBAEtE3K,OAAA;gBAAKyK,SAAS,EAAC,UAAU;gBAAAE,QAAA,gBACvB3K,OAAA;kBAAMyK,SAAS,EAAE,iBAAiB,CAAClD,IAAI,CAAC3B,QAAQ,IAAI,CAAC2B,IAAI,CAAC3B,QAAQ,CAAC6B,WAAW,GAAG,aAAa,GAAG,EAAE,IAAIF,IAAI,CAAC3B,QAAQ,IAAI2B,IAAI,CAAC3B,QAAQ,CAAC6B,WAAW,IAAIF,IAAI,CAAC3B,QAAQ,CAACP,WAAW,GAAG,SAAS,GAAGkC,IAAI,CAAC3B,QAAQ,IAAI2B,IAAI,CAAC3B,QAAQ,CAAC6B,WAAW,GAAG,UAAU,GAAG,EAAE,EAAG;kBAAAkD,QAAA,EAEzPpD,IAAI,CAAC3B,QAAQ,IAAI2B,IAAI,CAAC3B,QAAQ,CAAC6B,WAAW,GACtCF,IAAI,CAAC3B,QAAQ,CAACP,WAAW,GACtB,cAAcvF,UAAU,CAACyH,IAAI,CAAC3B,QAAQ,CAAC+B,UAAU,IAAIJ,IAAI,CAAC3B,QAAQ,CAAC6B,WAAW,CAAC,EAAE,GACjF,eAAe3H,UAAU,CAACyH,IAAI,CAAC3B,QAAQ,CAAC6B,WAAW,CAAC,EAAE,GAC1D,cAAc3H,UAAU,CAACyH,IAAI,CAACrE,IAAI,CAAC;gBAAE;kBAAA0H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGrC,CAAC,eACP/K,OAAA;kBAAA2K,QAAA,EAAK9K,YAAY,CAAC0H,IAAI,CAACtE,KAAK;gBAAC;kBAAA2H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnC/K,OAAA;kBAAKyK,SAAS,EAAC,aAAa;kBAAAE,QAAA,eAC1B3K,OAAA;oBAAMyK,SAAS,EAAC,YAAY;oBAAAE,QAAA,EAAE9K,YAAY,CAAC0H,IAAI,CAACzE,UAAU;kBAAC;oBAAA8H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLlJ,eAAe,KAAK0F,IAAI,CAACvE,EAAE,iBAC1BhD,OAAA;gBAAKyK,SAAS,EAAC,uBAAuB;gBAAAE,QAAA,eACpC3K,OAAA,CAAAE,SAAA;kBAAAyK,QAAA,GACGpD,IAAI,CAACjE,SAAS,KAAK2I,SAAS,IAAI1E,IAAI,CAACjE,SAAS,KAAK,IAAI,iBACtDtD,OAAA;oBAAMyK,SAAS,EAAC,uBAAuB;oBAAAE,QAAA,gBACrC3K,OAAA;sBAAMyK,SAAS,EAAC,cAAc;sBAAAE,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjD/K,OAAA;sBAAMyK,SAAS,EAAC,cAAc;sBAAAE,QAAA,EAAEpD,IAAI,CAACjE;oBAAS;sBAAAsH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CACP,EAEAxD,IAAI,CAAC/D,gBAAgB,KAAKyI,SAAS,IAAI1E,IAAI,CAAC/D,gBAAgB,KAAK,IAAI,IAAI+D,IAAI,CAAC/D,gBAAgB,GAAG,CAAC,gBACjGxD,OAAA;oBAAMyK,SAAS,EAAC,0BAA0B;oBAAAE,QAAA,gBACxC3K,OAAA;sBAAMyK,SAAS,EAAC,cAAc;sBAAAE,QAAA,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACpD/K,OAAA;sBAAMyK,SAAS,EAAC,cAAc;sBAAAE,QAAA,EAAEpD,IAAI,CAAC/D;oBAAgB;sBAAAoH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,GACLxD,IAAI,CAAC7D,kBAAkB,KAAKuI,SAAS,IAAI1E,IAAI,CAAC7D,kBAAkB,KAAK,IAAI,IAAI6D,IAAI,CAAC7D,kBAAkB,GAAG,CAAC,gBAC1G1D,OAAA;oBAAMyK,SAAS,EAAC,4BAA4B;oBAAAE,QAAA,gBAC1C3K,OAAA;sBAAMyK,SAAS,EAAC,cAAc;sBAAAE,QAAA,EAAC;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtD/K,OAAA;sBAAMyK,SAAS,EAAC,cAAc;sBAAAE,QAAA,EAAEpD,IAAI,CAAC7D;oBAAkB;sBAAAkH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC,GACLxD,IAAI,CAAC3D,eAAe,KAAKqI,SAAS,IAAI1E,IAAI,CAAC3D,eAAe,KAAK,IAAI,IAAI2D,IAAI,CAAC3D,eAAe,GAAG,CAAC,gBACjG5D,OAAA;oBAAMyK,SAAS,EAAC,yBAAyB;oBAAAE,QAAA,gBACvC3K,OAAA;sBAAMyK,SAAS,EAAC,cAAc;sBAAAE,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnD/K,OAAA;sBAAMyK,SAAS,EAAC,cAAc;sBAAAE,QAAA,EAAEpD,IAAI,CAAC3D;oBAAe;sBAAAgH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,GACL,IAAI;gBAAA,eAER;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CACN,eACD/K,OAAA;gBAAKyK,SAAS,EAAC,UAAU;gBAAAE,QAAA,eACvB3K,OAAA;kBAAKyK,SAAS,EAAC,aAAa;kBAAAE,QAAA,EAEzB9I,eAAe,KAAK0F,IAAI,CAACvE,EAAE,gBAC1BhD,OAAA;oBAAKyK,SAAS,EAAC,kBAAkB;oBAAAE,QAAA,gBAC/B3K,OAAA,CAACN,cAAc;sBAACsM,IAAI,EAAC;oBAAO;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC/B/K,OAAA;sBAAMyK,SAAS,EAAC,iBAAiB;sBAAAE,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,GACJxD,IAAI,CAACjE,SAAS,KAAK2I,SAAS,IAAI1E,IAAI,CAACjE,SAAS,KAAK,IAAI,gBACzDtD,OAAA;oBACEyK,SAAS,EAAC,aAAa;oBACvBC,OAAO,EAAEA,CAAA,KAAMV,iBAAiB,CAACC,KAAK,CAAE;oBAAAU,QAAA,EACzC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,gBAET/K,OAAA;oBAAMyK,SAAS,EAAC,YAAY;oBAAAE,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBACjD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GApEDxD,IAAI,CAACvE,EAAE,IAAIiH,KAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqElB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC,gBACN,CACH,eAED/K,OAAA,CAACZ,KAAK;QACJ8M,MAAM,EAAE7L,WAAY;QACpB8L,OAAO,EAAEA,CAAA,KAAM7L,cAAc,CAAC,KAAK,CAAE;QACrC8L,mBAAmB,EAAE,IAAK;QAC1BC,YAAY,EAAE1K,WAAY;QAAAgJ,QAAA,eAE1B3K,OAAA,CAACX,cAAc;UACb8M,OAAO,EAAEA,CAAA,KAAM7L,cAAc,CAAC,KAAK,CAAE;UACrCgM,SAAS,EAAEhB,wBAAyB;UACpCiB,cAAc,EAAEpB,kBAAmB;UACnCqB,iBAAiB,EAAEnB;QAAsB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAEJ/K,OAAA,CAACV,iBAAiB;QAClB8M,mBAAmB,EAAE,IAAK;QAC5BF,MAAM,EAAE3L,aAAc;QACtB4L,OAAO,EAAEA,CAAA,KAAM;UACb3L,gBAAgB,CAAC,KAAK,CAAC;UACvBE,mBAAmB,CAAC,IAAI,CAAC;UACzBU,kBAAkB,CAAC,IAAI,CAAC;QAC1B,CAAE;QACFqL,UAAU,EAAEhM,gBAAgB,KAAK,IAAI,GAAG2G,mBAAmB,CAAC3G,gBAAgB,CAAC,GAAG,IAAK;QACrFU,eAAe,EAAEA,eAAgB;QACjCJ,OAAO,EAAEM,eAAgB;QACzBJ,KAAK,EAAEM,aAAc;QACrBmL,aAAa,EAAE9F,cAAe;QAC9B+F,cAAc,EAAE1G;MAAyB;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eAGF/K,OAAA,CAACL,cAAc;QACbiN,QAAQ,EAAC,WAAW;QACpBC,SAAS,EAAE,IAAK;QAChBC,eAAe,EAAE,KAAM;QACvBC,WAAW;QACXC,YAAY;QACZC,GAAG,EAAE,KAAM;QACXC,gBAAgB;QAChBC,SAAS;QACTC,YAAY;QACZC,KAAK,EAAC;MAAO;QAAAzC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3K,EAAA,CAtxBID,WAAW;AAAAmN,EAAA,GAAXnN,WAAW;AAwxBjB,eAAeA,WAAW;AAAC,IAAAmN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}