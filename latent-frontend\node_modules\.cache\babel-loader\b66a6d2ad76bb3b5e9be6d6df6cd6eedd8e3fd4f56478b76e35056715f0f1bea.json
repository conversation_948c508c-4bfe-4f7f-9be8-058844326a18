{"ast": null, "code": "import apiService from './api';\nimport API_URLS from '../config/apiUrls';\nimport axios from 'axios';\nimport supabase from '../supabase';\nconst sopService = {\n  // Get all SOPs\n  getAllSOPs: async () => {\n    return await apiService.get(API_URLS.SOP.LIST);\n  },\n  // Upload a new SOP\n  uploadSOP: async formData => {\n    try {\n      // Get the token from supabase\n      const {\n        data: {\n          session\n        }\n      } = await supabase.auth.getSession();\n      const token = session === null || session === void 0 ? void 0 : session.access_token;\n\n      // Make a direct axios call for file upload\n      const response = await axios.post(API_URLS.SOP.UPLOAD, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error uploading SOP:', error);\n      let errorMessage = 'Failed to upload SOP.';\n      if (error !== null && error !== void 0 && (_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.detail) {\n        var _error$response2, _error$response2$data;\n        errorMessage = error === null || error === void 0 ? void 0 : (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail;\n      }\n      const errorToThrow = new Error(errorMessage);\n      errorToThrow.response = error.response;\n      throw errorToThrow;\n    }\n  },\n  // Delete a SOP\n  deleteSOP: async sopId => {\n    try {\n      const response = await apiService.delete(`${API_URLS.SOP.DELETE}/${sopId}`);\n      return response.data;\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('Error deleting SOP:', error);\n      let errorMessage = 'Failed to delete SOP.';\n      if (error !== null && error !== void 0 && (_error$response3 = error.response) !== null && _error$response3 !== void 0 && (_error$response3$data = _error$response3.data) !== null && _error$response3$data !== void 0 && _error$response3$data.detail) {\n        var _error$response4, _error$response4$data;\n        errorMessage = error === null || error === void 0 ? void 0 : (_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail;\n      }\n      const errorToThrow = new Error(errorMessage);\n      errorToThrow.response = error.response;\n      throw errorToThrow;\n    }\n  },\n  // Download a SOP\n  downloadSOP: async sopId => {\n    try {\n      // Get the token from localStorage\n      const {\n        data: {\n          session\n        }\n      } = await supabase.auth.getSession();\n      const token = session === null || session === void 0 ? void 0 : session.access_token;\n\n      // Make a direct axios call for file download with responseType blob\n      const response = await axios.get(`${API_URLS.SOP.DOWNLOAD}/${sopId}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        },\n        responseType: 'blob' // Important for file downloads\n      });\n      return response;\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      console.error('Error downloading SOP:', error);\n      let errorMessage = 'Failed to download SOP.';\n      if (error !== null && error !== void 0 && (_error$response5 = error.response) !== null && _error$response5 !== void 0 && (_error$response5$data = _error$response5.data) !== null && _error$response5$data !== void 0 && _error$response5$data.detail) {\n        var _error$response6, _error$response6$data;\n        errorMessage = error === null || error === void 0 ? void 0 : (_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.detail;\n      }\n      const errorToThrow = new Error(errorMessage);\n      errorToThrow.response = error.response;\n      throw errorToThrow;\n    }\n  }\n};\nexport default sopService;", "map": {"version": 3, "names": ["apiService", "API_URLS", "axios", "supabase", "sopService", "getAllSOPs", "get", "SOP", "LIST", "uploadSOP", "formData", "data", "session", "auth", "getSession", "token", "access_token", "response", "post", "UPLOAD", "headers", "error", "_error$response", "_error$response$data", "console", "errorMessage", "detail", "_error$response2", "_error$response2$data", "errorToThrow", "Error", "deleteSOP", "sopId", "delete", "DELETE", "_error$response3", "_error$response3$data", "_error$response4", "_error$response4$data", "downloadSOP", "DOWNLOAD", "responseType", "_error$response5", "_error$response5$data", "_error$response6", "_error$response6$data"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/services/sopService.js"], "sourcesContent": ["import apiService from './api';\r\nimport API_URLS from '../config/apiUrls';\r\nimport axios from 'axios';\r\nimport supabase from '../supabase';\r\n\r\nconst sopService = {\r\n  // Get all SOPs\r\n  getAllSOPs: async () => {\r\n    return await apiService.get(API_URLS.SOP.LIST);\r\n  },\r\n\r\n  // Upload a new SOP\r\n  uploadSOP: async (formData) => {\r\n    try {\r\n      // Get the token from supabase\r\n      const { data: { session } } = await supabase.auth.getSession();\r\n      const token = session?.access_token;\r\n      \r\n      // Make a direct axios call for file upload\r\n      const response = await axios.post(API_URLS.SOP.UPLOAD, formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n          'Authorization': `Bearer ${token}`\r\n        }\r\n      });\r\n      \r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error uploading SOP:', error);\r\n      \r\n      let errorMessage = 'Failed to upload SOP.';\r\n      if (error?.response?.data?.detail) {\r\n        errorMessage = error?.response?.data?.detail;\r\n      }\r\n      \r\n      const errorToThrow = new Error(errorMessage);\r\n      errorToThrow.response = error.response;\r\n      throw errorToThrow;\r\n    }\r\n  },\r\n\r\n  // Delete a SOP\r\n  deleteSOP: async (sopId) => {\r\n    try {\r\n      const response = await apiService.delete(`${API_URLS.SOP.DELETE}/${sopId}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting SOP:', error);\r\n      \r\n      let errorMessage = 'Failed to delete SOP.';\r\n      if (error?.response?.data?.detail) {\r\n        errorMessage = error?.response?.data?.detail;\r\n      }\r\n      \r\n      const errorToThrow = new Error(errorMessage);\r\n      errorToThrow.response = error.response;\r\n      throw errorToThrow;\r\n    }\r\n  },\r\n  \r\n  // Download a SOP\r\n  downloadSOP: async (sopId) => {\r\n    try {\r\n      // Get the token from localStorage\r\n      const { data: { session } } = await supabase.auth.getSession();\r\n      const token = session?.access_token;\r\n      \r\n      // Make a direct axios call for file download with responseType blob\r\n      const response = await axios.get(`${API_URLS.SOP.DOWNLOAD}/${sopId}`, {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`\r\n        },\r\n        responseType: 'blob' // Important for file downloads\r\n      });\r\n      \r\n      return response;\r\n    } catch (error) {\r\n      console.error('Error downloading SOP:', error);\r\n      \r\n      let errorMessage = 'Failed to download SOP.';\r\n      if (error?.response?.data?.detail) {\r\n        errorMessage = error?.response?.data?.detail;\r\n      }\r\n      \r\n      const errorToThrow = new Error(errorMessage);\r\n      errorToThrow.response = error.response;\r\n      throw errorToThrow;\r\n    }\r\n  },\r\n};\r\n\r\nexport default sopService; "], "mappings": "AAAA,OAAOA,UAAU,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,aAAa;AAElC,MAAMC,UAAU,GAAG;EACjB;EACAC,UAAU,EAAE,MAAAA,CAAA,KAAY;IACtB,OAAO,MAAML,UAAU,CAACM,GAAG,CAACL,QAAQ,CAACM,GAAG,CAACC,IAAI,CAAC;EAChD,CAAC;EAED;EACAC,SAAS,EAAE,MAAOC,QAAQ,IAAK;IAC7B,IAAI;MACF;MACA,MAAM;QAAEC,IAAI,EAAE;UAAEC;QAAQ;MAAE,CAAC,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAACC,UAAU,CAAC,CAAC;MAC9D,MAAMC,KAAK,GAAGH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,YAAY;;MAEnC;MACA,MAAMC,QAAQ,GAAG,MAAMf,KAAK,CAACgB,IAAI,CAACjB,QAAQ,CAACM,GAAG,CAACY,MAAM,EAAET,QAAQ,EAAE;QAC/DU,OAAO,EAAE;UACP,cAAc,EAAE,qBAAqB;UACrC,eAAe,EAAE,UAAUL,KAAK;QAClC;MACF,CAAC,CAAC;MAEF,OAAOE,QAAQ,CAACN,IAAI;IACtB,CAAC,CAAC,OAAOU,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACH,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAE5C,IAAII,YAAY,GAAG,uBAAuB;MAC1C,IAAIJ,KAAK,aAALA,KAAK,gBAAAC,eAAA,GAALD,KAAK,CAAEJ,QAAQ,cAAAK,eAAA,gBAAAC,oBAAA,GAAfD,eAAA,CAAiBX,IAAI,cAAAY,oBAAA,eAArBA,oBAAA,CAAuBG,MAAM,EAAE;QAAA,IAAAC,gBAAA,EAAAC,qBAAA;QACjCH,YAAY,GAAGJ,KAAK,aAALA,KAAK,wBAAAM,gBAAA,GAALN,KAAK,CAAEJ,QAAQ,cAAAU,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBhB,IAAI,cAAAiB,qBAAA,uBAArBA,qBAAA,CAAuBF,MAAM;MAC9C;MAEA,MAAMG,YAAY,GAAG,IAAIC,KAAK,CAACL,YAAY,CAAC;MAC5CI,YAAY,CAACZ,QAAQ,GAAGI,KAAK,CAACJ,QAAQ;MACtC,MAAMY,YAAY;IACpB;EACF,CAAC;EAED;EACAE,SAAS,EAAE,MAAOC,KAAK,IAAK;IAC1B,IAAI;MACF,MAAMf,QAAQ,GAAG,MAAMjB,UAAU,CAACiC,MAAM,CAAC,GAAGhC,QAAQ,CAACM,GAAG,CAAC2B,MAAM,IAAIF,KAAK,EAAE,CAAC;MAC3E,OAAOf,QAAQ,CAACN,IAAI;IACtB,CAAC,CAAC,OAAOU,KAAK,EAAE;MAAA,IAAAc,gBAAA,EAAAC,qBAAA;MACdZ,OAAO,CAACH,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAE3C,IAAII,YAAY,GAAG,uBAAuB;MAC1C,IAAIJ,KAAK,aAALA,KAAK,gBAAAc,gBAAA,GAALd,KAAK,CAAEJ,QAAQ,cAAAkB,gBAAA,gBAAAC,qBAAA,GAAfD,gBAAA,CAAiBxB,IAAI,cAAAyB,qBAAA,eAArBA,qBAAA,CAAuBV,MAAM,EAAE;QAAA,IAAAW,gBAAA,EAAAC,qBAAA;QACjCb,YAAY,GAAGJ,KAAK,aAALA,KAAK,wBAAAgB,gBAAA,GAALhB,KAAK,CAAEJ,QAAQ,cAAAoB,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiB1B,IAAI,cAAA2B,qBAAA,uBAArBA,qBAAA,CAAuBZ,MAAM;MAC9C;MAEA,MAAMG,YAAY,GAAG,IAAIC,KAAK,CAACL,YAAY,CAAC;MAC5CI,YAAY,CAACZ,QAAQ,GAAGI,KAAK,CAACJ,QAAQ;MACtC,MAAMY,YAAY;IACpB;EACF,CAAC;EAED;EACAU,WAAW,EAAE,MAAOP,KAAK,IAAK;IAC5B,IAAI;MACF;MACA,MAAM;QAAErB,IAAI,EAAE;UAAEC;QAAQ;MAAE,CAAC,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAACC,UAAU,CAAC,CAAC;MAC9D,MAAMC,KAAK,GAAGH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,YAAY;;MAEnC;MACA,MAAMC,QAAQ,GAAG,MAAMf,KAAK,CAACI,GAAG,CAAC,GAAGL,QAAQ,CAACM,GAAG,CAACiC,QAAQ,IAAIR,KAAK,EAAE,EAAE;QACpEZ,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,KAAK;QAClC,CAAC;QACD0B,YAAY,EAAE,MAAM,CAAC;MACvB,CAAC,CAAC;MAEF,OAAOxB,QAAQ;IACjB,CAAC,CAAC,OAAOI,KAAK,EAAE;MAAA,IAAAqB,gBAAA,EAAAC,qBAAA;MACdnB,OAAO,CAACH,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAE9C,IAAII,YAAY,GAAG,yBAAyB;MAC5C,IAAIJ,KAAK,aAALA,KAAK,gBAAAqB,gBAAA,GAALrB,KAAK,CAAEJ,QAAQ,cAAAyB,gBAAA,gBAAAC,qBAAA,GAAfD,gBAAA,CAAiB/B,IAAI,cAAAgC,qBAAA,eAArBA,qBAAA,CAAuBjB,MAAM,EAAE;QAAA,IAAAkB,gBAAA,EAAAC,qBAAA;QACjCpB,YAAY,GAAGJ,KAAK,aAALA,KAAK,wBAAAuB,gBAAA,GAALvB,KAAK,CAAEJ,QAAQ,cAAA2B,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBjC,IAAI,cAAAkC,qBAAA,uBAArBA,qBAAA,CAAuBnB,MAAM;MAC9C;MAEA,MAAMG,YAAY,GAAG,IAAIC,KAAK,CAACL,YAAY,CAAC;MAC5CI,YAAY,CAACZ,QAAQ,GAAGI,KAAK,CAACJ,QAAQ;MACtC,MAAMY,YAAY;IACpB;EACF;AACF,CAAC;AAED,eAAezB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}