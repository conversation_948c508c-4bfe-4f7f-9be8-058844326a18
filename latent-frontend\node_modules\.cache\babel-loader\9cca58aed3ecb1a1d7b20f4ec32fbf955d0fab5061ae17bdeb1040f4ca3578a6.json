{"ast": null, "code": "/**\r\n * Formats a date string for display in a user-friendly format\r\n * @param {string} dateString - The date string to format\r\n * @returns {string} - Formatted date string like \"May 24, 2024 at 9:29 PM\" or error message\r\n */\nexport const formatDate = dateString => {\n  if (!dateString) return 'N/A';\n  const date = new Date(dateString);\n  if (isNaN(date.getTime())) return 'Invalid date';\n\n  // Format: \"May 24, 2024 at 9:29 PM\"\n  return date.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric'\n  }) + ' at ' + date.toLocaleTimeString('en-US', {\n    hour: 'numeric',\n    minute: '2-digit',\n    hour12: true\n  });\n};\n\n// /**\n//  * Formats a date to a relative time string (e.g., \"2 hours ago\", \"Yesterday\")\n//  * @param {string} dateString - The date string to format\n//  * @returns {string} - Relative time string\n//  */\n// export const formatRelativeTime = (dateString) => {\n//   if (!dateString) return 'N/A';\n\n//   const date = new Date(dateString);\n//   if (isNaN(date.getTime())) return 'Invalid date';\n\n//   const now = new Date();\n//   const diffInSeconds = Math.floor((now - date) / 1000);\n\n//   // Less than a minute\n//   if (diffInSeconds < 60) {\n//     return 'Just now';\n//   }\n\n//   // Less than an hour\n//   if (diffInSeconds < 3600) {\n//     const minutes = Math.floor(diffInSeconds / 60);\n//     return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;\n//   }\n\n//   // Less than a day\n//   if (diffInSeconds < 86400) {\n//     const hours = Math.floor(diffInSeconds / 3600);\n//     return `${hours} hour${hours !== 1 ? 's' : ''} ago`;\n//   }\n\n//   // Less than a week\n//   if (diffInSeconds < 604800) {\n//     const days = Math.floor(diffInSeconds / 86400);\n//     if (days === 1) return 'Yesterday';\n//     return `${days} days ago`;\n//   }\n\n//   // Less than a month\n//   if (diffInSeconds < 2592000) {\n//     const weeks = Math.floor(diffInSeconds / 604800);\n//     return `${weeks} week${weeks !== 1 ? 's' : ''} ago`;\n//   }\n\n//   // Fall back to absolute date for older dates\n//   return formatDate(dateString);\n// };\n\n// export default {\n//   formatDate,\n//   formatRelativeTime\n// };", "map": {"version": 3, "names": ["formatDate", "dateString", "date", "Date", "isNaN", "getTime", "toLocaleDateString", "year", "month", "day", "toLocaleTimeString", "hour", "minute", "hour12"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/utils/dateUtils.js"], "sourcesContent": ["/**\r\n * Formats a date string for display in a user-friendly format\r\n * @param {string} dateString - The date string to format\r\n * @returns {string} - Formatted date string like \"May 24, 2024 at 9:29 PM\" or error message\r\n */\r\nexport const formatDate = (dateString) => {\r\n  if (!dateString) return 'N/A';\r\n  \r\n  const date = new Date(dateString);\r\n  if (isNaN(date.getTime())) return 'Invalid date';\r\n  \r\n  // Format: \"May 24, 2024 at 9:29 PM\"\r\n  return date.toLocaleDateString('en-US', {\r\n    year: 'numeric',\r\n    month: 'short',\r\n    day: 'numeric',\r\n  }) + ' at ' + date.toLocaleTimeString('en-US', {\r\n    hour: 'numeric',\r\n    minute: '2-digit',\r\n    hour12: true\r\n  });\r\n};\r\n\r\n\r\n\r\n// /**\r\n//  * Formats a date to a relative time string (e.g., \"2 hours ago\", \"Yesterday\")\r\n//  * @param {string} dateString - The date string to format\r\n//  * @returns {string} - Relative time string\r\n//  */\r\n// export const formatRelativeTime = (dateString) => {\r\n//   if (!dateString) return 'N/A';\r\n  \r\n//   const date = new Date(dateString);\r\n//   if (isNaN(date.getTime())) return 'Invalid date';\r\n  \r\n//   const now = new Date();\r\n//   const diffInSeconds = Math.floor((now - date) / 1000);\r\n  \r\n//   // Less than a minute\r\n//   if (diffInSeconds < 60) {\r\n//     return 'Just now';\r\n//   }\r\n  \r\n//   // Less than an hour\r\n//   if (diffInSeconds < 3600) {\r\n//     const minutes = Math.floor(diffInSeconds / 60);\r\n//     return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;\r\n//   }\r\n  \r\n//   // Less than a day\r\n//   if (diffInSeconds < 86400) {\r\n//     const hours = Math.floor(diffInSeconds / 3600);\r\n//     return `${hours} hour${hours !== 1 ? 's' : ''} ago`;\r\n//   }\r\n  \r\n//   // Less than a week\r\n//   if (diffInSeconds < 604800) {\r\n//     const days = Math.floor(diffInSeconds / 86400);\r\n//     if (days === 1) return 'Yesterday';\r\n//     return `${days} days ago`;\r\n//   }\r\n  \r\n//   // Less than a month\r\n//   if (diffInSeconds < 2592000) {\r\n//     const weeks = Math.floor(diffInSeconds / 604800);\r\n//     return `${weeks} week${weeks !== 1 ? 's' : ''} ago`;\r\n//   }\r\n  \r\n//   // Fall back to absolute date for older dates\r\n//   return formatDate(dateString);\r\n// };\r\n\r\n// export default {\r\n//   formatDate,\r\n//   formatRelativeTime\r\n// }; "], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,UAAU,GAAIC,UAAU,IAAK;EACxC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;EAE7B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;EACjC,IAAIG,KAAK,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,cAAc;;EAEhD;EACA,OAAOH,IAAI,CAACI,kBAAkB,CAAC,OAAO,EAAE;IACtCC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,OAAO;IACdC,GAAG,EAAE;EACP,CAAC,CAAC,GAAG,MAAM,GAAGP,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;IAC7CC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAID;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}