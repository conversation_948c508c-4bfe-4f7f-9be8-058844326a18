{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\zipp\\\\latent-frontend\\\\src\\\\components\\\\common\\\\SOPDropdown\\\\SOPDropdown.js\",\n  _s = $RefreshSig$();\nimport { useState } from \"react\";\nimport { ChevronDown, FileText } from \"lucide-react\";\nimport \"./SOPDropdown.css\";\nimport LoadingSpinner from \"../LoadingSpinner/LoadingSpinner\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SOPDropdown = ({\n  sopTitles,\n  selectedSOPs,\n  onSOPChange,\n  isLoading\n}) => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const handleSOPToggle = sopTitle => {\n    if (selectedSOPs.includes(sopTitle)) {\n      onSOPChange(selectedSOPs.filter(sop => sop !== sopTitle));\n    } else {\n      onSOPChange([...selectedSOPs, sopTitle]);\n    }\n  };\n  const handleSelectAll = () => {\n    onSOPChange([...sopTitles]);\n  };\n  const handleClearAll = () => {\n    onSOPChange([]);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"sop-dropdown-wrapper\",\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"sop-label\",\n      children: \"Select Documents (Optional)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sop-selector\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: () => setIsOpen(!isOpen),\n        className: \"sop-button\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sop-button-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sop-summary\",\n            children: [/*#__PURE__*/_jsxDEV(FileText, {\n              className: \"sop-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: selectedSOPs.length === 0 ? \"All documents\" : selectedSOPs.length === 1 ? selectedSOPs[0] : `${selectedSOPs.length} documents selected`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ChevronDown, {\n            className: `sop-chevron ${isOpen ? \"rotate\" : \"\"}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sop-dropdown\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sop-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSelectAll,\n            className: \"sop-action-button blue\",\n            children: \"Select All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleClearAll,\n            className: \"sop-action-button gray\",\n            children: \"Clear All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sop-options\",\n          children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sop-loading\",\n            children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 17\n          }, this) : sopTitles.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sop-empty\",\n            children: \"No documents available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 17\n          }, this) : sopTitles.map(sopTitle => /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"sop-option\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: selectedSOPs.includes(sopTitle),\n              onChange: () => handleSOPToggle(sopTitle),\n              className: \"sop-checkbox\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sop-title\",\n              children: sopTitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 21\n            }, this)]\n          }, sopTitle, true, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), selectedSOPs.length > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"sop-selected\",\n      children: [\"Selected: \", selectedSOPs.join(\", \")]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_s(SOPDropdown, \"+sus0Lb0ewKHdwiUhiTAJFoFyQ0=\");\n_c = SOPDropdown;\nexport default SOPDropdown;\nvar _c;\n$RefreshReg$(_c, \"SOPDropdown\");", "map": {"version": 3, "names": ["useState", "ChevronDown", "FileText", "LoadingSpinner", "jsxDEV", "_jsxDEV", "SOPDropdown", "sop<PERSON><PERSON><PERSON>", "selectedSOPs", "onSOPChange", "isLoading", "_s", "isOpen", "setIsOpen", "handleSOPToggle", "sopTitle", "includes", "filter", "sop", "handleSelectAll", "handleClearAll", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onClick", "length", "size", "map", "checked", "onChange", "join", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/components/common/SOPDropdown/SOPDropdown.js"], "sourcesContent": ["import { useState } from \"react\";\r\nimport { ChevronDown, FileText } from \"lucide-react\";\r\nimport \"./SOPDropdown.css\";\r\nimport LoadingSpinner from \"../LoadingSpinner/LoadingSpinner\";\r\n\r\nconst SOPDropdown = ({ sopTitles, selectedSOPs, onSOPChange, isLoading }) => {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n\r\n  const handleSOPToggle = (sopTitle) => {\r\n    if (selectedSOPs.includes(sopTitle)) {\r\n      onSOPChange(selectedSOPs.filter((sop) => sop !== sopTitle));\r\n    } else {\r\n      onSOPChange([...selectedSOPs, sopTitle]);\r\n    }\r\n  };\r\n\r\n  const handleSelectAll = () => {\r\n    onSOPChange([...sopTitles]);\r\n  };\r\n\r\n  const handleClearAll = () => {\r\n    onSOPChange([]);\r\n  };\r\n\r\n  return (\r\n    <div className=\"sop-dropdown-wrapper\">\r\n      <label className=\"sop-label\">Select Documents (Optional)</label>\r\n      <div className=\"sop-selector\">\r\n        <button\r\n          type=\"button\"\r\n          onClick={() => setIsOpen(!isOpen)}\r\n          className=\"sop-button\"\r\n        >\r\n          <div className=\"sop-button-content\">\r\n            <div className=\"sop-summary\">\r\n              <FileText className=\"sop-icon\" />\r\n              <span>\r\n                {selectedSOPs.length === 0\r\n                  ? \"All documents\"\r\n                  : selectedSOPs.length === 1\r\n                  ? selectedSOPs[0]\r\n                  : `${selectedSOPs.length} documents selected`}\r\n              </span>\r\n            </div>\r\n            <ChevronDown className={`sop-chevron ${isOpen ? \"rotate\" : \"\"}`} />\r\n          </div>\r\n        </button>\r\n\r\n        {isOpen && (\r\n          <div className=\"sop-dropdown\">\r\n            <div className=\"sop-actions\">\r\n              <button\r\n                onClick={handleSelectAll}\r\n                className=\"sop-action-button blue\"\r\n              >\r\n                Select All\r\n              </button>\r\n              <button\r\n                onClick={handleClearAll}\r\n                className=\"sop-action-button gray\"\r\n              >\r\n                Clear All\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"sop-options\">\r\n              {isLoading ? (\r\n                <div className=\"sop-loading\">\r\n                  <LoadingSpinner size=\"small\" />\r\n                </div>\r\n              ) : sopTitles.length === 0 ? (\r\n                <div className=\"sop-empty\">No documents available</div>\r\n              ) : (\r\n                sopTitles.map((sopTitle) => (\r\n                  <label key={sopTitle} className=\"sop-option\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      checked={selectedSOPs.includes(sopTitle)}\r\n                      onChange={() => handleSOPToggle(sopTitle)}\r\n                      className=\"sop-checkbox\"\r\n                    />\r\n                    <span className=\"sop-title\">{sopTitle}</span>\r\n                  </label>\r\n                ))\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {selectedSOPs.length > 0 && (\r\n        <p className=\"sop-selected\">Selected: {selectedSOPs.join(\", \")}</p>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SOPDropdown;"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,WAAW,EAAEC,QAAQ,QAAQ,cAAc;AACpD,OAAO,mBAAmB;AAC1B,OAAOC,cAAc,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,WAAW,GAAGA,CAAC;EAAEC,SAAS;EAAEC,YAAY;EAAEC,WAAW;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC3E,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAE3C,MAAMc,eAAe,GAAIC,QAAQ,IAAK;IACpC,IAAIP,YAAY,CAACQ,QAAQ,CAACD,QAAQ,CAAC,EAAE;MACnCN,WAAW,CAACD,YAAY,CAACS,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAKH,QAAQ,CAAC,CAAC;IAC7D,CAAC,MAAM;MACLN,WAAW,CAAC,CAAC,GAAGD,YAAY,EAAEO,QAAQ,CAAC,CAAC;IAC1C;EACF,CAAC;EAED,MAAMI,eAAe,GAAGA,CAAA,KAAM;IAC5BV,WAAW,CAAC,CAAC,GAAGF,SAAS,CAAC,CAAC;EAC7B,CAAC;EAED,MAAMa,cAAc,GAAGA,CAAA,KAAM;IAC3BX,WAAW,CAAC,EAAE,CAAC;EACjB,CAAC;EAED,oBACEJ,OAAA;IAAKgB,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBACnCjB,OAAA;MAAOgB,SAAS,EAAC,WAAW;MAAAC,QAAA,EAAC;IAA2B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAChErB,OAAA;MAAKgB,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BjB,OAAA;QACEsB,IAAI,EAAC,QAAQ;QACbC,OAAO,EAAEA,CAAA,KAAMf,SAAS,CAAC,CAACD,MAAM,CAAE;QAClCS,SAAS,EAAC,YAAY;QAAAC,QAAA,eAEtBjB,OAAA;UAAKgB,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjCjB,OAAA;YAAKgB,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BjB,OAAA,CAACH,QAAQ;cAACmB,SAAS,EAAC;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjCrB,OAAA;cAAAiB,QAAA,EACGd,YAAY,CAACqB,MAAM,KAAK,CAAC,GACtB,eAAe,GACfrB,YAAY,CAACqB,MAAM,KAAK,CAAC,GACzBrB,YAAY,CAAC,CAAC,CAAC,GACf,GAAGA,YAAY,CAACqB,MAAM;YAAqB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNrB,OAAA,CAACJ,WAAW;YAACoB,SAAS,EAAE,eAAeT,MAAM,GAAG,QAAQ,GAAG,EAAE;UAAG;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,EAERd,MAAM,iBACLP,OAAA;QAAKgB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BjB,OAAA;UAAKgB,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BjB,OAAA;YACEuB,OAAO,EAAET,eAAgB;YACzBE,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EACnC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrB,OAAA;YACEuB,OAAO,EAAER,cAAe;YACxBC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EACnC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENrB,OAAA;UAAKgB,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBZ,SAAS,gBACRL,OAAA;YAAKgB,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BjB,OAAA,CAACF,cAAc;cAAC2B,IAAI,EAAC;YAAO;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,GACJnB,SAAS,CAACsB,MAAM,KAAK,CAAC,gBACxBxB,OAAA;YAAKgB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GAEvDnB,SAAS,CAACwB,GAAG,CAAEhB,QAAQ,iBACrBV,OAAA;YAAsBgB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC1CjB,OAAA;cACEsB,IAAI,EAAC,UAAU;cACfK,OAAO,EAAExB,YAAY,CAACQ,QAAQ,CAACD,QAAQ,CAAE;cACzCkB,QAAQ,EAAEA,CAAA,KAAMnB,eAAe,CAACC,QAAQ,CAAE;cAC1CM,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACFrB,OAAA;cAAMgB,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEP;YAAQ;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAPnCX,QAAQ;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQb,CACR;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELlB,YAAY,CAACqB,MAAM,GAAG,CAAC,iBACtBxB,OAAA;MAAGgB,SAAS,EAAC,cAAc;MAAAC,QAAA,GAAC,YAAU,EAACd,YAAY,CAAC0B,IAAI,CAAC,IAAI,CAAC;IAAA;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CACnE;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACf,EAAA,CA1FIL,WAAW;AAAA6B,EAAA,GAAX7B,WAAW;AA4FjB,eAAeA,WAAW;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}