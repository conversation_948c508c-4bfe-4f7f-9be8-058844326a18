.sop-dropdown-wrapper {
    position: relative;
    margin-bottom: 1rem;
    width: 115%;
    max-width: 420px;
  }
  
  .sop-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
  }
  
  .sop-selector {
    position: relative;
  }
  
  .sop-button {
    width: 100%;
    background-color: white;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    text-align: left;
    cursor: pointer;
  }
  
  .sop-button:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 2px var(--primary-blue-hover);
  }
  
  .sop-button-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .sop-summary {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #111827;
  }
  
  .sop-icon {
    width: 1rem;
    height: 1rem;
    color: #6b7280;
  }
  
  .sop-chevron {
    width: 1rem;
    height: 1rem;
    color: #6b7280;
    transition: transform 0.2s ease;
  }
  
  .sop-chevron.rotate {
    transform: rotate(180deg);
  }
  
  .sop-dropdown {
    position: absolute;
    z-index: 10;
    top: 100%;
    left: 0;
    width: 100%;
    min-width: 250px;
    margin-top: 0.25rem;
    background-color: white;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-height: 15rem;
    overflow-y: auto;
  }
  
  .sop-actions {
    padding: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    gap: 0.5rem;
  }
  
  .sop-action-button {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 500;
    cursor: pointer;
    border: none;
  }
  
  .sop-action-button.blue {
    background-color: var(--primary-blue-light);
    color: var(--primary-blue);
  }
  
  .sop-action-button.blue:hover {
    background-color: var(--primary-blue-hover);
  }
  
  .sop-action-button.gray {
    background-color: #f3f4f6;
    color: #374151;
  }
  
  .sop-action-button.gray:hover {
    background-color: #e5e7eb;
  }
  
  .sop-options {
    padding: 0.25rem 0;
  }
  
  .sop-option {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    cursor: pointer;
  }
  
  .sop-option:hover {
    background-color: #f9fafb;
  }
  
  .sop-checkbox {
    width: 1rem;
    height: 1rem;
    accent-color: var(--primary-blue);
  }
  
  .sop-title {
    margin-left: 0.75rem;
    font-size: 0.875rem;
    color: #111827;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .sop-empty {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    color: #6b7280;
  }
  
  .sop-selected {
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
  }
  
  .sop-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #0d0b10d3;
  }