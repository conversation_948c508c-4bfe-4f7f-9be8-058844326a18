import configparser
import anthropic
from app.services.gap_analysis_utils.config import Config


class AnthropicClient:
    def __init__(self, config: Config):
        self.api_key = config.get("Anthropic.api_key")
        self.client = anthropic.Anthropic(api_key=self.api_key)
        self.model = "claude-3-7-sonnet-20250219"

    def generate_response(self, user_input):
        try:
            response = self.client.messages.create(
                model=self.model,
                messages=[{"role": "user", "content": user_input}],
                max_tokens=8000,
                temperature=0,
            )
            return response.content[0].text #type: ignore[union-attr]
        except Exception:
            raise Exception("A temporary issue occurred while analyzing the SOP.")


# Usage example
if __name__ == "__main__":
    from app.services.gap_analysis_utils.config import Config
    config = Config("app/services/gap_analysis_utils/user_config.yaml")
    wrapper = AnthropicClient(config)
    user_input = "Write a sonnet about the beauty of nature."
    response = wrapper.generate_response(user_input)
    print(response)
