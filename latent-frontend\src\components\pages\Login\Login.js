import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import supabase from '../../../supabase';
import './Login.css';
import API_URLS from '../../../config/apiUrls';

const Login = () => {
  const location = useLocation();
  const [activeTab, setActiveTab] = useState('login');
  const [formData, setFormData] = useState({
    loginEmail: '',
    loginPassword: '',
    signupEmail: '',
    signupPassword: '',
    signupPasswordConfirm: '',
    firstName: '',
    lastName: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [message, setMessage] = useState('');
  const [isConfirmationAccess, setIsConfirmationAccess] = useState(false);
  // const navigate = useNavigate();

  useEffect(() => {
    // Add comprehensive debugging
    
    
    // Check if accessed via confirmation email
    const urlParams = new URLSearchParams(location.search);
    const token = urlParams.get('token');
    const type = urlParams.get('type');
    const confirmationType = urlParams.get('confirmation_url');
    
    console.log('URL Params - token:', token, 'type:', type, 'confirmationType:', confirmationType);
    
    // Check URL hash for access_token (format: #access_token=...)
    const hash = location.hash;
    const hasAccessToken = hash && hash.includes('access_token');
    
    console.log('Hash check - hasAccessToken:', hasAccessToken);
    
    // Check if URL comes from Supabase confirmation email (sendibt3.com redirect)
    const referrer = document.referrer;
    const isSupabaseConfirmation = referrer && referrer.includes('sendibt3.com');
    
    console.log('Referrer check - isSupabaseConfirmation:', isSupabaseConfirmation);
    
    // Check for confirmation email indicators in current URL
    const currentUrl = window.location.href;
    const isConfirmationEmail = currentUrl.includes('sendibt3.com') || 
                               currentUrl.includes('confirmation') ||
                               currentUrl.includes('verify');
    

    
    // Check all hash parameters
    if (hash) {
      console.log('Hash parameters detected:', hash);
      const hashParams = new URLSearchParams(hash.substring(1)); // Remove # from hash
      console.log('Parsed hash params:');
      for (let [key, value] of hashParams) {
        console.log(`  ${key}: ${value}`);
      }
    }
    
    // If hasAccessToken or confirmation email detected, logout and redirect to clean login page
    if (hasAccessToken || isSupabaseConfirmation || isConfirmationEmail) {

      
      // Logout user and redirect to clean login URL
      const logoutAndRedirect = async () => {
        try {
          // Sign out from Supabase if user is logged in
          await supabase.auth.signOut();
          console.log('User logged out successfully');
        } catch (error) {
          console.error('Error logging out user:', error);
          // Continue with redirect even if signOut fails
        } finally {
          // Clear the hash and redirect to clean login URL
          window.location.href = '/login';
        }
      };
      
      logoutAndRedirect();
      return;
    }
    
    // Check localStorage for confirmation access flag
    const confirmationAccessFlag = localStorage.getItem('confirmation_email_access');

    
    if (token || type || confirmationType || confirmationAccessFlag) {

      // setIsConfirmationAccess(true);
      // setError('Login is temporarily disabled. This appears to be a confirmation email link. Please access the application directly.');
      
      // Clear the flag if it exists
      if (confirmationAccessFlag) {
        localStorage.removeItem('confirmation_email_access');
      }
    }
    

  }, [location]);

  const handleInputChange = (e) => {
    const { id, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [id]: value
    }));
  };

  const handleLogin = async (e) => {
    e.preventDefault();
    
    // Prevent login if accessed via confirmation email
    if (isConfirmationAccess) {
      setError('Login is disabled when accessing via confirmation email. Please visit the application directly.');
      return;
    }
    
    setLoading(true);
    setError(null);
    setMessage('');

    // Basic validation
    // Check user status before proceeding with login
    try {
      const response = await fetch(API_URLS.USER.CHECK_USER_EXISTS(formData.loginEmail), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (response.status === 401) {
        const errorData = await response.json();
        setError(errorData.message || 'User account is not active');
        setLoading(false);
        return;
      }
      
      if (response.status !== 200) {
        setError('Failed to verify user status. Please try again.');
        setLoading(false);
        return;
      }
    } catch (error) {
      let errorMessage = 'Failed to verify user status. Please try again.';
      if (error?.response?.data?.detail) {
        errorMessage = error?.response?.data?.detail;
      }
      
      setError(errorMessage);
      setLoading(false);
      return;
    }

    
    if (!formData.loginEmail || !formData.loginPassword) {
      setError('Please fill in all fields');
      setLoading(false);
      return;
    }

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: formData.loginEmail,
        password: formData.loginPassword,
      });

      if (error) {
        if (error.message.includes('Invalid login credentials')) {
          setError('Invalid email or password');
        } else {
          setError(error.message);
        }
        return;
      }

      // If login successful, navigate to dashboard
      if (data?.user) {
        window.location.href = '/dashboard'; // This will force a full page reload
        // OR use this if you want to keep React state:
        // navigate('/dashboard', { replace: true });

      }
    } catch (error) {
      let errorMessage = 'An unexpected error occurred. Please try again.';
      if (error?.response?.data?.detail) {
        errorMessage = error?.response?.data?.detail;
      }
      
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleSignup = async (e) => {
    e.preventDefault();
    
    // Prevent signup if accessed via confirmation email
    if (isConfirmationAccess) {
      setError('Signup is disabled when accessing via confirmation email. Please visit the application directly.');
      return;
    }
    
    setLoading(true);
    setError(null);
    setMessage('');

    // Basic validation
    if (!formData.signupEmail || !formData.signupPassword || !formData.signupPasswordConfirm || 
        !formData.firstName ) {
      setError('Please fill in all required fields');
      setLoading(false);
      return;
    }

    // Password confirmation check
    if (formData.signupPassword !== formData.signupPasswordConfirm) {
      setError('Passwords do not match');
      setLoading(false);
      return;
    }

    try {
      // Normalize the email
      const normalizedEmail = formData.signupEmail.trim().toLowerCase();

      console.log("Attempting signup for:", normalizedEmail);

      // Attempt signup
      const { data, error: signupError } = await supabase.auth.signUp({
        email: normalizedEmail,
        password: formData.signupPassword,
        options: {
          data: {
            first_name: formData.firstName.trim(),
            last_name: formData.lastName.trim() === '' ? null : formData.lastName.trim()
          }
        }
      });

      console.log("Signup response data:", data);
      console.log("Signup error:", signupError);

      // Check for errors
      if (signupError) {
        console.error("Signup error details:", signupError);
        
        if (signupError.message.includes('Database error saving new user')) {
          setError(`you are not allowed to login contact your administrator`);
        } else if (signupError.message.includes('User already registered')) {
          setError('User already signed up, please login');
        } else {
          setError(signupError.message || 'Error during signup');
        }
        return;
      }

      // Check if user was created
      if (data?.user) {
        // console.log("User created successfully:", data.user);
        
        // Check if email confirmation is required
        if (data.user.identities && data.user.identities.length === 0) {
          setError('This email is already registered. Please login instead.');
          return;
        }
        
        setMessage('Check your email for the confirmation link.');
        // Clear the form
        setFormData(prev => ({
          ...prev,
          signupEmail: '',
          signupPassword: '',
          signupPasswordConfirm: '',
          firstName: '',
          lastName: ''
        }));
      } else {
        // No user data but also no error - unusual case
        console.warn("No user data returned but no error either");
        setError('Something went wrong with registration. Please try again.');
      }
    } catch (error) {
      console.error("Unexpected error during signup:", error);
      
      let errorMessage = 'An unexpected error occurred. Please try again.';
      if (error?.response?.data?.detail) {
        errorMessage = error?.response?.data?.detail;
      }
      
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container">
      <div className="logo-container">

          <div style={{display: 'flex', alignItems: 'center', gap: '10px'}}>

            <img 
              src="/zipplogo.png" 
              alt="Zipp Logo" 
              className="logo-image"
              />  
            <span style={{color:'#000'}}>|</span>
            <img src='latentlogo.png'  className="logo-image" style={{width: '110px', height: '30px', marginTop: '4px'}}/>
              </div>

      </div>
      
      <div id="auth-container">
        <div className="tab-container">
          <button 
            className={`tab-button ${activeTab === 'login' ? 'active' : ''}`}
            onClick={() => setActiveTab('login')}
          >
            Login
          </button>
          <button 
            className={`tab-button ${activeTab === 'signup' ? 'active' : ''}`}
            onClick={() => setActiveTab('signup')}
          >
            Sign Up
          </button>
        </div>
        
        <div id="login" className={`tab-content ${activeTab === 'login' ? 'active' : ''}`}>
          <h2>Login</h2>
          <form onSubmit={handleLogin}>
            <div className="form-group">
              <label htmlFor="loginEmail">Email <span style={{color: 'var(--severity-high-text)'}}>*</span></label>
              <input
                type="email"
                id="loginEmail"
                value={formData.loginEmail}
                onChange={handleInputChange}
                required
              />
            </div>
            <div className="form-group">
              <label htmlFor="loginPassword">Password <span style={{color: 'var(--severity-high-text)'}}>*</span></label>
              <input
                type="password"
                id="loginPassword"
                value={formData.loginPassword}
                onChange={handleInputChange}
                required
              />
            </div>
            <button type="submit" className="btn" disabled={loading}>
              {loading ? 'Logging in...' : 'Login'}
            </button>
          </form>
        </div>

        <div id="signup" className={`tab-content ${activeTab === 'signup' ? 'active' : ''}`}>
          <h2>Sign Up</h2>
          <form onSubmit={handleSignup}>
            <div className="form-group">
              <label htmlFor="firstName">First Name <span style={{color: 'var(--severity-high-text)'}}>*</span></label>
              <input
                type="text"
                id="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                required
              />
            </div>
            <div className="form-group">
              <label htmlFor="lastName">Last Name</label>
              <input
                type="text"
                id="lastName"
                value={formData.lastName}
                onChange={handleInputChange}

              />
          </div>
          <div className="form-group">
              <label htmlFor="signupEmail">Email <span style={{color: 'var(--severity-high-text)'}}>*</span></label>
            <input
              type="email"
                id="signupEmail"
                value={formData.signupEmail}
                onChange={handleInputChange}
                required
              />
            </div>
            <div className="form-group">
              <label htmlFor="signupPassword">Password <span style={{color: 'var(--severity-high-text)'}}>*</span></label>
              <input
                type="password"
                id="signupPassword"
                value={formData.signupPassword}
                onChange={handleInputChange}
                required
            />
          </div>
          <div className="form-group">
              <label htmlFor="signupPasswordConfirm">Confirm Password <span style={{color: 'var(--severity-high-text)'}}>*</span></label>
            <input
              type="password"
                id="signupPasswordConfirm"
                value={formData.signupPasswordConfirm}
                onChange={handleInputChange}
                required
            />
          </div>
            <button type="submit" className="btn" disabled={loading}>
              {loading ? 'Signing up...' : 'Sign Up'}
          </button>
          </form>
          <p className="info-text">
            Only allowed emails can sign up. Please check if your email is on the allowlist.
          </p>
          </div>
      </div>
      
      {error && <div className="message error">{error}</div>}
      {message && <div className="message success">{message}</div>}
    </div>
  );
};

export default Login; 