{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\zipp\\\\latent-frontend\\\\src\\\\components\\\\common\\\\Navigation\\\\Navigation.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport supabase from '../../../supabase';\nimport './Navigation.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navigation = () => {\n  _s();\n  const location = useLocation();\n  // const navigate = useNavigate();\n  const [showDropdown, setShowDropdown] = useState(false);\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\n  const dropdownRef = useRef(null);\n  const navItems = [{\n    name: 'Overview',\n    path: '/dashboard'\n  }, {\n    name: 'SOPs',\n    path: '/sop-library'\n  }, {\n    name: 'Gap Analysis',\n    path: '/gap-analysis'\n  }, {\n    name: 'GxP Assistant',\n    path: '/chatbot'\n  }\n  // { name: 'Users', path: '/users' }\n  ];\n  const handleLogout = async () => {\n    try {\n      setIsLoggingOut(true);\n      const {\n        error\n      } = await supabase.auth.signOut();\n      if (error) throw error;\n\n      // Force a page reload to clear any cached state\n      window.location.href = '/login';\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error logging out:', error.message);\n      let errorMessage = 'Error logging out. Please try again.';\n      if (error !== null && error !== void 0 && (_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.detail) {\n        var _error$response2, _error$response2$data;\n        errorMessage = error === null || error === void 0 ? void 0 : (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail;\n      }\n      setIsLoggingOut(false);\n    }\n  };\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setShowDropdown(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"navigation-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"nav-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-logo\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '10px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/zipplogo.png\",\n              alt: \"Zipp Logo\",\n              className: \"logo-image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#000'\n              },\n              children: \"|\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"latentlogo.png\",\n              className: \"logo-image\",\n              style: {\n                width: '110px',\n                height: '30px',\n                marginTop: '4px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-profile\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-dropdown\",\n          ref: dropdownRef,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"profile-button\",\n            onClick: () => setShowDropdown(!showDropdown),\n            \"aria-label\": \"Settings menu\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"settings-icon\",\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), showDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dropdown-menu\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/profile\",\n              className: \"dropdown-item\",\n              onClick: () => setShowDropdown(false),\n              children: \"Profile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"dropdown-item\",\n              onClick: handleLogout,\n              disabled: isLoggingOut,\n              children: isLoggingOut ? /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"loading-text\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"loading-dots\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 23\n                }, this), \"Logging out\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 21\n              }, this) : 'Logout'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"nav-tabs\",\n      children: navItems.map((item, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: item.path,\n          className: `nav-tab ${location.pathname === item.path ? 'active' : ''}`,\n          children: item.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 13\n        }, this), index < navItems.length - 1 && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"nav-separator\",\n          children: \"\\u2022\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 45\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this);\n};\n_s(Navigation, \"G+Y9hwKpcsL7Puf40ZVtCAN/6SQ=\", false, function () {\n  return [useLocation];\n});\n_c = Navigation;\nexport default Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Link", "useLocation", "supabase", "jsxDEV", "_jsxDEV", "Navigation", "_s", "location", "showDropdown", "setShowDropdown", "isLoggingOut", "setIsLoggingOut", "dropdownRef", "navItems", "name", "path", "handleLogout", "error", "auth", "signOut", "window", "href", "_error$response", "_error$response$data", "console", "message", "errorMessage", "response", "data", "detail", "_error$response2", "_error$response2$data", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "className", "children", "to", "style", "display", "alignItems", "gap", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "width", "height", "marginTop", "ref", "onClick", "viewBox", "fill", "xmlns", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "disabled", "map", "item", "index", "Fragment", "pathname", "length", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/components/common/Navigation/Navigation.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport { Link, useLocation } from 'react-router-dom';\r\nimport supabase from '../../../supabase';\r\nimport './Navigation.css';\r\n\r\nconst Navigation = () => {\r\n  const location = useLocation();\r\n  // const navigate = useNavigate();\r\n  const [showDropdown, setShowDropdown] = useState(false);\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const dropdownRef = useRef(null);\r\n  \r\n  const navItems = [\r\n    { name: 'Overview', path: '/dashboard' },\r\n    { name: 'SOPs', path: '/sop-library' },\r\n    { name: 'Gap Analysis', path: '/gap-analysis' },\r\n    { name: 'GxP Assistant', path: '/chatbot' },\r\n    // { name: 'Users', path: '/users' }\r\n  ];\r\n\r\n  const handleLogout = async () => {\r\n    try {\r\n      setIsLoggingOut(true);\r\n      const { error } = await supabase.auth.signOut();\r\n      if (error) throw error;\r\n      \r\n      // Force a page reload to clear any cached state\r\n      window.location.href = '/login';\r\n      \r\n    } catch (error) {\r\n      console.error('Error logging out:', error.message);\r\n      \r\n      let errorMessage = 'Error logging out. Please try again.';\r\n      if (error?.response?.data?.detail) {\r\n        errorMessage = error?.response?.data?.detail;\r\n      }\r\n      \r\n      setIsLoggingOut(false);\r\n    }\r\n  };\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\r\n        setShowDropdown(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => document.removeEventListener('mousedown', handleClickOutside);\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"navigation-container\">\r\n      <div className=\"nav-header\">\r\n        <div className=\"nav-logo\">\r\n          <Link to=\"/dashboard\">\r\n          <div style={{display: 'flex', alignItems: 'center', gap: '10px'}}>\r\n\r\n            <img \r\n              src=\"/zipplogo.png\" \r\n              alt=\"Zipp Logo\" \r\n              className=\"logo-image\"\r\n              />  \r\n            <span style={{color:'#000'}}>|</span>\r\n            <img src='latentlogo.png'  className=\"logo-image\" style={{width: '110px', height: '30px', marginTop: '4px'}}/>\r\n              </div>\r\n          </Link>\r\n        </div>\r\n        <div className=\"user-profile\">\r\n          <div className=\"profile-dropdown\" ref={dropdownRef}>\r\n            <button \r\n              className=\"profile-button\" \r\n              onClick={() => setShowDropdown(!showDropdown)}\r\n              aria-label=\"Settings menu\"\r\n            >\r\n              <svg \r\n                className=\"settings-icon\" \r\n                width=\"24\" \r\n                height=\"24\" \r\n                viewBox=\"0 0 24 24\" \r\n                fill=\"none\" \r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n              >\r\n                <path \r\n                  d=\"M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z\" \r\n                  stroke=\"currentColor\" \r\n                  strokeWidth=\"2\" \r\n                  strokeLinecap=\"round\" \r\n                  strokeLinejoin=\"round\"\r\n                />\r\n                <path \r\n                  d=\"M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z\" \r\n                  stroke=\"currentColor\" \r\n                  strokeWidth=\"2\" \r\n                  strokeLinecap=\"round\" \r\n                  strokeLinejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </button>\r\n            \r\n            {showDropdown && (\r\n              <div className=\"dropdown-menu\">\r\n                <Link \r\n                  to=\"/profile\" \r\n                  className=\"dropdown-item\"\r\n                  onClick={() => setShowDropdown(false)}\r\n                >\r\n                  Profile\r\n                </Link>\r\n                <button \r\n                  className=\"dropdown-item\"\r\n                  onClick={handleLogout}\r\n                  disabled={isLoggingOut}\r\n                >\r\n                  {isLoggingOut ? (\r\n                    <span className=\"loading-text\">\r\n                      <span className=\"loading-dots\"></span>\r\n                      Logging out\r\n                    </span>\r\n                  ) : (\r\n                    'Logout'\r\n                  )}\r\n                </button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"nav-tabs\">\r\n        {navItems.map((item, index) => (\r\n          <React.Fragment key={index}>\r\n            <Link \r\n              to={item.path} \r\n              className={`nav-tab ${location.pathname === item.path ? 'active' : ''}`}\r\n            >\r\n              {item.name}\r\n            </Link>\r\n            {index < navItems.length - 1 && <span className=\"nav-separator\">•</span>}\r\n          </React.Fragment>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Navigation; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B;EACA,MAAM,CAACO,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAMe,WAAW,GAAGd,MAAM,CAAC,IAAI,CAAC;EAEhC,MAAMe,QAAQ,GAAG,CACf;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAa,CAAC,EACxC;IAAED,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAe,CAAC,EACtC;IAAED,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAgB,CAAC,EAC/C;IAAED,IAAI,EAAE,eAAe;IAAEC,IAAI,EAAE;EAAW;EAC1C;EAAA,CACD;EAED,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFL,eAAe,CAAC,IAAI,CAAC;MACrB,MAAM;QAAEM;MAAM,CAAC,GAAG,MAAMf,QAAQ,CAACgB,IAAI,CAACC,OAAO,CAAC,CAAC;MAC/C,IAAIF,KAAK,EAAE,MAAMA,KAAK;;MAEtB;MACAG,MAAM,CAACb,QAAQ,CAACc,IAAI,GAAG,QAAQ;IAEjC,CAAC,CAAC,OAAOJ,KAAK,EAAE;MAAA,IAAAK,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACP,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAACQ,OAAO,CAAC;MAElD,IAAIC,YAAY,GAAG,sCAAsC;MACzD,IAAIT,KAAK,aAALA,KAAK,gBAAAK,eAAA,GAALL,KAAK,CAAEU,QAAQ,cAAAL,eAAA,gBAAAC,oBAAA,GAAfD,eAAA,CAAiBM,IAAI,cAAAL,oBAAA,eAArBA,oBAAA,CAAuBM,MAAM,EAAE;QAAA,IAAAC,gBAAA,EAAAC,qBAAA;QACjCL,YAAY,GAAGT,KAAK,aAALA,KAAK,wBAAAa,gBAAA,GAALb,KAAK,CAAEU,QAAQ,cAAAG,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBF,IAAI,cAAAG,qBAAA,uBAArBA,qBAAA,CAAuBF,MAAM;MAC9C;MAEAlB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACAZ,SAAS,CAAC,MAAM;IACd,MAAMiC,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIrB,WAAW,CAACsB,OAAO,IAAI,CAACtB,WAAW,CAACsB,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACtE3B,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC;IAED4B,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAMK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;EAC5E,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE5B,OAAA;IAAKoC,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBACnCrC,OAAA;MAAKoC,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBrC,OAAA;QAAKoC,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBrC,OAAA,CAACJ,IAAI;UAAC0C,EAAE,EAAC,YAAY;UAAAD,QAAA,eACrBrC,OAAA;YAAKuC,KAAK,EAAE;cAACC,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,GAAG,EAAE;YAAM,CAAE;YAAAL,QAAA,gBAE/DrC,OAAA;cACE2C,GAAG,EAAC,eAAe;cACnBC,GAAG,EAAC,WAAW;cACfR,SAAS,EAAC;YAAY;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACJhD,OAAA;cAAMuC,KAAK,EAAE;gBAACU,KAAK,EAAC;cAAM,CAAE;cAAAZ,QAAA,EAAC;YAAC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrChD,OAAA;cAAK2C,GAAG,EAAC,gBAAgB;cAAEP,SAAS,EAAC,YAAY;cAACG,KAAK,EAAE;gBAACW,KAAK,EAAE,OAAO;gBAAEC,MAAM,EAAE,MAAM;gBAAEC,SAAS,EAAE;cAAK;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNhD,OAAA;QAAKoC,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BrC,OAAA;UAAKoC,SAAS,EAAC,kBAAkB;UAACiB,GAAG,EAAE7C,WAAY;UAAA6B,QAAA,gBACjDrC,OAAA;YACEoC,SAAS,EAAC,gBAAgB;YAC1BkB,OAAO,EAAEA,CAAA,KAAMjD,eAAe,CAAC,CAACD,YAAY,CAAE;YAC9C,cAAW,eAAe;YAAAiC,QAAA,eAE1BrC,OAAA;cACEoC,SAAS,EAAC,eAAe;cACzBc,KAAK,EAAC,IAAI;cACVC,MAAM,EAAC,IAAI;cACXI,OAAO,EAAC,WAAW;cACnBC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAC,4BAA4B;cAAApB,QAAA,gBAElCrC,OAAA;gBACE0D,CAAC,EAAC,mHAAmH;gBACrHC,MAAM,EAAC,cAAc;gBACrBC,WAAW,EAAC,GAAG;gBACfC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC;cAAO;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACFhD,OAAA;gBACE0D,CAAC,EAAC,wjHAAwjH;gBAC1jHC,MAAM,EAAC,cAAc;gBACrBC,WAAW,EAAC,GAAG;gBACfC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC;cAAO;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,EAER5C,YAAY,iBACXJ,OAAA;YAAKoC,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BrC,OAAA,CAACJ,IAAI;cACH0C,EAAE,EAAC,UAAU;cACbF,SAAS,EAAC,eAAe;cACzBkB,OAAO,EAAEA,CAAA,KAAMjD,eAAe,CAAC,KAAK,CAAE;cAAAgC,QAAA,EACvC;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPhD,OAAA;cACEoC,SAAS,EAAC,eAAe;cACzBkB,OAAO,EAAE1C,YAAa;cACtBmD,QAAQ,EAAEzD,YAAa;cAAA+B,QAAA,EAEtB/B,YAAY,gBACXN,OAAA;gBAAMoC,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC5BrC,OAAA;kBAAMoC,SAAS,EAAC;gBAAc;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAExC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,GAEP;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNhD,OAAA;MAAKoC,SAAS,EAAC,UAAU;MAAAC,QAAA,EACtB5B,QAAQ,CAACuD,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACxBlE,OAAA,CAACR,KAAK,CAAC2E,QAAQ;QAAA9B,QAAA,gBACbrC,OAAA,CAACJ,IAAI;UACH0C,EAAE,EAAE2B,IAAI,CAACtD,IAAK;UACdyB,SAAS,EAAE,WAAWjC,QAAQ,CAACiE,QAAQ,KAAKH,IAAI,CAACtD,IAAI,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAA0B,QAAA,EAEvE4B,IAAI,CAACvD;QAAI;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACNkB,KAAK,GAAGzD,QAAQ,CAAC4D,MAAM,GAAG,CAAC,iBAAIrE,OAAA;UAAMoC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,GAPrDkB,KAAK;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQV,CACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9C,EAAA,CA5IID,UAAU;EAAA,QACGJ,WAAW;AAAA;AAAAyE,EAAA,GADxBrE,UAAU;AA8IhB,eAAeA,UAAU;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}