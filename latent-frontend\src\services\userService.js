import apiService from './api';
import API_URLS from '../config/apiUrls';

const userService = {
  // Get user profile
  getUserProfile: async () => {
    return await apiService.get(API_URLS.USER.PROFILE);
  },

  // Update user profile
  updateUserProfile: async (userData, userId) => {
    try {
      return await apiService.patch(API_URLS.USER.UPDATE_USER(userId), userData);
    } catch (error) {
      console.error('Error updating user profile:', error);
      
      let errorMessage = 'Failed to update user profile.';
      if (error?.response?.data?.detail) {
        errorMessage = error?.response?.data?.detail;
      }
      
      const errorToThrow = new Error(errorMessage);
      errorToThrow.response = error.response;
      throw errorToThrow;
    }
  },

  // Get user recent activity
  getRecentActivity: async () => {
    try {
      return await apiService.get(API_URLS.USER.RECENT_ACTIVITY);
    } catch (error) {
      console.error('Error fetching recent activity:', error);
      
      let errorMessage = 'Failed to fetch recent activity.';
      if (error?.response?.data?.detail) {
        errorMessage = error?.response?.data?.detail;
      }
      
      const errorToThrow = new Error(errorMessage);
      errorToThrow.response = error.response;
      throw errorToThrow;
    }
  },

  // Other user-related API methods can be added here
};

export default userService; 