{"ast": null, "code": "// API URL configuration\nconst BASE_URL = process.env.REACT_APP_API_URL;\nconst API_URLS = {\n  // User endpoints\n  USER: {\n    PROFILE: `${BASE_URL}/user/profile`,\n    UPDATE_PROFILE: `${BASE_URL}/user/update`,\n    UPDATE_USER: userId => `${BASE_URL}/user/users/${userId}`,\n    RECENT_ACTIVITY: `${BASE_URL}/user/recent-activity`,\n    CHECK_USER_EXISTS: email => `${BASE_URL}/user/check-status/${email}`\n  },\n  // Organization endpoints\n  ORGANIZATION: {\n    DETAILS: `${BASE_URL}/organization/details`\n  },\n  // SOP endpoints\n  SOP: {\n    LIST: `${BASE_URL}/sops/`,\n    UPLOAD: `${BASE_URL}/sops/upload`,\n    DELETE: `${BASE_URL}/sops`,\n    DOWNLOAD: `${BASE_URL}/sops/download`\n  },\n  // Regulations endpoints\n  REGULATIONS: {\n    LIST: `${BASE_URL}/regulations/`,\n    ORGANIZATION: `${BASE_URL}/regulations/organization`\n  },\n  // Analysis endpoints\n  ANALYSIS: {\n    RESULTS: `${BASE_URL}/analysis/results`,\n    ANALYZE: `${BASE_URL}/analysis/analyze`\n  },\n  // Other API endpoints can be added here\n  SOPS: {\n    GET_ALL: '/sops',\n    GET: '/sops',\n    CREATE: '/sops',\n    UPDATE: '/sops',\n    DELETE: '/sops',\n    DOWNLOAD: '/sops/download'\n  },\n  ANALYSIS_RESULTS: {\n    UPDATE_META: `${BASE_URL}/analysis/results`\n  },\n  DEPARTMENTS: {\n    LIST: `${BASE_URL}/sops/departments`\n  },\n  CHAT: {\n    QUERY: `${BASE_URL}/chat/query`\n  }\n};\nexport default API_URLS;", "map": {"version": 3, "names": ["BASE_URL", "process", "env", "REACT_APP_API_URL", "API_URLS", "USER", "PROFILE", "UPDATE_PROFILE", "UPDATE_USER", "userId", "RECENT_ACTIVITY", "CHECK_USER_EXISTS", "email", "ORGANIZATION", "DETAILS", "SOP", "LIST", "UPLOAD", "DELETE", "DOWNLOAD", "REGULATIONS", "ANALYSIS", "RESULTS", "ANALYZE", "SOPS", "GET_ALL", "GET", "CREATE", "UPDATE", "ANALYSIS_RESULTS", "UPDATE_META", "DEPARTMENTS", "CHAT", "QUERY"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/config/apiUrls.js"], "sourcesContent": ["// API URL configuration\r\nconst BASE_URL = process.env.REACT_APP_API_URL\r\n\r\nconst API_URLS = {\r\n  // User endpoints\r\n  USER: {\r\n    PROFILE: `${BASE_URL}/user/profile`,\r\n    UPDATE_PROFILE: `${BASE_URL}/user/update`,\r\n    UPDATE_USER: (userId) => `${BASE_URL}/user/users/${userId}`,\r\n    RECENT_ACTIVITY: `${BASE_URL}/user/recent-activity`,\r\n    CHECK_USER_EXISTS: (email) => `${BASE_URL}/user/check-status/${email}`,\r\n  },\r\n  // Organization endpoints\r\n  ORGANIZATION: {\r\n    DETAILS: `${BASE_URL}/organization/details`,\r\n  },\r\n  // SOP endpoints\r\n  SOP: {\r\n    LIST: `${BASE_URL}/sops/`,\r\n    UPLOAD: `${BASE_URL}/sops/upload`,\r\n    DELETE: `${BASE_URL}/sops`,\r\n    DOWNLOAD: `${BASE_URL}/sops/download`,\r\n  },\r\n  // Regulations endpoints\r\n  REGULATIONS: {\r\n    LIST: `${BASE_URL}/regulations/`,\r\n    ORGANIZATION: `${BASE_URL}/regulations/organization`,\r\n  },\r\n  // Analysis endpoints\r\n  ANALYSIS: {\r\n    RESULTS: `${BASE_URL}/analysis/results`,\r\n    ANALYZE: `${BASE_URL}/analysis/analyze`\r\n  },\r\n  // Other API endpoints can be added here\r\n  SOPS: {\r\n    GET_ALL: '/sops',\r\n    GET: '/sops',\r\n    CREATE: '/sops',\r\n    UPDATE: '/sops',\r\n    DELETE: '/sops',\r\n    DOWNLOAD: '/sops/download'\r\n  },\r\n\r\n  ANALYSIS_RESULTS: {\r\n    UPDATE_META:`${BASE_URL}/analysis/results`\r\n  },\r\n\r\n  DEPARTMENTS: {\r\n    LIST: `${BASE_URL}/sops/departments`\r\n  },\r\n    CHAT: {\r\n    QUERY: `${BASE_URL}/chat/query`\r\n  },\r\n\r\n};\r\n\r\nexport default API_URLS; "], "mappings": "AAAA;AACA,MAAMA,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE9C,MAAMC,QAAQ,GAAG;EACf;EACAC,IAAI,EAAE;IACJC,OAAO,EAAE,GAAGN,QAAQ,eAAe;IACnCO,cAAc,EAAE,GAAGP,QAAQ,cAAc;IACzCQ,WAAW,EAAGC,MAAM,IAAK,GAAGT,QAAQ,eAAeS,MAAM,EAAE;IAC3DC,eAAe,EAAE,GAAGV,QAAQ,uBAAuB;IACnDW,iBAAiB,EAAGC,KAAK,IAAK,GAAGZ,QAAQ,sBAAsBY,KAAK;EACtE,CAAC;EACD;EACAC,YAAY,EAAE;IACZC,OAAO,EAAE,GAAGd,QAAQ;EACtB,CAAC;EACD;EACAe,GAAG,EAAE;IACHC,IAAI,EAAE,GAAGhB,QAAQ,QAAQ;IACzBiB,MAAM,EAAE,GAAGjB,QAAQ,cAAc;IACjCkB,MAAM,EAAE,GAAGlB,QAAQ,OAAO;IAC1BmB,QAAQ,EAAE,GAAGnB,QAAQ;EACvB,CAAC;EACD;EACAoB,WAAW,EAAE;IACXJ,IAAI,EAAE,GAAGhB,QAAQ,eAAe;IAChCa,YAAY,EAAE,GAAGb,QAAQ;EAC3B,CAAC;EACD;EACAqB,QAAQ,EAAE;IACRC,OAAO,EAAE,GAAGtB,QAAQ,mBAAmB;IACvCuB,OAAO,EAAE,GAAGvB,QAAQ;EACtB,CAAC;EACD;EACAwB,IAAI,EAAE;IACJC,OAAO,EAAE,OAAO;IAChBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE,OAAO;IACfV,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE;EACZ,CAAC;EAEDU,gBAAgB,EAAE;IAChBC,WAAW,EAAC,GAAG9B,QAAQ;EACzB,CAAC;EAED+B,WAAW,EAAE;IACXf,IAAI,EAAE,GAAGhB,QAAQ;EACnB,CAAC;EACCgC,IAAI,EAAE;IACNC,KAAK,EAAE,GAAGjC,QAAQ;EACpB;AAEF,CAAC;AAED,eAAeI,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}