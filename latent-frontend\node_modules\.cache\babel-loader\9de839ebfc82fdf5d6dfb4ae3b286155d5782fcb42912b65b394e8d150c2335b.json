{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\zipp\\\\latent-frontend\\\\src\\\\components\\\\pages\\\\GapAnalysis\\\\GapDetailsSidebar.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './GapDetailsSidebar.css';\nimport LoadingSpinner from '../../common/LoadingSpinner/LoadingSpinner';\nimport apiService from '../../../services/api';\nimport { toast } from 'react-toastify';\nimport API_URLS from '../../../config/apiUrls';\nimport { sanitizeHtml, sanitizeText } from '../../../utils/sanitize';\nimport { formatDate } from '../../../utils/dateUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst GapDetailsSidebar = ({\n  isOpen,\n  onClose,\n  gapDetails,\n  analysisResults,\n  loading,\n  error,\n  onRefreshData,\n  onSilentUpdate\n}) => {\n  _s();\n  var _result$regulations;\n  // Move all hooks to the top, before any conditional returns\n  const [editableGaps, setEditableGaps] = useState([]);\n  const [isEditing, setIsEditing] = useState(false);\n  const [hasChanges, setHasChanges] = useState(false);\n  const [verifyLoading, setVerifyLoading] = useState(false);\n  const originalGapsRef = useRef([]);\n  const [wasValidated, setWasValidated] = useState(false);\n  const [expandedGapId, setExpandedGapId] = useState(null);\n\n  // Get the result for the current SOP using its ID\n  const result = analysisResults && gapDetails ?\n  // If analysisResults is an array, find the result with matching SOP ID\n  Array.isArray(analysisResults.results) ? analysisResults.results.find(r => r.sop_id === gapDetails.id) :\n  // Otherwise, use the analysisResults directly if it's for the current SOP\n  analysisResults.sop_id === gapDetails.id ? analysisResults : null : null;\n\n  // Initialize editable gaps when analysis results change\n  useEffect(() => {\n    if (isOpen && result && result.gap_details && result.gap_details.length > 0) {\n      // Check if validated gap details exist in metadata\n      const hasValidatedGaps = result.metadata && result.metadata.changed_gap_detail;\n\n      // Set the wasValidated state based on whether gaps were previously validated\n      setWasValidated(hasValidatedGaps);\n      // Use validated gaps if available, otherwise use original gap_details\n      const gapDetailsToUse = hasValidatedGaps ? result.metadata.changed_gap_detail : result.gap_details;\n\n      // Add unique IDs to each gap for easier manipulation\n      const gapsWithIds = gapDetailsToUse.map((gap, index) => ({\n        ...gap,\n        id: `gap-${index}-${Date.now()}`\n      }));\n      setEditableGaps(gapsWithIds);\n      originalGapsRef.current = JSON.parse(JSON.stringify(gapsWithIds));\n      setHasChanges(false);\n    } else {\n      setEditableGaps([]);\n      originalGapsRef.current = [];\n      setHasChanges(false);\n    }\n\n    // Reset editing state when opening/closing the sidebar\n    setIsEditing(false);\n  }, [isOpen, result]);\n\n  // Prevent body scroll when modal is open\n  useEffect(() => {\n    if (isOpen) {\n      // Save current scroll position and prevent scrolling\n      document.body.style.overflow = 'hidden';\n    } else {\n      // Restore scrolling when modal is closed\n      document.body.style.overflow = 'unset';\n    }\n\n    // Cleanup function to restore scrolling if component unmounts while modal is open\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  // Check for changes whenever editableGaps changes\n  useEffect(() => {\n    if (isEditing && originalGapsRef.current.length > 0) {\n      const currentGapsString = JSON.stringify(editableGaps);\n      const originalGapsString = JSON.stringify(originalGapsRef.current);\n      setHasChanges(currentGapsString !== originalGapsString);\n    } else if (isEditing) {\n      // When entering edit mode for the first time, ensure hasChanges is false\n      setHasChanges(false);\n    }\n  }, [editableGaps, isEditing]);\n\n  // Reset hasChanges when entering edit mode\n  useEffect(() => {\n    if (isEditing) {\n      setHasChanges(false);\n    }\n  }, [isEditing]);\n\n  // Handle cell edit\n  const handleCellEdit = (id, field, value) => {\n    setEditableGaps(gaps => gaps.map(gap => gap.id === id ? {\n      ...gap,\n      [field]: value\n    } : gap));\n  };\n\n  // Add new row\n  const handleAddRow = () => {\n    const newGap = {\n      id: `gap-new-${Date.now()}`,\n      gap_description: '',\n      suggestion: '',\n      priority: 'medium',\n      isNew: true // Flag to identify newly added gaps\n    };\n    setEditableGaps(prev => [...prev, newGap]);\n    setHasChanges(true);\n\n    // Automatically expand the newly added gap\n    setExpandedGapId(newGap.id);\n  };\n\n  // Delete row\n  const handleDeleteRow = id => {\n    setEditableGaps(gaps => gaps.filter(gap => gap.id !== id));\n  };\n\n  // Verify changes\n  const handleVerify = async () => {\n    if (!result || verifyLoading) return;\n    try {\n      setVerifyLoading(true);\n\n      // Prepare the gaps data without the temporary IDs\n      const gapsToSubmit = editableGaps.map(({\n        id,\n        ...rest\n      }) => rest);\n\n      // Create the request payload with the SOP ID\n      const payload = {\n        sop_id: gapDetails.id,\n        changed_gap_detail: gapsToSubmit\n      };\n\n      // Make the API call to verify/save changes\n      await apiService.patch(`${API_URLS.ANALYSIS.RESULTS}/${result.id}`, payload);\n\n      // Update the state to reflect the changes are now validated\n      setWasValidated(true);\n      setHasChanges(false);\n      originalGapsRef.current = JSON.parse(JSON.stringify(editableGaps));\n\n      // Create unique toast ID to prevent conflicts\n      const toastId = `gap-verify-${Date.now()}`;\n\n      // Show success toast immediately with unique ID\n      if (wasValidated) {\n        toast.success('Gap details have been successfully saved.', {\n          toastId: toastId,\n          position: \"top-right\",\n          autoClose: 4000,\n          hideProgressBar: false,\n          closeOnClick: true,\n          pauseOnHover: true,\n          draggable: true\n        });\n      } else {\n        toast.success('Gap details have been successfully verified and saved.', {\n          toastId: toastId,\n          position: \"top-right\",\n          autoClose: 4000,\n          hideProgressBar: false,\n          closeOnClick: true,\n          pauseOnHover: true,\n          draggable: true\n        });\n      }\n\n      // Call the silent update function to update data without reload\n      if (typeof onSilentUpdate === 'function') {\n        console.log('Silently updating gap analysis data after verification...');\n        await onSilentUpdate(gapDetails.id);\n      }\n\n      // Update the local analysisResults to reflect the saved changes immediately\n      // This ensures the sidebar shows the updated data without needing to close/reopen\n      if (analysisResults) {\n        const updatedAnalysisResults = {\n          ...analysisResults,\n          metadata: {\n            ...analysisResults.metadata,\n            changed_gap_detail: gapsToSubmit\n          }\n        };\n\n        // If there's a way to update the parent's analysisResults, we could call a callback here\n        // For now, the silent update should handle this\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error verifying gap details:', error);\n      let errorMessage = 'Failed to verify gap details. Please try again.';\n      if (error !== null && error !== void 0 && (_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.detail) {\n        var _error$response2, _error$response2$data;\n        errorMessage = error === null || error === void 0 ? void 0 : (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail;\n      }\n      const errorToastId = `gap-error-${Date.now()}`;\n      toast.error(errorMessage, {\n        toastId: errorToastId,\n        position: \"top-right\",\n        autoClose: 4000,\n        hideProgressBar: false,\n        closeOnClick: true,\n        pauseOnHover: true,\n        draggable: true\n      });\n    } finally {\n      setVerifyLoading(false);\n    }\n  };\n\n  // Add this useEffect after your other useEffects\n  useEffect(() => {\n    if (isEditing) {\n      // Function to adjust textarea height\n      const adjustTextareaHeight = () => {\n        const textareas = document.querySelectorAll('.auto-resize-textarea');\n        textareas.forEach(textarea => {\n          textarea.style.height = 'auto';\n          textarea.style.height = textarea.scrollHeight + 'px';\n        });\n      };\n\n      // Adjust heights initially\n      adjustTextareaHeight();\n\n      // Add event listeners to all textareas\n      const textareas = document.querySelectorAll('.auto-resize-textarea');\n      textareas.forEach(textarea => {\n        textarea.addEventListener('input', adjustTextareaHeight);\n      });\n\n      // Clean up event listeners\n      return () => {\n        textareas.forEach(textarea => {\n          textarea.removeEventListener('input', adjustTextareaHeight);\n        });\n      };\n    }\n  }, [isEditing, editableGaps, expandedGapId]); // Re-run when editing mode, gaps, or expanded state changes\n\n  // Update the toggle function to implement accordion behavior\n  const toggleGapExpansion = gapId => {\n    setExpandedGapId(expandedGapId === gapId ? null : gapId);\n  };\n\n  // Update the renderGaps function to use arrow icons\n  const renderGaps = () => {\n    if (!editableGaps || editableGaps.length === 0) {\n      return /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"no-gaps\",\n        children: \"No gaps identified for this SOP.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 14\n      }, this);\n    }\n\n    // Helper function to truncate text for preview\n    const truncateText = (text, maxLength = 60) => {\n      if (!text) return 'No critique provided';\n      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\n    };\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"gaps-paragraph-container\",\n      children: [editableGaps.map((gap, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gap-paragraph-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"gap-paragraph-header\",\n          onClick: () => toggleGapExpansion(gap.id),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"gap-header-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"gap-expand-icon\",\n              children: expandedGapId === gap.id ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"16\",\n                height: \"16\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M6 9L12 15L18 9\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"16\",\n                height: \"16\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M9 6L15 12L9 18\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              children: [\"Gap #\", index + 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"gap-header-preview\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"gap-critique-label\",\n              children: \"Critique - \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"gap-critique-preview\",\n              children: truncateText(gap.critique)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `gap-severity ${(gap.priority || 'MEDIUM').toLowerCase()}`,\n            children: gap.priority\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this), expandedGapId === gap.id && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"gap-paragraph-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"gap-field\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"gap-field-label\",\n              children: \"Critique:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 19\n            }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"textarea\", {\n              className: \"auto-resize-textarea\",\n              value: gap.critique || '',\n              onChange: e => handleCellEdit(gap.id, 'critique', e.target.value),\n              placeholder: \"Enter critique\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"gap-field-value\",\n              children: gap.critique || 'No critique provided'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"gap-field\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"gap-field-label\",\n              children: \"Suggestions:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 19\n            }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"textarea\", {\n              className: \"auto-resize-textarea\",\n              value: gap.missing_step || '',\n              onChange: e => handleCellEdit(gap.id, 'missing_step', e.target.value),\n              placeholder: \"Enter missing step\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"gap-field-value\",\n              children: gap.missing_step || 'No missing step provided'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"gap-field\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"gap-field-label\",\n              children: \"Guidelines Reference:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 19\n            }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"textarea\", {\n              className: \"auto-resize-textarea\",\n              value: gap.guidelines_reference || '',\n              onChange: e => handleCellEdit(gap.id, 'guidelines_reference', e.target.value),\n              placeholder: \"Enter guidelines reference\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"gap-field-value\",\n              children: gap.guidelines_reference || 'No guidelines reference provided'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"gap-field\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"gap-field-label\",\n              children: \"SOP Reference:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 19\n            }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"textarea\", {\n              className: \"auto-resize-textarea\",\n              value: gap.sop_reference || '',\n              onChange: e => handleCellEdit(gap.id, 'sop_reference', e.target.value),\n              placeholder: \"Enter SOP reference\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"gap-field-value\",\n              children: gap.sop_reference || 'No SOP reference provided'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 17\n          }, this), isEditing && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"gap-field\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"gap-field-label\",\n              children: \"Severity:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"severity-select\",\n              value: gap.priority,\n              onChange: e => handleCellEdit(gap.id, 'priority', e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Low\",\n                children: \"Low\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Medium\",\n                children: \"Medium\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"High\",\n                children: \"High\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 19\n          }, this), isEditing && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"gap-paragraph-actions\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"delete-row-btn\",\n              onClick: e => {\n                e.stopPropagation(); // Prevent triggering the header click\n                handleDeleteRow(gap.id);\n              },\n              title: \"Delete this gap\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                width: \"16\",\n                height: \"16\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M3 6h18\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 23\n              }, this), \"Delete Gap\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 15\n        }, this)]\n      }, gap.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 11\n      }, this)), isEditing && /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"add-gap-btn\",\n        onClick: handleAddRow,\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          xmlns: \"http://www.w3.org/2000/svg\",\n          width: \"16\",\n          height: \"16\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          strokeWidth: \"2\",\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          children: [/*#__PURE__*/_jsxDEV(\"line\", {\n            x1: \"12\",\n            y1: \"5\",\n            x2: \"12\",\n            y2: \"19\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n            x1: \"5\",\n            y1: \"12\",\n            x2: \"19\",\n            y2: \"12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 13\n        }, this), \"Add Gap\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Return null if not open, but only after all hooks have been called\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-overlay\",\n    onClick: onClose,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"gap-details-modal\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Gaps & Suggestions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-actions\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"close-btn\",\n            onClick: onClose,\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-loading\",\n        children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"loading-text\",\n          children: \"Loading analysis results...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 11\n      }, this) : !result ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-no-data\",\n        children: \"No analysis results available.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sidebar-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"parent-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"section\", {\n              className: \"info-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"SOP Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: sanitizeText(gapDetails === null || gapDetails === void 0 ? void 0 : gapDetails.title) || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Department\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: sanitizeText(gapDetails === null || gapDetails === void 0 ? void 0 : gapDetails.department) || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Compliance Score\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"compliance-score\",\n                  children: [sanitizeText(result.compliance_score), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: !wasValidated ? 'Analyzed on' : 'Updated on'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: formatDate(sanitizeText(!wasValidated ? result.analyzed_at : result.updated_at))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n              className: \"info-section\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-row\",\n                style: {\n                  width: 'auto'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Regulation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"regulations-container\",\n                  children: (result === null || result === void 0 ? void 0 : (_result$regulations = result.regulations) === null || _result$regulations === void 0 ? void 0 : _result$regulations.length) > 0 ? result.regulations.map((regu, key) => /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"regulations-name\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: sanitizeText(regu === null || regu === void 0 ? void 0 : regu.name)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 480,\n                      columnNumber: 23\n                    }, this)\n                  }, key, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 21\n                  }, this)) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"N/A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 482,\n                    columnNumber: 24\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n            className: \"gaps-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Identified Gaps\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"section-actions\",\n                children: [isEditing && /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"add-row-btn\",\n                  onClick: handleAddRow,\n                  children: \"+ Add Gap\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 23\n                }, this), !loading && result && /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `edit-button ${isEditing ? 'active' : ''}`,\n                  onClick: () => setIsEditing(!isEditing),\n                  children: isEditing ? 'Cancel Editing' : 'Edit Gaps'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"gaps-container\",\n              children: renderGaps()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sidebar-footer\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-cancel\",\n            onClick: onClose,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `verify-btn ${wasValidated ? hasChanges ? '' : 'disabled' : 'verify-btn-orange'}`,\n            onClick: handleVerify,\n            disabled: verifyLoading || wasValidated && !hasChanges,\n            children: verifyLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: wasValidated ? 'Saving...' : 'Verifying...'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true) : wasValidated ? 'Save Changes' : 'Verify Changes'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 431,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 430,\n    columnNumber: 5\n  }, this);\n};\n_s(GapDetailsSidebar, \"pf5HGpV0ze6VsOcw36MoPgf2iC8=\");\n_c = GapDetailsSidebar;\nexport default GapDetailsSidebar;\nvar _c;\n$RefreshReg$(_c, \"GapDetailsSidebar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "LoadingSpinner", "apiService", "toast", "API_URLS", "sanitizeHtml", "sanitizeText", "formatDate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "GapDetailsSidebar", "isOpen", "onClose", "gapDetails", "analysisResults", "loading", "error", "onRefreshData", "onSilentUpdate", "_s", "_result$regulations", "editableGaps", "setEditableGaps", "isEditing", "setIsEditing", "has<PERSON><PERSON><PERSON>", "set<PERSON>as<PERSON><PERSON><PERSON>", "verifyLoading", "setVerifyLoading", "originalGapsRef", "wasValidated", "setWasValidated", "expandedGapId", "setExpandedGapId", "result", "Array", "isArray", "results", "find", "r", "sop_id", "id", "gap_details", "length", "hasValidatedGaps", "metadata", "changed_gap_detail", "gapDetailsToUse", "gapsWithIds", "map", "gap", "index", "Date", "now", "current", "JSON", "parse", "stringify", "document", "body", "style", "overflow", "currentGapsString", "originalGapsString", "handleCellEdit", "field", "value", "gaps", "handleAddRow", "newGap", "gap_description", "suggestion", "priority", "isNew", "prev", "handleDeleteRow", "filter", "handleVerify", "gapsToSubmit", "rest", "payload", "patch", "ANALYSIS", "RESULTS", "toastId", "success", "position", "autoClose", "hideProgressBar", "closeOnClick", "pauseOnHover", "draggable", "console", "log", "updatedAnalysisResults", "_error$response", "_error$response$data", "errorMessage", "response", "data", "detail", "_error$response2", "_error$response2$data", "errorToastId", "adjustTextareaHeight", "textareas", "querySelectorAll", "for<PERSON>ach", "textarea", "height", "scrollHeight", "addEventListener", "removeEventListener", "toggleGapExpansion", "gapId", "renderGaps", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "truncateText", "text", "max<PERSON><PERSON><PERSON>", "substring", "onClick", "width", "viewBox", "fill", "xmlns", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "critique", "toLowerCase", "onChange", "e", "target", "placeholder", "missing_step", "guidelines_reference", "sop_reference", "stopPropagation", "title", "x1", "y1", "x2", "y2", "size", "department", "compliance_score", "analyzed_at", "updated_at", "regulations", "regu", "key", "name", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/components/pages/GapAnalysis/GapDetailsSidebar.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport './GapDetailsSidebar.css';\r\nimport LoadingSpinner from '../../common/LoadingSpinner/LoadingSpinner';\r\nimport apiService from '../../../services/api';\r\nimport { toast } from 'react-toastify';\r\nimport API_URLS from '../../../config/apiUrls';\r\nimport { sanitizeHtml, sanitizeText } from '../../../utils/sanitize';\r\nimport { formatDate } from '../../../utils/dateUtils';\r\n\r\nconst GapDetailsSidebar = ({ isOpen, onClose, gapDetails, analysisResults, loading, error, onRefreshData, onSilentUpdate }) => {\r\n  // Move all hooks to the top, before any conditional returns\r\n  const [editableGaps, setEditableGaps] = useState([]);\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [hasChanges, setHasChanges] = useState(false);\r\n  const [verifyLoading, setVerifyLoading] = useState(false);\r\n  const originalGapsRef = useRef([]);\r\n  const [wasValidated, setWasValidated] = useState(false);\r\n  const [expandedGapId, setExpandedGapId] = useState(null);\r\n  \r\n  // Get the result for the current SOP using its ID\r\n  const result = analysisResults && gapDetails ? \r\n    // If analysisResults is an array, find the result with matching SOP ID\r\n    Array.isArray(analysisResults.results) ? \r\n      analysisResults.results.find(r => r.sop_id === gapDetails.id) : \r\n      // Otherwise, use the analysisResults directly if it's for the current SOP\r\n      (analysisResults.sop_id === gapDetails.id ? analysisResults : null)\r\n    : null;\r\n  \r\n  // Initialize editable gaps when analysis results change\r\n  useEffect(() => {\r\n    if (isOpen && result && result.gap_details && result.gap_details.length > 0) {\r\n      // Check if validated gap details exist in metadata\r\n      const hasValidatedGaps = \r\n        result.metadata && \r\n        result.metadata.changed_gap_detail;\r\n      \r\n      // Set the wasValidated state based on whether gaps were previously validated\r\n      setWasValidated(hasValidatedGaps);\r\n      // Use validated gaps if available, otherwise use original gap_details\r\n      const gapDetailsToUse = hasValidatedGaps \r\n        ? result.metadata.changed_gap_detail \r\n        : result.gap_details;\r\n      \r\n      // Add unique IDs to each gap for easier manipulation\r\n      const gapsWithIds = gapDetailsToUse.map((gap, index) => ({\r\n        ...gap,\r\n        id: `gap-${index}-${Date.now()}`\r\n      }));\r\n      \r\n      setEditableGaps(gapsWithIds);\r\n      originalGapsRef.current = JSON.parse(JSON.stringify(gapsWithIds));\r\n      setHasChanges(false);\r\n    } else {\r\n      setEditableGaps([]);\r\n      originalGapsRef.current = [];\r\n      setHasChanges(false);\r\n    }\r\n    \r\n    // Reset editing state when opening/closing the sidebar\r\n    setIsEditing(false);\r\n  }, [isOpen, result]);\r\n\r\n  // Prevent body scroll when modal is open\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      // Save current scroll position and prevent scrolling\r\n      document.body.style.overflow = 'hidden';\r\n    } else {\r\n      // Restore scrolling when modal is closed\r\n      document.body.style.overflow = 'unset';\r\n    }\r\n\r\n    // Cleanup function to restore scrolling if component unmounts while modal is open\r\n    return () => {\r\n      document.body.style.overflow = 'unset';\r\n    };\r\n  }, [isOpen]);\r\n\r\n  // Check for changes whenever editableGaps changes\r\n  useEffect(() => {\r\n    if (isEditing && originalGapsRef.current.length > 0) {\r\n      const currentGapsString = JSON.stringify(editableGaps);\r\n      const originalGapsString = JSON.stringify(originalGapsRef.current);\r\n      setHasChanges(currentGapsString !== originalGapsString);\r\n    } else if (isEditing) {\r\n      // When entering edit mode for the first time, ensure hasChanges is false\r\n      setHasChanges(false);\r\n    }\r\n  }, [editableGaps, isEditing]);\r\n\r\n  // Reset hasChanges when entering edit mode\r\n  useEffect(() => {\r\n    if (isEditing) {\r\n      setHasChanges(false);\r\n    }\r\n  }, [isEditing]);\r\n\r\n  // Handle cell edit\r\n  const handleCellEdit = (id, field, value) => {\r\n    setEditableGaps(gaps => \r\n      gaps.map(gap => \r\n        gap.id === id ? { ...gap, [field]: value } : gap\r\n      )\r\n    );\r\n  };\r\n  \r\n  // Add new row\r\n  const handleAddRow = () => {\r\n    const newGap = {\r\n      id: `gap-new-${Date.now()}`,\r\n      gap_description: '',\r\n      suggestion: '',\r\n      priority: 'medium',\r\n      isNew: true // Flag to identify newly added gaps\r\n    };\r\n    \r\n    setEditableGaps(prev => [...prev, newGap]);\r\n    setHasChanges(true);\r\n    \r\n    // Automatically expand the newly added gap\r\n    setExpandedGapId(newGap.id);\r\n  };\r\n  \r\n  // Delete row\r\n  const handleDeleteRow = (id) => {\r\n    setEditableGaps(gaps => gaps.filter(gap => gap.id !== id));\r\n  };\r\n  \r\n  // Verify changes\r\n  const handleVerify = async () => {\r\n    if (!result || verifyLoading) return;\r\n    \r\n    try {\r\n      setVerifyLoading(true);\r\n      \r\n      // Prepare the gaps data without the temporary IDs\r\n      const gapsToSubmit = editableGaps.map(({ id, ...rest }) => rest);\r\n      \r\n      // Create the request payload with the SOP ID\r\n      const payload = {\r\n        sop_id: gapDetails.id,\r\n        changed_gap_detail: gapsToSubmit\r\n      };\r\n      \r\n      // Make the API call to verify/save changes\r\n      await apiService.patch(\r\n        `${API_URLS.ANALYSIS.RESULTS}/${result.id}`, \r\n        payload\r\n      );\r\n      \r\n      // Update the state to reflect the changes are now validated\r\n      setWasValidated(true);\r\n      setHasChanges(false);\r\n      originalGapsRef.current = JSON.parse(JSON.stringify(editableGaps));\r\n      \r\n      // Create unique toast ID to prevent conflicts\r\n      const toastId = `gap-verify-${Date.now()}`;\r\n      \r\n      // Show success toast immediately with unique ID\r\n      if(wasValidated){\r\n        toast.success('Gap details have been successfully saved.', {\r\n          toastId: toastId,\r\n          position: \"top-right\",\r\n          autoClose: 4000,\r\n          hideProgressBar: false,\r\n          closeOnClick: true,\r\n          pauseOnHover: true,\r\n          draggable: true,\r\n        });\r\n      } else {\r\n        toast.success('Gap details have been successfully verified and saved.', {\r\n          toastId: toastId,\r\n          position: \"top-right\", \r\n          autoClose: 4000,\r\n          hideProgressBar: false,\r\n          closeOnClick: true,\r\n          pauseOnHover: true,\r\n          draggable: true,\r\n        });\r\n      }\r\n      \r\n      // Call the silent update function to update data without reload\r\n      if (typeof onSilentUpdate === 'function') {\r\n        console.log('Silently updating gap analysis data after verification...');\r\n        await onSilentUpdate(gapDetails.id);\r\n      }\r\n      \r\n      // Update the local analysisResults to reflect the saved changes immediately\r\n      // This ensures the sidebar shows the updated data without needing to close/reopen\r\n      if (analysisResults) {\r\n        const updatedAnalysisResults = {\r\n          ...analysisResults,\r\n          metadata: {\r\n            ...analysisResults.metadata,\r\n            changed_gap_detail: gapsToSubmit\r\n          }\r\n        };\r\n        \r\n        // If there's a way to update the parent's analysisResults, we could call a callback here\r\n        // For now, the silent update should handle this\r\n      }\r\n      \r\n    } catch (error) {\r\n      console.error('Error verifying gap details:', error);\r\n      \r\n      let errorMessage = 'Failed to verify gap details. Please try again.';\r\n      if (error?.response?.data?.detail) {\r\n        errorMessage = error?.response?.data?.detail;\r\n      }\r\n      \r\n      const errorToastId = `gap-error-${Date.now()}`;\r\n      toast.error(errorMessage, {\r\n        toastId: errorToastId,\r\n        position: \"top-right\",\r\n        autoClose: 4000,\r\n        hideProgressBar: false,\r\n        closeOnClick: true,\r\n        pauseOnHover: true,\r\n        draggable: true,\r\n      });\r\n    } finally {\r\n      setVerifyLoading(false);\r\n    }\r\n  };\r\n\r\n  // Add this useEffect after your other useEffects\r\n  useEffect(() => {\r\n    if (isEditing) {\r\n      // Function to adjust textarea height\r\n      const adjustTextareaHeight = () => {\r\n        const textareas = document.querySelectorAll('.auto-resize-textarea');\r\n        textareas.forEach(textarea => {\r\n          textarea.style.height = 'auto';\r\n          textarea.style.height = textarea.scrollHeight + 'px';\r\n        });\r\n      };\r\n      \r\n      // Adjust heights initially\r\n      adjustTextareaHeight();\r\n      \r\n      // Add event listeners to all textareas\r\n      const textareas = document.querySelectorAll('.auto-resize-textarea');\r\n      textareas.forEach(textarea => {\r\n        textarea.addEventListener('input', adjustTextareaHeight);\r\n      });\r\n      \r\n      // Clean up event listeners\r\n      return () => {\r\n        textareas.forEach(textarea => {\r\n          textarea.removeEventListener('input', adjustTextareaHeight);\r\n        });\r\n      };\r\n    }\r\n  }, [isEditing, editableGaps, expandedGapId]); // Re-run when editing mode, gaps, or expanded state changes\r\n\r\n  // Update the toggle function to implement accordion behavior\r\n  const toggleGapExpansion = (gapId) => {\r\n    setExpandedGapId(expandedGapId === gapId ? null : gapId);\r\n  };\r\n\r\n  // Update the renderGaps function to use arrow icons\r\n  const renderGaps = () => {\r\n    if (!editableGaps || editableGaps.length === 0) {\r\n      return <p className=\"no-gaps\">No gaps identified for this SOP.</p>;\r\n    }\r\n\r\n    // Helper function to truncate text for preview\r\n    const truncateText = (text, maxLength = 60) => {\r\n      if (!text) return 'No critique provided';\r\n      return text.length > maxLength \r\n        ? text.substring(0, maxLength) + '...' \r\n        : text;\r\n    };\r\n\r\n    return (\r\n      <div className=\"gaps-paragraph-container\">\r\n        {editableGaps.map((gap, index) => (\r\n          <div key={gap.id} className=\"gap-paragraph-item\">\r\n            <div \r\n              className=\"gap-paragraph-header\"\r\n              onClick={() => toggleGapExpansion(gap.id)}\r\n            >\r\n              <div className=\"gap-header-left\">\r\n                <span className=\"gap-expand-icon\">\r\n                  {expandedGapId === gap.id ? (\r\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                      <path d=\"M6 9L12 15L18 9\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                    </svg>\r\n                  ) : (\r\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                      <path d=\"M9 6L15 12L9 18\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                    </svg>\r\n                  )}\r\n                </span>\r\n                <h4>Gap #{index + 1}</h4>\r\n              </div>\r\n              \r\n              <div className=\"gap-header-preview\">\r\n                <span className=\"gap-critique-label\">Critique - </span>\r\n                <span className=\"gap-critique-preview\">\r\n                  {truncateText(gap.critique)}\r\n                </span>\r\n              </div>\r\n              \r\n              <div className={`gap-severity ${(gap.priority || 'MEDIUM').toLowerCase()}`}>\r\n                {gap.priority}\r\n              </div>\r\n            </div>\r\n            \r\n            {expandedGapId === gap.id && (\r\n              <div className=\"gap-paragraph-content\">\r\n                {/* Critique */}\r\n                <div className=\"gap-field\">\r\n                  <h5 className=\"gap-field-label\">Critique:</h5>\r\n                  {isEditing ? (\r\n                    <textarea\r\n                      className=\"auto-resize-textarea\"\r\n                      value={gap.critique || ''}\r\n                      onChange={(e) => handleCellEdit(gap.id, 'critique', e.target.value)}\r\n                      placeholder=\"Enter critique\"\r\n                    />\r\n                  ) : (\r\n                    <p className=\"gap-field-value\">{gap.critique || 'No critique provided'}</p>\r\n                  )}\r\n                </div>\r\n                \r\n                {/* Missing Step - Add this new field */}\r\n                <div className=\"gap-field\">\r\n                  <h5 className=\"gap-field-label\">Suggestions:</h5>\r\n                  {isEditing ? (\r\n                    <textarea\r\n                      className=\"auto-resize-textarea\"\r\n                      value={gap.missing_step || ''}\r\n                      onChange={(e) => handleCellEdit(gap.id, 'missing_step', e.target.value)}\r\n                      placeholder=\"Enter missing step\"\r\n                    />\r\n                  ) : (\r\n                    <p className=\"gap-field-value\">{gap.missing_step || 'No missing step provided'}</p>\r\n                  )}\r\n                </div>\r\n                \r\n                {/* Guidelines Reference */}\r\n                <div className=\"gap-field\">\r\n                  <h5 className=\"gap-field-label\">Guidelines Reference:</h5>\r\n                  {isEditing ? (\r\n                    <textarea\r\n                      className=\"auto-resize-textarea\"\r\n                      value={gap.guidelines_reference || ''}\r\n                      onChange={(e) => handleCellEdit(gap.id, 'guidelines_reference', e.target.value)}\r\n                      placeholder=\"Enter guidelines reference\"\r\n                    />\r\n                  ) : (\r\n                    <p className=\"gap-field-value\">{gap.guidelines_reference || 'No guidelines reference provided'}</p>\r\n                  )}\r\n                </div>\r\n                \r\n                {/* SOP Reference - Add this field */}\r\n                <div className=\"gap-field\">\r\n                  <h5 className=\"gap-field-label\">SOP Reference:</h5>\r\n                  {isEditing ? (\r\n                    <textarea\r\n                      className=\"auto-resize-textarea\"\r\n                      value={gap.sop_reference || ''}\r\n                      onChange={(e) => handleCellEdit(gap.id, 'sop_reference', e.target.value)}\r\n                      placeholder=\"Enter SOP reference\"\r\n                    />\r\n                  ) : (\r\n                    <p className=\"gap-field-value\">{gap.sop_reference || 'No SOP reference provided'}</p>\r\n                  )}\r\n                </div>\r\n                \r\n                {/* Priority/Severity selector if editing */}\r\n                {isEditing && (\r\n                  <div className=\"gap-field\">\r\n                    <h5 className=\"gap-field-label\">Severity:</h5>\r\n                    <select\r\n                      className=\"severity-select\"\r\n                      value={gap.priority}\r\n                      onChange={(e) => handleCellEdit(gap.id, 'priority', e.target.value)}\r\n                    >\r\n                      <option value=\"Low\">Low</option>\r\n                      <option value=\"Medium\">Medium</option>\r\n                      <option value=\"High\">High</option>\r\n                    </select>\r\n                  </div>\r\n                )}\r\n                \r\n                {/* Action buttons for editing mode */}\r\n                {isEditing && (\r\n                  <div className=\"gap-paragraph-actions\">\r\n                    <button \r\n                      className=\"delete-row-btn\" \r\n                      onClick={(e) => {\r\n                        e.stopPropagation(); // Prevent triggering the header click\r\n                        handleDeleteRow(gap.id);\r\n                      }}\r\n                      title=\"Delete this gap\"\r\n                    >\r\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                        <path d=\"M3 6h18\"></path>\r\n                        <path d=\"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\"></path>\r\n                        <path d=\"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\"></path>\r\n                      </svg>\r\n                      Delete Gap\r\n                    </button>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        ))}\r\n        \r\n        {isEditing && (\r\n          <button className=\"add-gap-btn\" onClick={handleAddRow}>\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n              <line x1=\"12\" y1=\"5\" x2=\"12\" y2=\"19\"></line>\r\n              <line x1=\"5\" y1=\"12\" x2=\"19\" y2=\"12\"></line>\r\n            </svg>\r\n            Add Gap\r\n          </button>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Return null if not open, but only after all hooks have been called\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"modal-overlay\" onClick={onClose}>\r\n      <div className=\"gap-details-modal\" onClick={(e) => e.stopPropagation()}>\r\n        <div className=\"sidebar-header\">\r\n          <h2>Gaps & Suggestions</h2>\r\n          <div className=\"header-actions\">\r\n            <button className=\"close-btn\" onClick={onClose}>&times;</button>\r\n          </div>\r\n        </div>\r\n\r\n        {loading ? (\r\n          <div className=\"sidebar-loading\">\r\n            <LoadingSpinner size=\"large\" />\r\n            <span className=\"loading-text\">Loading analysis results...</span>\r\n          </div>\r\n        ) : error ? (\r\n          <div className=\"sidebar-error\">{error}</div>\r\n        ) : !result ? (\r\n          <div className=\"sidebar-no-data\">No analysis results available.</div>\r\n        ) : (\r\n          <>\r\n            <div className=\"sidebar-content\">\r\n              <div className='parent-info'>\r\n              <section className=\"info-section\">\r\n                <div className=\"info-row\">\r\n                  <label>SOP Name</label>\r\n                  <span>{sanitizeText(gapDetails?.title) || 'N/A'}</span>\r\n                </div>\r\n                \r\n                <div className=\"info-row\">\r\n                  <label>Department</label>\r\n                  <span>{sanitizeText(gapDetails?.department) || 'N/A'}</span>\r\n                </div>\r\n\r\n                <div className=\"info-row\">\r\n                  <label>Compliance Score</label>\r\n                  <span className=\"compliance-score\">{sanitizeText(result.compliance_score)}%</span>\r\n                </div>\r\n\r\n                <div className=\"info-row\">\r\n                  <label>{!wasValidated ? 'Analyzed on' : 'Updated on'}</label>\r\n                  <span>{formatDate(sanitizeText(!wasValidated ? result.analyzed_at : result.updated_at))}</span>\r\n                </div>\r\n\r\n              </section>\r\n\r\n              <section className=\"info-section\">\r\n                <div className=\"info-row\" style={{width: 'auto'}}>\r\n                  <label>Regulation</label>\r\n                  <ul className=\"regulations-container\">{result?.regulations?.length > 0 ? result.regulations.map((regu,key) => (\r\n                    <li className=\"regulations-name\" key={key}>\r\n                      <span>{sanitizeText(regu?.name)}</span>  \r\n                    </li>\r\n                  )) : <span>N/A</span>}\r\n                  </ul>\r\n                 \r\n                </div>\r\n              </section>\r\n              </div>\r\n              <section className=\"gaps-section\">\r\n                <div className=\"section-header\">\r\n                  <h3>Identified Gaps</h3>\r\n                  <div className=\"section-actions\">\r\n                    {isEditing && (\r\n                      <button className=\"add-row-btn\" onClick={handleAddRow}>\r\n                        + Add Gap\r\n                      </button>\r\n                    )}\r\n                    {!loading && result && (\r\n                      <button \r\n                        className={`edit-button ${isEditing ? 'active' : ''}`} \r\n                        onClick={() => setIsEditing(!isEditing)}\r\n                      >\r\n                        {isEditing ? 'Cancel Editing' : 'Edit Gaps'}\r\n                      </button>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n                \r\n                <div className=\"gaps-container\">\r\n                  {renderGaps()}\r\n                </div>\r\n              </section>\r\n            </div>\r\n\r\n            <div className=\"sidebar-footer\">\r\n              <button className=\"btn-cancel\" onClick={onClose}>Cancel</button>\r\n              <button \r\n                className={`verify-btn ${wasValidated ? (hasChanges ? '' : 'disabled') : 'verify-btn-orange'}`} \r\n                onClick={handleVerify}\r\n                disabled={verifyLoading || (wasValidated && !hasChanges)}\r\n              >\r\n                {verifyLoading ? (\r\n                  <>\r\n                    <LoadingSpinner size=\"small\" />\r\n                    <span>{wasValidated ? 'Saving...' : 'Verifying...'}</span>\r\n                  </>\r\n                ) : (\r\n                  wasValidated ? 'Save Changes' : 'Verify Changes'\r\n                )}\r\n              </button>\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default GapDetailsSidebar; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,yBAAyB;AAChC,OAAOC,cAAc,MAAM,4CAA4C;AACvE,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,SAASC,YAAY,EAAEC,YAAY,QAAQ,yBAAyB;AACpE,SAASC,UAAU,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,UAAU;EAAEC,eAAe;EAAEC,OAAO;EAAEC,KAAK;EAAEC,aAAa;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,mBAAA;EAC7H;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAMiC,eAAe,GAAG/B,MAAM,CAAC,EAAE,CAAC;EAClC,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;;EAExD;EACA,MAAMsC,MAAM,GAAGpB,eAAe,IAAID,UAAU;EAC1C;EACAsB,KAAK,CAACC,OAAO,CAACtB,eAAe,CAACuB,OAAO,CAAC,GACpCvB,eAAe,CAACuB,OAAO,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK3B,UAAU,CAAC4B,EAAE,CAAC;EAC7D;EACC3B,eAAe,CAAC0B,MAAM,KAAK3B,UAAU,CAAC4B,EAAE,GAAG3B,eAAe,GAAG,IAAK,GACnE,IAAI;;EAER;EACAjB,SAAS,CAAC,MAAM;IACd,IAAIc,MAAM,IAAIuB,MAAM,IAAIA,MAAM,CAACQ,WAAW,IAAIR,MAAM,CAACQ,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3E;MACA,MAAMC,gBAAgB,GACpBV,MAAM,CAACW,QAAQ,IACfX,MAAM,CAACW,QAAQ,CAACC,kBAAkB;;MAEpC;MACAf,eAAe,CAACa,gBAAgB,CAAC;MACjC;MACA,MAAMG,eAAe,GAAGH,gBAAgB,GACpCV,MAAM,CAACW,QAAQ,CAACC,kBAAkB,GAClCZ,MAAM,CAACQ,WAAW;;MAEtB;MACA,MAAMM,WAAW,GAAGD,eAAe,CAACE,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,MAAM;QACvD,GAAGD,GAAG;QACNT,EAAE,EAAE,OAAOU,KAAK,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC;MAEH/B,eAAe,CAAC0B,WAAW,CAAC;MAC5BnB,eAAe,CAACyB,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACT,WAAW,CAAC,CAAC;MACjEtB,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,MAAM;MACLJ,eAAe,CAAC,EAAE,CAAC;MACnBO,eAAe,CAACyB,OAAO,GAAG,EAAE;MAC5B5B,aAAa,CAAC,KAAK,CAAC;IACtB;;IAEA;IACAF,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC,EAAE,CAACb,MAAM,EAAEuB,MAAM,CAAC,CAAC;;EAEpB;EACArC,SAAS,CAAC,MAAM;IACd,IAAIc,MAAM,EAAE;MACV;MACA+C,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC,CAAC,MAAM;MACL;MACAH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC;;IAEA;IACA,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAAClD,MAAM,CAAC,CAAC;;EAEZ;EACAd,SAAS,CAAC,MAAM;IACd,IAAI0B,SAAS,IAAIM,eAAe,CAACyB,OAAO,CAACX,MAAM,GAAG,CAAC,EAAE;MACnD,MAAMmB,iBAAiB,GAAGP,IAAI,CAACE,SAAS,CAACpC,YAAY,CAAC;MACtD,MAAM0C,kBAAkB,GAAGR,IAAI,CAACE,SAAS,CAAC5B,eAAe,CAACyB,OAAO,CAAC;MAClE5B,aAAa,CAACoC,iBAAiB,KAAKC,kBAAkB,CAAC;IACzD,CAAC,MAAM,IAAIxC,SAAS,EAAE;MACpB;MACAG,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACL,YAAY,EAAEE,SAAS,CAAC,CAAC;;EAE7B;EACA1B,SAAS,CAAC,MAAM;IACd,IAAI0B,SAAS,EAAE;MACbG,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACH,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMyC,cAAc,GAAGA,CAACvB,EAAE,EAAEwB,KAAK,EAAEC,KAAK,KAAK;IAC3C5C,eAAe,CAAC6C,IAAI,IAClBA,IAAI,CAAClB,GAAG,CAACC,GAAG,IACVA,GAAG,CAACT,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGS,GAAG;MAAE,CAACe,KAAK,GAAGC;IAAM,CAAC,GAAGhB,GAC/C,CACF,CAAC;EACH,CAAC;;EAED;EACA,MAAMkB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAG;MACb5B,EAAE,EAAE,WAAWW,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MAC3BiB,eAAe,EAAE,EAAE;MACnBC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,QAAQ;MAClBC,KAAK,EAAE,IAAI,CAAC;IACd,CAAC;IAEDnD,eAAe,CAACoD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEL,MAAM,CAAC,CAAC;IAC1C3C,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACAO,gBAAgB,CAACoC,MAAM,CAAC5B,EAAE,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMkC,eAAe,GAAIlC,EAAE,IAAK;IAC9BnB,eAAe,CAAC6C,IAAI,IAAIA,IAAI,CAACS,MAAM,CAAC1B,GAAG,IAAIA,GAAG,CAACT,EAAE,KAAKA,EAAE,CAAC,CAAC;EAC5D,CAAC;;EAED;EACA,MAAMoC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAC3C,MAAM,IAAIP,aAAa,EAAE;IAE9B,IAAI;MACFC,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,MAAMkD,YAAY,GAAGzD,YAAY,CAAC4B,GAAG,CAAC,CAAC;QAAER,EAAE;QAAE,GAAGsC;MAAK,CAAC,KAAKA,IAAI,CAAC;;MAEhE;MACA,MAAMC,OAAO,GAAG;QACdxC,MAAM,EAAE3B,UAAU,CAAC4B,EAAE;QACrBK,kBAAkB,EAAEgC;MACtB,CAAC;;MAED;MACA,MAAM9E,UAAU,CAACiF,KAAK,CACpB,GAAG/E,QAAQ,CAACgF,QAAQ,CAACC,OAAO,IAAIjD,MAAM,CAACO,EAAE,EAAE,EAC3CuC,OACF,CAAC;;MAED;MACAjD,eAAe,CAAC,IAAI,CAAC;MACrBL,aAAa,CAAC,KAAK,CAAC;MACpBG,eAAe,CAACyB,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACpC,YAAY,CAAC,CAAC;;MAElE;MACA,MAAM+D,OAAO,GAAG,cAAchC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;;MAE1C;MACA,IAAGvB,YAAY,EAAC;QACd7B,KAAK,CAACoF,OAAO,CAAC,2CAA2C,EAAE;UACzDD,OAAO,EAAEA,OAAO;UAChBE,QAAQ,EAAE,WAAW;UACrBC,SAAS,EAAE,IAAI;UACfC,eAAe,EAAE,KAAK;UACtBC,YAAY,EAAE,IAAI;UAClBC,YAAY,EAAE,IAAI;UAClBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ,CAAC,MAAM;QACL1F,KAAK,CAACoF,OAAO,CAAC,wDAAwD,EAAE;UACtED,OAAO,EAAEA,OAAO;UAChBE,QAAQ,EAAE,WAAW;UACrBC,SAAS,EAAE,IAAI;UACfC,eAAe,EAAE,KAAK;UACtBC,YAAY,EAAE,IAAI;UAClBC,YAAY,EAAE,IAAI;UAClBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;;MAEA;MACA,IAAI,OAAOzE,cAAc,KAAK,UAAU,EAAE;QACxC0E,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;QACxE,MAAM3E,cAAc,CAACL,UAAU,CAAC4B,EAAE,CAAC;MACrC;;MAEA;MACA;MACA,IAAI3B,eAAe,EAAE;QACnB,MAAMgF,sBAAsB,GAAG;UAC7B,GAAGhF,eAAe;UAClB+B,QAAQ,EAAE;YACR,GAAG/B,eAAe,CAAC+B,QAAQ;YAC3BC,kBAAkB,EAAEgC;UACtB;QACF,CAAC;;QAED;QACA;MACF;IAEF,CAAC,CAAC,OAAO9D,KAAK,EAAE;MAAA,IAAA+E,eAAA,EAAAC,oBAAA;MACdJ,OAAO,CAAC5E,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MAEpD,IAAIiF,YAAY,GAAG,iDAAiD;MACpE,IAAIjF,KAAK,aAALA,KAAK,gBAAA+E,eAAA,GAAL/E,KAAK,CAAEkF,QAAQ,cAAAH,eAAA,gBAAAC,oBAAA,GAAfD,eAAA,CAAiBI,IAAI,cAAAH,oBAAA,eAArBA,oBAAA,CAAuBI,MAAM,EAAE;QAAA,IAAAC,gBAAA,EAAAC,qBAAA;QACjCL,YAAY,GAAGjF,KAAK,aAALA,KAAK,wBAAAqF,gBAAA,GAALrF,KAAK,CAAEkF,QAAQ,cAAAG,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBF,IAAI,cAAAG,qBAAA,uBAArBA,qBAAA,CAAuBF,MAAM;MAC9C;MAEA,MAAMG,YAAY,GAAG,aAAanD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MAC9CpD,KAAK,CAACe,KAAK,CAACiF,YAAY,EAAE;QACxBb,OAAO,EAAEmB,YAAY;QACrBjB,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE,IAAI;QACfC,eAAe,EAAE,KAAK;QACtBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,IAAI;QAClBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC,SAAS;MACR/D,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA/B,SAAS,CAAC,MAAM;IACd,IAAI0B,SAAS,EAAE;MACb;MACA,MAAMiF,oBAAoB,GAAGA,CAAA,KAAM;QACjC,MAAMC,SAAS,GAAG/C,QAAQ,CAACgD,gBAAgB,CAAC,uBAAuB,CAAC;QACpED,SAAS,CAACE,OAAO,CAACC,QAAQ,IAAI;UAC5BA,QAAQ,CAAChD,KAAK,CAACiD,MAAM,GAAG,MAAM;UAC9BD,QAAQ,CAAChD,KAAK,CAACiD,MAAM,GAAGD,QAAQ,CAACE,YAAY,GAAG,IAAI;QACtD,CAAC,CAAC;MACJ,CAAC;;MAED;MACAN,oBAAoB,CAAC,CAAC;;MAEtB;MACA,MAAMC,SAAS,GAAG/C,QAAQ,CAACgD,gBAAgB,CAAC,uBAAuB,CAAC;MACpED,SAAS,CAACE,OAAO,CAACC,QAAQ,IAAI;QAC5BA,QAAQ,CAACG,gBAAgB,CAAC,OAAO,EAAEP,oBAAoB,CAAC;MAC1D,CAAC,CAAC;;MAEF;MACA,OAAO,MAAM;QACXC,SAAS,CAACE,OAAO,CAACC,QAAQ,IAAI;UAC5BA,QAAQ,CAACI,mBAAmB,CAAC,OAAO,EAAER,oBAAoB,CAAC;QAC7D,CAAC,CAAC;MACJ,CAAC;IACH;EACF,CAAC,EAAE,CAACjF,SAAS,EAAEF,YAAY,EAAEW,aAAa,CAAC,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAMiF,kBAAkB,GAAIC,KAAK,IAAK;IACpCjF,gBAAgB,CAACD,aAAa,KAAKkF,KAAK,GAAG,IAAI,GAAGA,KAAK,CAAC;EAC1D,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAAC9F,YAAY,IAAIA,YAAY,CAACsB,MAAM,KAAK,CAAC,EAAE;MAC9C,oBAAOpC,OAAA;QAAG6G,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IACpE;;IAEA;IACA,MAAMC,YAAY,GAAGA,CAACC,IAAI,EAAEC,SAAS,GAAG,EAAE,KAAK;MAC7C,IAAI,CAACD,IAAI,EAAE,OAAO,sBAAsB;MACxC,OAAOA,IAAI,CAAChF,MAAM,GAAGiF,SAAS,GAC1BD,IAAI,CAACE,SAAS,CAAC,CAAC,EAAED,SAAS,CAAC,GAAG,KAAK,GACpCD,IAAI;IACV,CAAC;IAED,oBACEpH,OAAA;MAAK6G,SAAS,EAAC,0BAA0B;MAAAC,QAAA,GACtChG,YAAY,CAAC4B,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBAC3B5C,OAAA;QAAkB6G,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBAC9C9G,OAAA;UACE6G,SAAS,EAAC,sBAAsB;UAChCU,OAAO,EAAEA,CAAA,KAAMb,kBAAkB,CAAC/D,GAAG,CAACT,EAAE,CAAE;UAAA4E,QAAA,gBAE1C9G,OAAA;YAAK6G,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B9G,OAAA;cAAM6G,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC9BrF,aAAa,KAAKkB,GAAG,CAACT,EAAE,gBACvBlC,OAAA;gBAAKwH,KAAK,EAAC,IAAI;gBAAClB,MAAM,EAAC,IAAI;gBAACmB,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACC,KAAK,EAAC,4BAA4B;gBAAAb,QAAA,eAC5F9G,OAAA;kBAAM4H,CAAC,EAAC,iBAAiB;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC;gBAAO;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3G,CAAC,gBAENlH,OAAA;gBAAKwH,KAAK,EAAC,IAAI;gBAAClB,MAAM,EAAC,IAAI;gBAACmB,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACC,KAAK,EAAC,4BAA4B;gBAAAb,QAAA,eAC5F9G,OAAA;kBAAM4H,CAAC,EAAC,iBAAiB;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC;gBAAO;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3G;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACPlH,OAAA;cAAA8G,QAAA,GAAI,OAAK,EAAClE,KAAK,GAAG,CAAC;YAAA;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAENlH,OAAA;YAAK6G,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC9G,OAAA;cAAM6G,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvDlH,OAAA;cAAM6G,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EACnCK,YAAY,CAACxE,GAAG,CAACsF,QAAQ;YAAC;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENlH,OAAA;YAAK6G,SAAS,EAAE,gBAAgB,CAAClE,GAAG,CAACsB,QAAQ,IAAI,QAAQ,EAAEiE,WAAW,CAAC,CAAC,EAAG;YAAApB,QAAA,EACxEnE,GAAG,CAACsB;UAAQ;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELzF,aAAa,KAAKkB,GAAG,CAACT,EAAE,iBACvBlC,OAAA;UAAK6G,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBAEpC9G,OAAA;YAAK6G,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB9G,OAAA;cAAI6G,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC7ClG,SAAS,gBACRhB,OAAA;cACE6G,SAAS,EAAC,sBAAsB;cAChClD,KAAK,EAAEhB,GAAG,CAACsF,QAAQ,IAAI,EAAG;cAC1BE,QAAQ,EAAGC,CAAC,IAAK3E,cAAc,CAACd,GAAG,CAACT,EAAE,EAAE,UAAU,EAAEkG,CAAC,CAACC,MAAM,CAAC1E,KAAK,CAAE;cACpE2E,WAAW,EAAC;YAAgB;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,gBAEFlH,OAAA;cAAG6G,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAEnE,GAAG,CAACsF,QAAQ,IAAI;YAAsB;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC3E;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNlH,OAAA;YAAK6G,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB9G,OAAA;cAAI6G,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAChDlG,SAAS,gBACRhB,OAAA;cACE6G,SAAS,EAAC,sBAAsB;cAChClD,KAAK,EAAEhB,GAAG,CAAC4F,YAAY,IAAI,EAAG;cAC9BJ,QAAQ,EAAGC,CAAC,IAAK3E,cAAc,CAACd,GAAG,CAACT,EAAE,EAAE,cAAc,EAAEkG,CAAC,CAACC,MAAM,CAAC1E,KAAK,CAAE;cACxE2E,WAAW,EAAC;YAAoB;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,gBAEFlH,OAAA;cAAG6G,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAEnE,GAAG,CAAC4F,YAAY,IAAI;YAA0B;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACnF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNlH,OAAA;YAAK6G,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB9G,OAAA;cAAI6G,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACzDlG,SAAS,gBACRhB,OAAA;cACE6G,SAAS,EAAC,sBAAsB;cAChClD,KAAK,EAAEhB,GAAG,CAAC6F,oBAAoB,IAAI,EAAG;cACtCL,QAAQ,EAAGC,CAAC,IAAK3E,cAAc,CAACd,GAAG,CAACT,EAAE,EAAE,sBAAsB,EAAEkG,CAAC,CAACC,MAAM,CAAC1E,KAAK,CAAE;cAChF2E,WAAW,EAAC;YAA4B;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,gBAEFlH,OAAA;cAAG6G,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAEnE,GAAG,CAAC6F,oBAAoB,IAAI;YAAkC;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACnG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNlH,OAAA;YAAK6G,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB9G,OAAA;cAAI6G,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAClDlG,SAAS,gBACRhB,OAAA;cACE6G,SAAS,EAAC,sBAAsB;cAChClD,KAAK,EAAEhB,GAAG,CAAC8F,aAAa,IAAI,EAAG;cAC/BN,QAAQ,EAAGC,CAAC,IAAK3E,cAAc,CAACd,GAAG,CAACT,EAAE,EAAE,eAAe,EAAEkG,CAAC,CAACC,MAAM,CAAC1E,KAAK,CAAE;cACzE2E,WAAW,EAAC;YAAqB;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,gBAEFlH,OAAA;cAAG6G,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAEnE,GAAG,CAAC8F,aAAa,IAAI;YAA2B;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACrF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGLlG,SAAS,iBACRhB,OAAA;YAAK6G,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB9G,OAAA;cAAI6G,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9ClH,OAAA;cACE6G,SAAS,EAAC,iBAAiB;cAC3BlD,KAAK,EAAEhB,GAAG,CAACsB,QAAS;cACpBkE,QAAQ,EAAGC,CAAC,IAAK3E,cAAc,CAACd,GAAG,CAACT,EAAE,EAAE,UAAU,EAAEkG,CAAC,CAACC,MAAM,CAAC1E,KAAK,CAAE;cAAAmD,QAAA,gBAEpE9G,OAAA;gBAAQ2D,KAAK,EAAC,KAAK;gBAAAmD,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChClH,OAAA;gBAAQ2D,KAAK,EAAC,QAAQ;gBAAAmD,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtClH,OAAA;gBAAQ2D,KAAK,EAAC,MAAM;gBAAAmD,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAGAlG,SAAS,iBACRhB,OAAA;YAAK6G,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eACpC9G,OAAA;cACE6G,SAAS,EAAC,gBAAgB;cAC1BU,OAAO,EAAGa,CAAC,IAAK;gBACdA,CAAC,CAACM,eAAe,CAAC,CAAC,CAAC,CAAC;gBACrBtE,eAAe,CAACzB,GAAG,CAACT,EAAE,CAAC;cACzB,CAAE;cACFyG,KAAK,EAAC,iBAAiB;cAAA7B,QAAA,gBAEvB9G,OAAA;gBAAK2H,KAAK,EAAC,4BAA4B;gBAACH,KAAK,EAAC,IAAI;gBAAClB,MAAM,EAAC,IAAI;gBAACmB,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACG,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAAAlB,QAAA,gBAC/K9G,OAAA;kBAAM4H,CAAC,EAAC;gBAAS;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzBlH,OAAA;kBAAM4H,CAAC,EAAC;gBAAuC;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvDlH,OAAA;kBAAM4H,CAAC,EAAC;gBAAoC;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,cAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA,GAnIOvE,GAAG,CAACT,EAAE;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoIX,CACN,CAAC,EAEDlG,SAAS,iBACRhB,OAAA;QAAQ6G,SAAS,EAAC,aAAa;QAACU,OAAO,EAAE1D,YAAa;QAAAiD,QAAA,gBACpD9G,OAAA;UAAK2H,KAAK,EAAC,4BAA4B;UAACH,KAAK,EAAC,IAAI;UAAClB,MAAM,EAAC,IAAI;UAACmB,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACG,MAAM,EAAC,cAAc;UAACC,WAAW,EAAC,GAAG;UAACC,aAAa,EAAC,OAAO;UAACC,cAAc,EAAC,OAAO;UAAAlB,QAAA,gBAC/K9G,OAAA;YAAM4I,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC;UAAI;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5ClH,OAAA;YAAM4I,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC;UAAI;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,WAER;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;;EAED;EACA,IAAI,CAAC9G,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEJ,OAAA;IAAK6G,SAAS,EAAC,eAAe;IAACU,OAAO,EAAElH,OAAQ;IAAAyG,QAAA,eAC9C9G,OAAA;MAAK6G,SAAS,EAAC,mBAAmB;MAACU,OAAO,EAAGa,CAAC,IAAKA,CAAC,CAACM,eAAe,CAAC,CAAE;MAAA5B,QAAA,gBACrE9G,OAAA;QAAK6G,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B9G,OAAA;UAAA8G,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BlH,OAAA;UAAK6G,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7B9G,OAAA;YAAQ6G,SAAS,EAAC,WAAW;YAACU,OAAO,EAAElH,OAAQ;YAAAyG,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL1G,OAAO,gBACNR,OAAA;QAAK6G,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B9G,OAAA,CAACR,cAAc;UAACwJ,IAAI,EAAC;QAAO;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/BlH,OAAA;UAAM6G,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,GACJzG,KAAK,gBACPT,OAAA;QAAK6G,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAErG;MAAK;QAAAsG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,GAC1C,CAACvF,MAAM,gBACT3B,OAAA;QAAK6G,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAA8B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAErElH,OAAA,CAAAE,SAAA;QAAA4G,QAAA,gBACE9G,OAAA;UAAK6G,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B9G,OAAA;YAAK6G,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC5B9G,OAAA;cAAS6G,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC/B9G,OAAA;gBAAK6G,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB9G,OAAA;kBAAA8G,QAAA,EAAO;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvBlH,OAAA;kBAAA8G,QAAA,EAAOjH,YAAY,CAACS,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEqI,KAAK,CAAC,IAAI;gBAAK;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eAENlH,OAAA;gBAAK6G,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB9G,OAAA;kBAAA8G,QAAA,EAAO;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzBlH,OAAA;kBAAA8G,QAAA,EAAOjH,YAAY,CAACS,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE2I,UAAU,CAAC,IAAI;gBAAK;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eAENlH,OAAA;gBAAK6G,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB9G,OAAA;kBAAA8G,QAAA,EAAO;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/BlH,OAAA;kBAAM6G,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,GAAEjH,YAAY,CAAC8B,MAAM,CAACuH,gBAAgB,CAAC,EAAC,GAAC;gBAAA;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC,eAENlH,OAAA;gBAAK6G,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB9G,OAAA;kBAAA8G,QAAA,EAAQ,CAACvF,YAAY,GAAG,aAAa,GAAG;gBAAY;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC7DlH,OAAA;kBAAA8G,QAAA,EAAOhH,UAAU,CAACD,YAAY,CAAC,CAAC0B,YAAY,GAAGI,MAAM,CAACwH,WAAW,GAAGxH,MAAM,CAACyH,UAAU,CAAC;gBAAC;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEC,CAAC,eAEVlH,OAAA;cAAS6G,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC/B9G,OAAA;gBAAK6G,SAAS,EAAC,UAAU;gBAACxD,KAAK,EAAE;kBAACmE,KAAK,EAAE;gBAAM,CAAE;gBAAAV,QAAA,gBAC/C9G,OAAA;kBAAA8G,QAAA,EAAO;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzBlH,OAAA;kBAAI6G,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAE,CAAAnF,MAAM,aAANA,MAAM,wBAAAd,mBAAA,GAANc,MAAM,CAAE0H,WAAW,cAAAxI,mBAAA,uBAAnBA,mBAAA,CAAqBuB,MAAM,IAAG,CAAC,GAAGT,MAAM,CAAC0H,WAAW,CAAC3G,GAAG,CAAC,CAAC4G,IAAI,EAACC,GAAG,kBACvGvJ,OAAA;oBAAI6G,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,eAC9B9G,OAAA;sBAAA8G,QAAA,EAAOjH,YAAY,CAACyJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI;oBAAC;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC,GADHqC,GAAG;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAErC,CACL,CAAC,gBAAGlH,OAAA;oBAAA8G,QAAA,EAAM;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNlH,OAAA;YAAS6G,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC/B9G,OAAA;cAAK6G,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B9G,OAAA;gBAAA8G,QAAA,EAAI;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBlH,OAAA;gBAAK6G,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAC7B9F,SAAS,iBACRhB,OAAA;kBAAQ6G,SAAS,EAAC,aAAa;kBAACU,OAAO,EAAE1D,YAAa;kBAAAiD,QAAA,EAAC;gBAEvD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT,EACA,CAAC1G,OAAO,IAAImB,MAAM,iBACjB3B,OAAA;kBACE6G,SAAS,EAAE,eAAe7F,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;kBACtDuG,OAAO,EAAEA,CAAA,KAAMtG,YAAY,CAAC,CAACD,SAAS,CAAE;kBAAA8F,QAAA,EAEvC9F,SAAS,GAAG,gBAAgB,GAAG;gBAAW;kBAAA+F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENlH,OAAA;cAAK6G,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAC5BF,UAAU,CAAC;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAENlH,OAAA;UAAK6G,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B9G,OAAA;YAAQ6G,SAAS,EAAC,YAAY;YAACU,OAAO,EAAElH,OAAQ;YAAAyG,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChElH,OAAA;YACE6G,SAAS,EAAE,cAActF,YAAY,GAAIL,UAAU,GAAG,EAAE,GAAG,UAAU,GAAI,mBAAmB,EAAG;YAC/FqG,OAAO,EAAEjD,YAAa;YACtBmF,QAAQ,EAAErI,aAAa,IAAKG,YAAY,IAAI,CAACL,UAAY;YAAA4F,QAAA,EAExD1F,aAAa,gBACZpB,OAAA,CAAAE,SAAA;cAAA4G,QAAA,gBACE9G,OAAA,CAACR,cAAc;gBAACwJ,IAAI,EAAC;cAAO;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/BlH,OAAA;gBAAA8G,QAAA,EAAOvF,YAAY,GAAG,WAAW,GAAG;cAAc;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,eAC1D,CAAC,GAEH3F,YAAY,GAAG,cAAc,GAAG;UACjC;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA,eACN,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtG,EAAA,CA9gBIT,iBAAiB;AAAAuJ,EAAA,GAAjBvJ,iBAAiB;AAghBvB,eAAeA,iBAAiB;AAAC,IAAAuJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}