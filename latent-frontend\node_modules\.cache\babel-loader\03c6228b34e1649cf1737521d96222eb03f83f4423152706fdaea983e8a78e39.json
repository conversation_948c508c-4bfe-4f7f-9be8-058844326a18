{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\zipp\\\\latent-frontend\\\\src\\\\components\\\\common\\\\AutoLogout\\\\AutoLogout.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useRef } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport supabase from '../../../supabase';\nimport { toast } from 'react-toastify';\nimport './AutoLogout.css';\n\n// Custom timeout warning component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TimeoutWarning = ({\n  seconds,\n  onStayLoggedIn,\n  onLogout\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"timeout-warning-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"timeout-warning-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"warning-icon\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\",\n          stroke: \"currentColor\",\n          strokeWidth: \"2\",\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Session Timeout Warning\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"timeout-warning-body\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Your session is about to expire due to inactivity.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"timeout-countdown\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: `countdown-number ${seconds <= 10 ? 'urgent' : ''}`,\n          children: seconds\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"countdown-text\",\n          children: \"seconds remaining\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"timeout-instruction\",\n        children: \"Move your mouse or press a key to stay logged in.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_c = TimeoutWarning;\nconst AutoLogout = ({\n  timeoutMinutes = 5\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [lastActivity, setLastActivity] = useState(Date.now());\n  const [warningShown, setWarningShown] = useState(false);\n  const [countdown, setCountdown] = useState(60); // Countdown in seconds\n  const countdownIntervalRef = useRef(null);\n  const [showCustomWarning, setShowCustomWarning] = useState(false);\n\n  // Convert minutes to milliseconds\n  const timeoutDuration = timeoutMinutes * 60 * 1000;\n  // Show warning 1 minute before logout\n  const warningTime = timeoutDuration - 60 * 1000;\n\n  // Function to clear warning toast and reset countdown\n  const clearWarningAndReset = () => {\n    // Hide custom warning\n    setShowCustomWarning(false);\n\n    // Dismiss any existing toast\n    toast.dismiss('inactivity-warning');\n    setWarningShown(false);\n    setCountdown(60); // Reset countdown\n\n    // Clear the countdown interval\n    if (countdownIntervalRef.current) {\n      clearInterval(countdownIntervalRef.current);\n      countdownIntervalRef.current = null;\n    }\n  };\n\n  // Function to handle user activity\n  const handleUserActivity = () => {\n    setLastActivity(Date.now());\n\n    // If warning is shown, dismiss it immediately\n    if (warningShown) {\n      clearWarningAndReset();\n    }\n  };\n\n  // Function to handle logout\n  const handleLogout = async () => {\n    try {\n      // Clear warning toast and reset countdown\n      clearWarningAndReset();\n\n      // Show logout toast\n      toast.info('You have been logged out due to inactivity', {\n        toastId: 'auto-logout',\n        autoClose: 5000\n      });\n\n      // Sign out from Supabase\n      await supabase.auth.signOut();\n\n      // Redirect to login page\n      window.location.href = '/login';\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error during auto-logout:', error);\n      let errorMessage = 'Error during auto-logout.';\n      if (error !== null && error !== void 0 && (_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.detail) {\n        var _error$response2, _error$response2$data;\n        errorMessage = error === null || error === void 0 ? void 0 : (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail;\n      }\n\n      // Force reload to ensure logout even if there's an error\n      window.location.href = '/login';\n    }\n  };\n\n  // Function to manually stay logged in (reset timer)\n  const handleStayLoggedIn = () => {\n    handleUserActivity();\n  };\n\n  // Set up event listeners for user activity\n  useEffect(() => {\n    // List of events to track for user activity\n    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click', 'keydown'];\n\n    // Add event listeners\n    events.forEach(event => {\n      window.addEventListener(event, handleUserActivity);\n    });\n\n    // Clean up event listeners\n    return () => {\n      events.forEach(event => {\n        window.removeEventListener(event, handleUserActivity);\n      });\n    };\n  }, [warningShown]); // Add warningShown as dependency to update event listeners when it changes\n\n  // Start countdown timer when warning is shown\n  useEffect(() => {\n    if (warningShown && !countdownIntervalRef.current) {\n      // Start a countdown timer that updates every second\n      countdownIntervalRef.current = setInterval(() => {\n        setCountdown(prevCount => {\n          const newCount = prevCount - 1;\n\n          // If countdown reaches 0, trigger logout\n          if (newCount <= 0) {\n            handleLogout();\n            return 0;\n          }\n          return newCount;\n        });\n      }, 1000);\n    }\n\n    // Clean up interval on unmount or when warning is dismissed\n    return () => {\n      if (countdownIntervalRef.current) {\n        clearInterval(countdownIntervalRef.current);\n        countdownIntervalRef.current = null;\n      }\n    };\n  }, [warningShown]);\n\n  // Check for inactivity\n  useEffect(() => {\n    const interval = setInterval(() => {\n      const now = Date.now();\n      const timeSinceLastActivity = now - lastActivity;\n\n      // Show warning if approaching timeout and warning not shown yet\n      if (timeSinceLastActivity > warningTime && !warningShown) {\n        // Calculate seconds remaining\n        const secondsRemaining = Math.floor((timeoutDuration - timeSinceLastActivity) / 1000);\n        setCountdown(secondsRemaining);\n\n        // Show custom warning\n        setShowCustomWarning(true);\n        setWarningShown(true);\n      }\n\n      // Log out if inactive for too long\n      if (timeSinceLastActivity > timeoutDuration) {\n        handleLogout();\n        clearInterval(interval);\n      }\n    }, 1000); // Check every second for more accurate detection\n\n    return () => clearInterval(interval);\n  }, [lastActivity, timeoutDuration, warningShown, warningTime]);\n\n  // Clean up on unmount\n  useEffect(() => {\n    return () => {\n      clearWarningAndReset();\n    };\n  }, []);\n\n  // Render the custom warning if shown\n  return showCustomWarning ? /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"timeout-warning-overlay\",\n    children: /*#__PURE__*/_jsxDEV(TimeoutWarning, {\n      seconds: countdown,\n      onStayLoggedIn: handleStayLoggedIn,\n      onLogout: handleLogout\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 191,\n    columnNumber: 5\n  }, this) : null;\n};\n_s(AutoLogout, \"fPrC2t7eT2aQbZYKmEUn06pY3nk=\", false, function () {\n  return [useNavigate];\n});\n_c2 = AutoLogout;\nexport default AutoLogout;\nvar _c, _c2;\n$RefreshReg$(_c, \"TimeoutWarning\");\n$RefreshReg$(_c2, \"AutoLogout\");", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "useNavigate", "supabase", "toast", "jsxDEV", "_jsxDEV", "TimeoutWarning", "seconds", "onStayLoggedIn", "onLogout", "className", "children", "viewBox", "fill", "xmlns", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "AutoLogout", "timeoutMinutes", "_s", "navigate", "lastActivity", "setLastActivity", "Date", "now", "warningShown", "setWarningShown", "countdown", "setCountdown", "countdownIntervalRef", "showCustomWarning", "setShowCustomWarning", "timeoutDuration", "warningTime", "clearWarningAndReset", "dismiss", "current", "clearInterval", "handleUserActivity", "handleLogout", "info", "toastId", "autoClose", "auth", "signOut", "window", "location", "href", "error", "_error$response", "_error$response$data", "console", "errorMessage", "response", "data", "detail", "_error$response2", "_error$response2$data", "handleStayLoggedIn", "events", "for<PERSON>ach", "event", "addEventListener", "removeEventListener", "setInterval", "prevCount", "newCount", "interval", "timeSinceLastActivity", "secondsRemaining", "Math", "floor", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/components/common/AutoLogout/AutoLogout.js"], "sourcesContent": ["import { useState, useEffect, useRef } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport supabase from '../../../supabase';\r\nimport { toast } from 'react-toastify';\r\nimport './AutoLogout.css';\r\n\r\n// Custom timeout warning component\r\nconst TimeoutWarning = ({ seconds, onStayLoggedIn, onLogout }) => {\r\n  return (\r\n    <div className=\"timeout-warning-container\">\r\n      <div className=\"timeout-warning-header\">\r\n        <svg className=\"warning-icon\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n          <path d=\"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" \r\n            stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n        </svg>\r\n        <h3>Session Timeout Warning</h3>\r\n      </div>\r\n      <div className=\"timeout-warning-body\">\r\n        <p>Your session is about to expire due to inactivity.</p>\r\n        <div className=\"timeout-countdown\">\r\n          <span className={`countdown-number ${seconds <= 10 ? 'urgent' : ''}`}>{seconds}</span>\r\n          <span className=\"countdown-text\">seconds remaining</span>\r\n        </div>\r\n        <p className=\"timeout-instruction\">Move your mouse or press a key to stay logged in.</p>\r\n      </div>\r\n    \r\n    </div>\r\n  );\r\n};\r\n\r\nconst AutoLogout = ({ timeoutMinutes = 5 }) => {\r\n  const navigate = useNavigate();\r\n  const [lastActivity, setLastActivity] = useState(Date.now());\r\n  const [warningShown, setWarningShown] = useState(false);\r\n  const [countdown, setCountdown] = useState(60); // Countdown in seconds\r\n  const countdownIntervalRef = useRef(null);\r\n  const [showCustomWarning, setShowCustomWarning] = useState(false);\r\n  \r\n  // Convert minutes to milliseconds\r\n  const timeoutDuration = timeoutMinutes * 60 * 1000;\r\n  // Show warning 1 minute before logout\r\n  const warningTime = timeoutDuration - (60 * 1000);\r\n  \r\n  // Function to clear warning toast and reset countdown\r\n  const clearWarningAndReset = () => {\r\n    // Hide custom warning\r\n    setShowCustomWarning(false);\r\n    \r\n    // Dismiss any existing toast\r\n    toast.dismiss('inactivity-warning');\r\n    setWarningShown(false);\r\n    setCountdown(60); // Reset countdown\r\n    \r\n    // Clear the countdown interval\r\n    if (countdownIntervalRef.current) {\r\n      clearInterval(countdownIntervalRef.current);\r\n      countdownIntervalRef.current = null;\r\n    }\r\n  };\r\n  \r\n  // Function to handle user activity\r\n  const handleUserActivity = () => {\r\n    setLastActivity(Date.now());\r\n    \r\n    // If warning is shown, dismiss it immediately\r\n    if (warningShown) {\r\n      clearWarningAndReset();\r\n    }\r\n  };\r\n  \r\n  // Function to handle logout\r\n  const handleLogout = async () => {\r\n    try {\r\n      // Clear warning toast and reset countdown\r\n      clearWarningAndReset();\r\n      \r\n      // Show logout toast\r\n      toast.info('You have been logged out due to inactivity', {\r\n        toastId: 'auto-logout',\r\n        autoClose: 5000\r\n      });\r\n      \r\n      // Sign out from Supabase\r\n      await supabase.auth.signOut();\r\n      \r\n      // Redirect to login page\r\n      window.location.href = '/login';\r\n    } catch (error) {\r\n      console.error('Error during auto-logout:', error);\r\n      \r\n      let errorMessage = 'Error during auto-logout.';\r\n      if (error?.response?.data?.detail) {\r\n        errorMessage = error?.response?.data?.detail;\r\n      }\r\n      \r\n      // Force reload to ensure logout even if there's an error\r\n      window.location.href = '/login';\r\n    }\r\n  };\r\n  \r\n  // Function to manually stay logged in (reset timer)\r\n  const handleStayLoggedIn = () => {\r\n    handleUserActivity();\r\n  };\r\n  \r\n  // Set up event listeners for user activity\r\n  useEffect(() => {\r\n    // List of events to track for user activity\r\n    const events = [\r\n      'mousedown', 'mousemove', 'keypress', \r\n      'scroll', 'touchstart', 'click', 'keydown'\r\n    ];\r\n    \r\n    // Add event listeners\r\n    events.forEach(event => {\r\n      window.addEventListener(event, handleUserActivity);\r\n    });\r\n    \r\n    // Clean up event listeners\r\n    return () => {\r\n      events.forEach(event => {\r\n        window.removeEventListener(event, handleUserActivity);\r\n      });\r\n    };\r\n  }, [warningShown]); // Add warningShown as dependency to update event listeners when it changes\r\n  \r\n  // Start countdown timer when warning is shown\r\n  useEffect(() => {\r\n    if (warningShown && !countdownIntervalRef.current) {\r\n      // Start a countdown timer that updates every second\r\n      countdownIntervalRef.current = setInterval(() => {\r\n        setCountdown(prevCount => {\r\n          const newCount = prevCount - 1;\r\n          \r\n          // If countdown reaches 0, trigger logout\r\n          if (newCount <= 0) {\r\n            handleLogout();\r\n            return 0;\r\n          }\r\n          \r\n          return newCount;\r\n        });\r\n      }, 1000);\r\n    }\r\n    \r\n    // Clean up interval on unmount or when warning is dismissed\r\n    return () => {\r\n      if (countdownIntervalRef.current) {\r\n        clearInterval(countdownIntervalRef.current);\r\n        countdownIntervalRef.current = null;\r\n      }\r\n    };\r\n  }, [warningShown]);\r\n  \r\n  // Check for inactivity\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      const now = Date.now();\r\n      const timeSinceLastActivity = now - lastActivity;\r\n      \r\n      // Show warning if approaching timeout and warning not shown yet\r\n      if (timeSinceLastActivity > warningTime && !warningShown) {\r\n        // Calculate seconds remaining\r\n        const secondsRemaining = Math.floor((timeoutDuration - timeSinceLastActivity) / 1000);\r\n        setCountdown(secondsRemaining);\r\n        \r\n        // Show custom warning\r\n        setShowCustomWarning(true);\r\n        setWarningShown(true);\r\n      }\r\n      \r\n      // Log out if inactive for too long\r\n      if (timeSinceLastActivity > timeoutDuration) {\r\n        handleLogout();\r\n        clearInterval(interval);\r\n      }\r\n    }, 1000); // Check every second for more accurate detection\r\n    \r\n    return () => clearInterval(interval);\r\n  }, [lastActivity, timeoutDuration, warningShown, warningTime]);\r\n  \r\n  // Clean up on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      clearWarningAndReset();\r\n    };\r\n  }, []);\r\n  \r\n  // Render the custom warning if shown\r\n  return showCustomWarning ? (\r\n    <div className=\"timeout-warning-overlay\">\r\n      <TimeoutWarning \r\n        seconds={countdown} \r\n        onStayLoggedIn={handleStayLoggedIn} \r\n        onLogout={handleLogout} \r\n      />\r\n    </div>\r\n  ) : null;\r\n};\r\n\r\nexport default AutoLogout; "], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAO,kBAAkB;;AAEzB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAGA,CAAC;EAAEC,OAAO;EAAEC,cAAc;EAAEC;AAAS,CAAC,KAAK;EAChE,oBACEJ,OAAA;IAAKK,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACxCN,OAAA;MAAKK,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrCN,OAAA;QAAKK,SAAS,EAAC,cAAc;QAACE,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC,4BAA4B;QAAAH,QAAA,eAC9FN,OAAA;UAAMU,CAAC,EAAC,uIAAuI;UAC7IC,MAAM,EAAC,cAAc;UAACC,WAAW,EAAC,GAAG;UAACC,aAAa,EAAC,OAAO;UAACC,cAAc,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnF,CAAC,eACNlB,OAAA;QAAAM,QAAA,EAAI;MAAuB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,eACNlB,OAAA;MAAKK,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnCN,OAAA;QAAAM,QAAA,EAAG;MAAkD;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACzDlB,OAAA;QAAKK,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCN,OAAA;UAAMK,SAAS,EAAE,oBAAoBH,OAAO,IAAI,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAI,QAAA,EAAEJ;QAAO;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtFlB,OAAA;UAAMK,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAAiB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,eACNlB,OAAA;QAAGK,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAAC;MAAiD;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEH,CAAC;AAEV,CAAC;AAACC,EAAA,GArBIlB,cAAc;AAuBpB,MAAMmB,UAAU,GAAGA,CAAC;EAAEC,cAAc,GAAG;AAAE,CAAC,KAAK;EAAAC,EAAA;EAC7C,MAAMC,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAACiC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChD,MAAMuC,oBAAoB,GAAGrC,MAAM,CAAC,IAAI,CAAC;EACzC,MAAM,CAACsC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM0C,eAAe,GAAGd,cAAc,GAAG,EAAE,GAAG,IAAI;EAClD;EACA,MAAMe,WAAW,GAAGD,eAAe,GAAI,EAAE,GAAG,IAAK;;EAEjD;EACA,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACAH,oBAAoB,CAAC,KAAK,CAAC;;IAE3B;IACApC,KAAK,CAACwC,OAAO,CAAC,oBAAoB,CAAC;IACnCT,eAAe,CAAC,KAAK,CAAC;IACtBE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;;IAElB;IACA,IAAIC,oBAAoB,CAACO,OAAO,EAAE;MAChCC,aAAa,CAACR,oBAAoB,CAACO,OAAO,CAAC;MAC3CP,oBAAoB,CAACO,OAAO,GAAG,IAAI;IACrC;EACF,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/BhB,eAAe,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;;IAE3B;IACA,IAAIC,YAAY,EAAE;MAChBS,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMK,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF;MACAL,oBAAoB,CAAC,CAAC;;MAEtB;MACAvC,KAAK,CAAC6C,IAAI,CAAC,4CAA4C,EAAE;QACvDC,OAAO,EAAE,aAAa;QACtBC,SAAS,EAAE;MACb,CAAC,CAAC;;MAEF;MACA,MAAMhD,QAAQ,CAACiD,IAAI,CAACC,OAAO,CAAC,CAAC;;MAE7B;MACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACH,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MAEjD,IAAII,YAAY,GAAG,2BAA2B;MAC9C,IAAIJ,KAAK,aAALA,KAAK,gBAAAC,eAAA,GAALD,KAAK,CAAEK,QAAQ,cAAAJ,eAAA,gBAAAC,oBAAA,GAAfD,eAAA,CAAiBK,IAAI,cAAAJ,oBAAA,eAArBA,oBAAA,CAAuBK,MAAM,EAAE;QAAA,IAAAC,gBAAA,EAAAC,qBAAA;QACjCL,YAAY,GAAGJ,KAAK,aAALA,KAAK,wBAAAQ,gBAAA,GAALR,KAAK,CAAEK,QAAQ,cAAAG,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBF,IAAI,cAAAG,qBAAA,uBAArBA,qBAAA,CAAuBF,MAAM;MAC9C;;MAEA;MACAV,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;IACjC;EACF,CAAC;;EAED;EACA,MAAMW,kBAAkB,GAAGA,CAAA,KAAM;IAC/BpB,kBAAkB,CAAC,CAAC;EACtB,CAAC;;EAED;EACA/C,SAAS,CAAC,MAAM;IACd;IACA,MAAMoE,MAAM,GAAG,CACb,WAAW,EAAE,WAAW,EAAE,UAAU,EACpC,QAAQ,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,CAC3C;;IAED;IACAA,MAAM,CAACC,OAAO,CAACC,KAAK,IAAI;MACtBhB,MAAM,CAACiB,gBAAgB,CAACD,KAAK,EAAEvB,kBAAkB,CAAC;IACpD,CAAC,CAAC;;IAEF;IACA,OAAO,MAAM;MACXqB,MAAM,CAACC,OAAO,CAACC,KAAK,IAAI;QACtBhB,MAAM,CAACkB,mBAAmB,CAACF,KAAK,EAAEvB,kBAAkB,CAAC;MACvD,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,CAACb,YAAY,CAAC,CAAC,CAAC,CAAC;;EAEpB;EACAlC,SAAS,CAAC,MAAM;IACd,IAAIkC,YAAY,IAAI,CAACI,oBAAoB,CAACO,OAAO,EAAE;MACjD;MACAP,oBAAoB,CAACO,OAAO,GAAG4B,WAAW,CAAC,MAAM;QAC/CpC,YAAY,CAACqC,SAAS,IAAI;UACxB,MAAMC,QAAQ,GAAGD,SAAS,GAAG,CAAC;;UAE9B;UACA,IAAIC,QAAQ,IAAI,CAAC,EAAE;YACjB3B,YAAY,CAAC,CAAC;YACd,OAAO,CAAC;UACV;UAEA,OAAO2B,QAAQ;QACjB,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC;IACV;;IAEA;IACA,OAAO,MAAM;MACX,IAAIrC,oBAAoB,CAACO,OAAO,EAAE;QAChCC,aAAa,CAACR,oBAAoB,CAACO,OAAO,CAAC;QAC3CP,oBAAoB,CAACO,OAAO,GAAG,IAAI;MACrC;IACF,CAAC;EACH,CAAC,EAAE,CAACX,YAAY,CAAC,CAAC;;EAElB;EACAlC,SAAS,CAAC,MAAM;IACd,MAAM4E,QAAQ,GAAGH,WAAW,CAAC,MAAM;MACjC,MAAMxC,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MACtB,MAAM4C,qBAAqB,GAAG5C,GAAG,GAAGH,YAAY;;MAEhD;MACA,IAAI+C,qBAAqB,GAAGnC,WAAW,IAAI,CAACR,YAAY,EAAE;QACxD;QACA,MAAM4C,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACvC,eAAe,GAAGoC,qBAAqB,IAAI,IAAI,CAAC;QACrFxC,YAAY,CAACyC,gBAAgB,CAAC;;QAE9B;QACAtC,oBAAoB,CAAC,IAAI,CAAC;QAC1BL,eAAe,CAAC,IAAI,CAAC;MACvB;;MAEA;MACA,IAAI0C,qBAAqB,GAAGpC,eAAe,EAAE;QAC3CO,YAAY,CAAC,CAAC;QACdF,aAAa,CAAC8B,QAAQ,CAAC;MACzB;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAM9B,aAAa,CAAC8B,QAAQ,CAAC;EACtC,CAAC,EAAE,CAAC9C,YAAY,EAAEW,eAAe,EAAEP,YAAY,EAAEQ,WAAW,CAAC,CAAC;;EAE9D;EACA1C,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX2C,oBAAoB,CAAC,CAAC;IACxB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,OAAOJ,iBAAiB,gBACtBjC,OAAA;IAAKK,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtCN,OAAA,CAACC,cAAc;MACbC,OAAO,EAAE4B,SAAU;MACnB3B,cAAc,EAAE0D,kBAAmB;MACnCzD,QAAQ,EAAEsC;IAAa;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC,GACJ,IAAI;AACV,CAAC;AAACI,EAAA,CAxKIF,UAAU;EAAA,QACGxB,WAAW;AAAA;AAAA+E,GAAA,GADxBvD,UAAU;AA0KhB,eAAeA,UAAU;AAAC,IAAAD,EAAA,EAAAwD,GAAA;AAAAC,YAAA,CAAAzD,EAAA;AAAAyD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}