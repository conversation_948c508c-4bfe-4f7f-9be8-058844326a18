{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\zipp\\\\latent-frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport supabase from './supabase';\nimport routes from './routes';\nimport Toast from './components/common/Toast/Toast';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\nimport LoadingSpinner from './components/common/LoadingSpinner/LoadingSpinner';\nimport AutoLogout from './components/common/AutoLogout/AutoLogout';\nimport URLParamChecker from './components/common/URLParamChecker/URLParamChecker';\nimport { autoLogoutMinutes } from './constants/constants';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    supabase.auth.getUser().then(({\n      data: {\n        user\n      }\n    }) => {\n      setUser(user);\n      setLoading(false);\n    });\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh'\n      },\n      children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 7\n      }, this), \"Loading...\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(URLParamChecker, {\n      children: [user && /*#__PURE__*/_jsxDEV(AutoLogout, {\n        timeoutMinutes: autoLogoutMinutes\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 16\n      }, this), /*#__PURE__*/_jsxDEV(Routes, {\n        children: routes(user).map((route, index) => /*#__PURE__*/_jsxDEV(Route, {\n          path: route.path,\n          element: route.element\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Toast, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n        position: \"top-right\",\n        autoClose: 5000,\n        hideProgressBar: false,\n        newestOnTop: true,\n        closeOnClick: true,\n        rtl: false,\n        pauseOnFocusLoss: true,\n        draggable: true,\n        pauseOnHover: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "supabase", "routes", "Toast", "ToastContainer", "LoadingSpinner", "AutoLogout", "URLParamChecker", "autoLogoutMinutes", "jsxDEV", "_jsxDEV", "App", "_s", "user", "setUser", "loading", "setLoading", "auth", "getUser", "then", "data", "style", "display", "justifyContent", "alignItems", "height", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "timeoutMinutes", "map", "route", "index", "path", "element", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/App.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\r\nimport supabase from './supabase';\r\nimport routes from './routes';\r\nimport Toast from './components/common/Toast/Toast';\r\nimport { ToastContainer } from 'react-toastify';\r\nimport 'react-toastify/dist/ReactToastify.css';\r\nimport LoadingSpinner from './components/common/LoadingSpinner/LoadingSpinner';\r\nimport AutoLogout from './components/common/AutoLogout/AutoLogout';\r\nimport URLParamChecker from './components/common/URLParamChecker/URLParamChecker';\r\nimport { autoLogoutMinutes } from './constants/constants';\r\n\r\nfunction App() {\r\n  const [user, setUser] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    supabase.auth.getUser().then(({ data: { user } }) => {\r\n      setUser(user);\r\n      setLoading(false);\r\n    });\r\n  }, []);\r\n\r\n  if (loading) {\r\n    return <div style={{display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh'}}>\r\n      <LoadingSpinner/>\r\n      Loading...\r\n      </div>;\r\n  }\r\n\r\n  return (\r\n    <Router>\r\n      <URLParamChecker>\r\n      {user && <AutoLogout timeoutMinutes={autoLogoutMinutes} />}\r\n      <Routes>\r\n        {routes(user).map((route, index) => (\r\n          <Route \r\n            key={index}\r\n            path={route.path}\r\n            element={route.element}\r\n          />\r\n        ))}\r\n      </Routes>\r\n      <Toast />\r\n      <ToastContainer \r\n        position=\"top-right\"\r\n        autoClose={5000}\r\n        hideProgressBar={false}\r\n        newestOnTop\r\n        closeOnClick\r\n        rtl={false}\r\n        pauseOnFocusLoss\r\n        draggable\r\n        pauseOnHover\r\n      />\r\n      </URLParamChecker>\r\n    </Router>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,iCAAiC;AACnD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,uCAAuC;AAC9C,OAAOC,cAAc,MAAM,mDAAmD;AAC9E,OAAOC,UAAU,MAAM,2CAA2C;AAClE,OAAOC,eAAe,MAAM,qDAAqD;AACjF,SAASC,iBAAiB,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAE5CD,SAAS,CAAC,MAAM;IACdM,QAAQ,CAACgB,IAAI,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;MAAEC,IAAI,EAAE;QAAEP;MAAK;IAAE,CAAC,KAAK;MACnDC,OAAO,CAACD,IAAI,CAAC;MACbG,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,IAAID,OAAO,EAAE;IACX,oBAAOL,OAAA;MAAKW,KAAK,EAAE;QAACC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,UAAU,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAO,CAAE;MAAAC,QAAA,gBACpGhB,OAAA,CAACL,cAAc;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,cAEjB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACV;EAEA,oBACEpB,OAAA,CAACZ,MAAM;IAAA4B,QAAA,eACLhB,OAAA,CAACH,eAAe;MAAAmB,QAAA,GACfb,IAAI,iBAAIH,OAAA,CAACJ,UAAU;QAACyB,cAAc,EAAEvB;MAAkB;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1DpB,OAAA,CAACX,MAAM;QAAA2B,QAAA,EACJxB,MAAM,CAACW,IAAI,CAAC,CAACmB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC7BxB,OAAA,CAACV,KAAK;UAEJmC,IAAI,EAAEF,KAAK,CAACE,IAAK;UACjBC,OAAO,EAAEH,KAAK,CAACG;QAAQ,GAFlBF,KAAK;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACTpB,OAAA,CAACP,KAAK;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACTpB,OAAA,CAACN,cAAc;QACbiC,QAAQ,EAAC,WAAW;QACpBC,SAAS,EAAE,IAAK;QAChBC,eAAe,EAAE,KAAM;QACvBC,WAAW;QACXC,YAAY;QACZC,GAAG,EAAE,KAAM;QACXC,gBAAgB;QAChBC,SAAS;QACTC,YAAY;MAAA;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACe;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEb;AAAClB,EAAA,CA9CQD,GAAG;AAAAmC,EAAA,GAAHnC,GAAG;AAgDZ,eAAeA,GAAG;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}