{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\zipp\\\\latent-frontend\\\\src\\\\components\\\\pages\\\\ProfileSettings\\\\ProfileSettings.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport userService from '../../../services/userService';\nimport Navigation from '../../common/Navigation/Navigation';\nimport './ProfileSettings.css';\nimport apiService from '../../../services/api';\nimport { toast, ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\nimport API_URLS from '../../../config/apiUrls';\nimport LoadingSpinner from '../../common/LoadingSpinner/LoadingSpinner';\nimport { adminOnly } from '../../../constants/constants';\nimport { sanitizeText } from '../../../utils/sanitize';\nimport supabase from '../../../supabase';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProfileSettings = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    fullName: '',\n    email: '',\n    department: '',\n    designation: '',\n    organizationName: '',\n    industry: '',\n    subIndustry: '',\n    standards: ''\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [regulations, setRegulations] = useState([]);\n  const [selectedRegulations, setSelectedRegulations] = useState([]);\n  const [regulationsLoading, setRegulationsLoading] = useState(false);\n  const [regulationsError, setRegulationsError] = useState(null);\n  const [originalFormData, setOriginalFormData] = useState(null);\n  const [originalRegulations, setOriginalRegulations] = useState([]);\n  const [hasChanges, setHasChanges] = useState(false);\n  const [regulationsDataReady, setRegulationsDataReady] = useState(false);\n  const [isAdmin, setIsAdmin] = useState(false);\n\n  // Helper function to compare arrays\n  const arraysEqual = (a, b) => {\n    if (a.length !== b.length) return false;\n    const sortedA = [...a].sort();\n    const sortedB = [...b].sort();\n    return sortedA.every((val, idx) => val === sortedB[idx]);\n  };\n  useEffect(() => {\n    const fetchUserProfile = async () => {\n      try {\n        setLoading(true);\n        const userData = await userService.getUserProfile();\n        if (userData && userData.length > 0) {\n          const user = userData[0];\n          const dummyData = {\n            department: '',\n            standards: '',\n            organizationName: '',\n            industry: '',\n            subIndustry: ''\n          };\n\n          // Create the form data object\n          const newFormData = {\n            fullName: `${user.first_name || ''} ${user.last_name || ''}`.trim(),\n            email: user.email || '',\n            department: user.department || dummyData.department,\n            designation: user.org_role || '',\n            organizationName: user.organization.name || dummyData.organizationName,\n            industry: user.industry || dummyData.industry,\n            subIndustry: user.sub_industry || dummyData.subIndustry,\n            standards: user.standards || dummyData.standards\n          };\n\n          // Update form data\n          setFormData(newFormData);\n\n          // Store original form data for comparison\n          setOriginalFormData(JSON.stringify(newFormData));\n        }\n      } catch (err) {\n        var _err$response, _err$response$data;\n        console.error('Error fetching user profile:', err);\n        let errorMessage = 'Failed to load user profile. Please try again later.';\n        if (err !== null && err !== void 0 && (_err$response = err.response) !== null && _err$response !== void 0 && (_err$response$data = _err$response.data) !== null && _err$response$data !== void 0 && _err$response$data.detail) {\n          var _err$response2, _err$response2$data;\n          errorMessage = err === null || err === void 0 ? void 0 : (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.detail;\n        }\n        setError(errorMessage);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchUserProfile();\n  }, []);\n  useEffect(() => {\n    const fetchRegulations = async () => {\n      try {\n        setRegulationsLoading(true);\n        setRegulationsError(null);\n\n        // Fetch both regulation data sets in parallel\n        const [allRegulations, orgRegulations] = await Promise.all([apiService.get(API_URLS.REGULATIONS.LIST), apiService.get(API_URLS.REGULATIONS.ORGANIZATION)]);\n\n        // Set all regulations\n        setRegulations(allRegulations || []);\n\n        // Map the organization regulations to IDs for easy checking\n        const selectedIds = orgRegulations.map(reg => reg.regulation_id);\n        setSelectedRegulations(selectedIds);\n\n        // Store original regulations for comparison\n        setOriginalRegulations([...selectedIds]);\n\n        // Set data as ready\n        setRegulationsDataReady(true);\n      } catch (error) {\n        var _error$response, _error$response$data;\n        console.error('Error fetching regulations:', error);\n        let errorMessage = 'Failed to load regulations. Please try again.';\n        if (error !== null && error !== void 0 && (_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.detail) {\n          var _error$response2, _error$response2$data;\n          errorMessage = error === null || error === void 0 ? void 0 : (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail;\n        }\n        setRegulationsError(errorMessage);\n      } finally {\n        setRegulationsLoading(false);\n      }\n    };\n    fetchRegulations();\n  }, []);\n  useEffect(() => {\n    // Check if form data has changed\n    const formDataChanged = originalFormData && JSON.stringify(formData) !== originalFormData;\n\n    // Check if regulations have changed\n    const regulationsChanged = originalRegulations && !arraysEqual(selectedRegulations, originalRegulations);\n\n    // Update hasChanges state\n    setHasChanges(formDataChanged || regulationsChanged);\n  }, [formData, selectedRegulations, originalFormData, originalRegulations]);\n  useEffect(() => {\n    const checkAdmin = async () => {\n      try {\n        const userData = await userService.getUserProfile();\n        if (userData && userData.length > 0) {\n          const user = userData[0];\n          setIsAdmin(user.org_role === adminOnly);\n        }\n      } catch (err) {\n        var _err$response3, _err$response3$data;\n        console.error('Error checking admin status:', err);\n        let errorMessage = 'Failed to check admin status. Please try again later.';\n        if (err !== null && err !== void 0 && (_err$response3 = err.response) !== null && _err$response3 !== void 0 && (_err$response3$data = _err$response3.data) !== null && _err$response3$data !== void 0 && _err$response3$data.detail) {\n          var _err$response4, _err$response4$data;\n          errorMessage = err === null || err === void 0 ? void 0 : (_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.detail;\n        }\n        setError(errorMessage);\n      }\n    };\n    checkAdmin();\n  }, []);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleRegulationToggle = regulationId => {\n    // Only allow admin users to toggle regulations\n    if (!isAdmin) return;\n    setSelectedRegulations(prev => {\n      if (prev.includes(regulationId)) {\n        return prev.filter(id => id !== regulationId);\n      } else {\n        return [...prev, regulationId];\n      }\n    });\n  };\n  const handleSaveChanges = async () => {\n    try {\n      setLoading(true);\n\n      // Sanitize form data before submission\n      const sanitizedFormData = {\n        ...formData,\n        fullName: sanitizeText(formData.fullName),\n        department: sanitizeText(formData.department),\n        designation: sanitizeText(formData.designation),\n        industry: sanitizeText(formData.industry),\n        subIndustry: sanitizeText(formData.subIndustry)\n        // Sanitize any other text fields\n      };\n\n      // Get the current user ID from Supabase\n      const {\n        data: {\n          user\n        }\n      } = await supabase.auth.getUser();\n      const userId = user === null || user === void 0 ? void 0 : user.id;\n      if (!userId) {\n        throw new Error('User ID not found');\n      }\n\n      // Split the full name into first and last name\n      const nameParts = sanitizedFormData.fullName.trim().split(/\\s+/);\n      const firstName = nameParts[0] || '';\n      const lastName = nameParts.slice(1).join(' ') || '';\n\n      // Prepare data for API\n      const updateData = {\n        first_name: firstName,\n        last_name: lastName\n      };\n\n      // Get the current user data to access existing metadata\n      const currentUserData = await userService.getUserProfile();\n      const currentUser = currentUserData && currentUserData.length > 0 ? currentUserData[0] : null;\n\n      // Handle metadata for standards\n      if (sanitizedFormData.standards && sanitizedFormData.standards.trim() !== '') {\n        // Initialize metadata object if it doesn't exist\n        if (!updateData.metadata) {\n          updateData.metadata = {};\n        }\n\n        // Get existing user_recommended_compliances as an array or initialize as empty array\n        let existingCompliances = [];\n        if (currentUser && currentUser.metadata && currentUser.metadata.user_recommended_compliances) {\n          // If it's already an array, use it\n          if (Array.isArray(currentUser.metadata.user_recommended_compliances)) {\n            existingCompliances = currentUser.metadata.user_recommended_compliances;\n          }\n          // If it's a string, convert to array with one item\n          else if (typeof currentUser.metadata.user_recommended_compliances === 'string') {\n            existingCompliances = [currentUser.metadata.user_recommended_compliances];\n          }\n        }\n\n        // Add the new compliance if it's not already in the array\n        if (!existingCompliances.includes(sanitizedFormData.standards.trim())) {\n          existingCompliances.push(sanitizedFormData.standards.trim());\n        }\n\n        // Update the metadata with the combined array\n        updateData.metadata.user_recommended_compliances = existingCompliances;\n      }\n      console.log('Updating user profile with data:', updateData);\n\n      // Update user profile\n      const response = await userService.updateUserProfile(updateData, userId);\n\n      // Handle regulation changes\n      const regulationsToAdd = selectedRegulations.filter(id => !originalRegulations.includes(id));\n      const regulationsToRemove = originalRegulations.filter(id => !selectedRegulations.includes(id));\n\n      // Process regulation additions\n      for (const regulationId of regulationsToAdd) {\n        await apiService.post(API_URLS.REGULATIONS.ORGANIZATION + `/${regulationId}`);\n      }\n\n      // Process regulation removals\n      for (const regulationId of regulationsToRemove) {\n        await apiService.delete(API_URLS.REGULATIONS.ORGANIZATION + `/${regulationId}`);\n      }\n      console.log('Profile update response:', response);\n\n      // Show success message with more details\n      toast.success('Profile updated successfully!', {\n        position: \"top-right\",\n        autoClose: 5000,\n        hideProgressBar: false,\n        closeOnClick: true,\n        pauseOnHover: true,\n        draggable: true\n      });\n\n      // Update the original form data and regulations to reflect the saved state\n      setOriginalFormData(JSON.stringify(sanitizedFormData));\n      setOriginalRegulations([...selectedRegulations]);\n\n      // Clear the standards input after successful update\n      setFormData(prev => ({\n        ...prev,\n        standards: ''\n      }));\n    } catch (err) {\n      var _err$response5, _err$response5$data;\n      console.error('Error updating profile:', err);\n      let errorMessage = 'Failed to update profile. Please try again.';\n      if (err !== null && err !== void 0 && (_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.detail) {\n        var _err$response6, _err$response6$data;\n        errorMessage = err === null || err === void 0 ? void 0 : (_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.detail;\n      }\n      setError(errorMessage);\n\n      // Show detailed error message\n      toast.error(`Failed to update profile: ${errorMessage}`, {\n        position: \"top-right\",\n        autoClose: 7000,\n        hideProgressBar: false,\n        closeOnClick: true,\n        pauseOnHover: true,\n        draggable: true\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading && !formData.email) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-indicator\",\n      children: \"Loading profile data...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Navigation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 6\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"profile-settings\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"settings-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"User Profile Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Basic Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Full Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"fullName\",\n            placeholder: \"Enter your full name\",\n            value: formData.fullName,\n            onChange: handleInputChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            name: \"email\",\n            placeholder: \"Enter user's email address\",\n            value: formData.email,\n            onChange: handleInputChange,\n            disabled: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Department\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"department\",\n              placeholder: \"Select department\",\n              value: formData.department,\n              onChange: handleInputChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Designation/Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"designation\",\n              placeholder: \"Enter user's role\",\n              value: formData.designation,\n              onChange: handleInputChange,\n              disabled: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"settings-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Organization Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Organization Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"organizationName\",\n            placeholder: \"Enter organization name\",\n            value: formData.organizationName,\n            onChange: handleInputChange,\n            disabled: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Industry\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"industry\",\n              placeholder: \"Enter industry\",\n              value: formData.industry,\n              onChange: handleInputChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Sub-Industry\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"subIndustry\",\n              placeholder: \"Enter sub-industry\",\n              value: formData.subIndustry,\n              onChange: handleInputChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"settings-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"regulations-heading\",\n            children: [\"Regulations List\", !isAdmin && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"admin-only-badge\",\n              children: \"Admin only\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 28\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this), regulationsLoading && /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 36\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this), !isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"admin-notice\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Only administrators can modify organization regulations. Contact your administrator for changes.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 13\n        }, this), regulationsError ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: regulationsError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 13\n        }, this) : !regulationsDataReady ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-message\",\n          children: \"Loading regulations data...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"regulations-list\",\n          children: regulations.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No regulations available.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 17\n          }, this) : regulations.map(regulation => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `regulation-item ${!isAdmin ? 'disabled-regulation' : ''}`,\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              className: `checkbox-container ${!isAdmin ? 'disabled-checkbox' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: selectedRegulations.includes(regulation.id),\n                onChange: () => handleRegulationToggle(regulation.id),\n                disabled: !isAdmin\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `checkmark ${!isAdmin ? 'disabled' : ''}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"regulation-name\",\n                children: regulation.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 21\n            }, this)\n          }, regulation.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"settings-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Recommend Compliances To Be Added\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"standards\",\n            placeholder: \"Add applicable standards\",\n            value: formData.standards,\n            onChange: handleInputChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-cancel\",\n            onClick: () => window.history.back(),\n            disabled: loading,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-save\",\n            onClick: handleSaveChanges,\n            disabled: loading || !hasChanges,\n            children: loading ? /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"button-loading\",\n              children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 19\n              }, this), \"Saving...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 17\n            }, this) : 'Save Changes'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ProfileSettings, \"13mkxfl6QoQH8558ZLi7APdAmxc=\");\n_c = ProfileSettings;\nexport default ProfileSettings;\nvar _c;\n$RefreshReg$(_c, \"ProfileSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "userService", "Navigation", "apiService", "toast", "ToastContainer", "API_URLS", "LoadingSpinner", "adminOnly", "sanitizeText", "supabase", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProfileSettings", "_s", "formData", "setFormData", "fullName", "email", "department", "designation", "organizationName", "industry", "subIndustry", "standards", "loading", "setLoading", "error", "setError", "regulations", "setRegulations", "selectedRegulations", "setSelectedRegulations", "regulationsLoading", "setRegulationsLoading", "regulationsError", "setRegulationsError", "originalFormData", "setOriginalFormData", "originalRegulations", "setOriginalRegulations", "has<PERSON><PERSON><PERSON>", "set<PERSON>as<PERSON><PERSON><PERSON>", "regulationsDataReady", "setRegulationsDataReady", "isAdmin", "setIsAdmin", "arraysEqual", "a", "b", "length", "sortedA", "sort", "sortedB", "every", "val", "idx", "fetchUserProfile", "userData", "getUserProfile", "user", "dummyData", "newFormData", "first_name", "last_name", "trim", "org_role", "organization", "name", "sub_industry", "JSON", "stringify", "err", "_err$response", "_err$response$data", "console", "errorMessage", "response", "data", "detail", "_err$response2", "_err$response2$data", "fetchRegulations", "allRegulations", "orgRegulations", "Promise", "all", "get", "REGULATIONS", "LIST", "ORGANIZATION", "selectedIds", "map", "reg", "regulation_id", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "formDataChanged", "regulationsChanged", "checkAdmin", "_err$response3", "_err$response3$data", "_err$response4", "_err$response4$data", "handleInputChange", "e", "value", "target", "prev", "handleRegulationToggle", "regulationId", "includes", "filter", "id", "handleSaveChanges", "sanitizedFormData", "auth", "getUser", "userId", "Error", "nameParts", "split", "firstName", "lastName", "slice", "join", "updateData", "currentUserData", "currentUser", "metadata", "existingCompliances", "user_recommended_compliances", "Array", "isArray", "push", "log", "updateUserProfile", "regulationsToAdd", "regulationsToRemove", "post", "delete", "success", "position", "autoClose", "hideProgressBar", "closeOnClick", "pauseOnHover", "draggable", "_err$response5", "_err$response5$data", "_err$response6", "_err$response6$data", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "onChange", "disabled", "size", "regulation", "checked", "onClick", "window", "history", "back", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/zipp/latent-frontend/src/components/pages/ProfileSettings/ProfileSettings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport userService from '../../../services/userService';\r\nimport Navigation from '../../common/Navigation/Navigation';\r\nimport './ProfileSettings.css';\r\nimport apiService from '../../../services/api';\r\nimport { toast, ToastContainer } from 'react-toastify';\r\nimport 'react-toastify/dist/ReactToastify.css';\r\nimport API_URLS from '../../../config/apiUrls';\r\nimport LoadingSpinner from '../../common/LoadingSpinner/LoadingSpinner';\r\nimport { adminOnly } from '../../../constants/constants';\r\nimport { sanitizeText } from '../../../utils/sanitize';\r\nimport supabase from '../../../supabase';\r\n\r\nconst ProfileSettings = () => {\r\n  const [formData, setFormData] = useState({\r\n    fullName: '',\r\n    email: '',\r\n    department: '',\r\n    designation: '',\r\n    organizationName: '',\r\n    industry: '',\r\n    subIndustry: '',\r\n    standards:''\r\n  });\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [regulations, setRegulations] = useState([]);\r\n  const [selectedRegulations, setSelectedRegulations] = useState([]);\r\n  const [regulationsLoading, setRegulationsLoading] = useState(false);\r\n  const [regulationsError, setRegulationsError] = useState(null);\r\n  const [originalFormData, setOriginalFormData] = useState(null);\r\n  const [originalRegulations, setOriginalRegulations] = useState([]);\r\n  const [hasChanges, setHasChanges] = useState(false);\r\n  const [regulationsDataReady, setRegulationsDataReady] = useState(false);\r\n  const [isAdmin, setIsAdmin] = useState(false);\r\n\r\n  // Helper function to compare arrays\r\n  const arraysEqual = (a, b) => {\r\n    if (a.length !== b.length) return false;\r\n    const sortedA = [...a].sort();\r\n    const sortedB = [...b].sort();\r\n    return sortedA.every((val, idx) => val === sortedB[idx]);\r\n  };\r\n\r\n  useEffect(() => {\r\n    const fetchUserProfile = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const userData = await userService.getUserProfile();\r\n        \r\n        if (userData && userData.length > 0) {\r\n          const user = userData[0];\r\n          const dummyData = {\r\n            department:'',\r\n            standards:'',\r\n            organizationName:'',\r\n            industry:'',\r\n            subIndustry:''\r\n          }\r\n          \r\n          // Create the form data object\r\n          const newFormData = {\r\n            fullName: `${user.first_name || ''} ${user.last_name || ''}`.trim(),\r\n            email: user.email || '',\r\n            department: user.department || dummyData.department,\r\n            designation: user.org_role || '',\r\n            organizationName: user.organization.name || dummyData.organizationName,\r\n            industry: user.industry || dummyData.industry,\r\n            subIndustry: user.sub_industry || dummyData.subIndustry,\r\n            standards: user.standards || dummyData.standards\r\n          };\r\n          \r\n          // Update form data\r\n          setFormData(newFormData);\r\n          \r\n          // Store original form data for comparison\r\n          setOriginalFormData(JSON.stringify(newFormData));\r\n        }\r\n      } catch (err) {\r\n        console.error('Error fetching user profile:', err);\r\n        \r\n        let errorMessage = 'Failed to load user profile. Please try again later.';\r\n        if (err?.response?.data?.detail) {\r\n          errorMessage = err?.response?.data?.detail;\r\n        }\r\n        \r\n        setError(errorMessage);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchUserProfile();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const fetchRegulations = async () => {\r\n      try {\r\n        setRegulationsLoading(true);\r\n        setRegulationsError(null);\r\n        \r\n        // Fetch both regulation data sets in parallel\r\n        const [allRegulations, orgRegulations] = await Promise.all([\r\n          apiService.get(API_URLS.REGULATIONS.LIST),\r\n          apiService.get(API_URLS.REGULATIONS.ORGANIZATION)\r\n        ]);\r\n        \r\n        // Set all regulations\r\n        setRegulations(allRegulations || []);\r\n        \r\n        // Map the organization regulations to IDs for easy checking\r\n        const selectedIds = orgRegulations.map(reg => reg.regulation_id);\r\n        setSelectedRegulations(selectedIds);\r\n        \r\n        // Store original regulations for comparison\r\n        setOriginalRegulations([...selectedIds]);\r\n        \r\n        // Set data as ready\r\n        setRegulationsDataReady(true);\r\n        \r\n      } catch (error) {\r\n        console.error('Error fetching regulations:', error);\r\n        \r\n        let errorMessage = 'Failed to load regulations. Please try again.';\r\n        if (error?.response?.data?.detail) {\r\n          errorMessage = error?.response?.data?.detail;\r\n        }\r\n        \r\n        setRegulationsError(errorMessage);\r\n      } finally {\r\n        setRegulationsLoading(false);\r\n      }\r\n    };\r\n    \r\n    fetchRegulations();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    // Check if form data has changed\r\n    const formDataChanged = originalFormData && JSON.stringify(formData) !== originalFormData;\r\n    \r\n    // Check if regulations have changed\r\n    const regulationsChanged = originalRegulations && !arraysEqual(selectedRegulations, originalRegulations);\r\n    \r\n    // Update hasChanges state\r\n    setHasChanges(formDataChanged || regulationsChanged);\r\n  }, [formData, selectedRegulations, originalFormData, originalRegulations]);\r\n\r\n  useEffect(() => {\r\n    const checkAdmin = async () => {\r\n      try {\r\n        const userData = await userService.getUserProfile();\r\n        if (userData && userData.length > 0) {\r\n          const user = userData[0];\r\n          setIsAdmin(user.org_role === adminOnly);\r\n        }\r\n      } catch (err) {\r\n        console.error('Error checking admin status:', err);\r\n        \r\n        let errorMessage = 'Failed to check admin status. Please try again later.';\r\n        if (err?.response?.data?.detail) {\r\n          errorMessage = err?.response?.data?.detail;\r\n        }\r\n        \r\n        setError(errorMessage);\r\n      }\r\n    };\r\n\r\n    checkAdmin();\r\n  }, []);\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: value\r\n    }));\r\n  };\r\n\r\n  const handleRegulationToggle = (regulationId) => {\r\n    // Only allow admin users to toggle regulations\r\n    if (!isAdmin) return;\r\n    \r\n    setSelectedRegulations(prev => {\r\n      if (prev.includes(regulationId)) {\r\n        return prev.filter(id => id !== regulationId);\r\n      } else {\r\n        return [...prev, regulationId];\r\n      }\r\n    });\r\n  };\r\n\r\n  const handleSaveChanges = async () => {\r\n    try {\r\n      setLoading(true);\r\n      \r\n      // Sanitize form data before submission\r\n      const sanitizedFormData = {\r\n        ...formData,\r\n        fullName: sanitizeText(formData.fullName),\r\n        department: sanitizeText(formData.department),\r\n        designation: sanitizeText(formData.designation),\r\n        industry: sanitizeText(formData.industry),\r\n        subIndustry: sanitizeText(formData.subIndustry),\r\n        // Sanitize any other text fields\r\n      };\r\n      \r\n      // Get the current user ID from Supabase\r\n      const { data: { user } } = await supabase.auth.getUser();\r\n      const userId = user?.id;\r\n      \r\n      if (!userId) {\r\n        throw new Error('User ID not found');\r\n      }\r\n      \r\n      // Split the full name into first and last name\r\n      const nameParts = sanitizedFormData.fullName.trim().split(/\\s+/);\r\n      const firstName = nameParts[0] || '';\r\n      const lastName = nameParts.slice(1).join(' ') || '';\r\n      \r\n      // Prepare data for API\r\n      const updateData = {\r\n        first_name: firstName,\r\n        last_name: lastName,\r\n      };\r\n      \r\n      // Get the current user data to access existing metadata\r\n      const currentUserData = await userService.getUserProfile();\r\n      const currentUser = currentUserData && currentUserData.length > 0 ? currentUserData[0] : null;\r\n      \r\n      // Handle metadata for standards\r\n      if (sanitizedFormData.standards && sanitizedFormData.standards.trim() !== '') {\r\n        // Initialize metadata object if it doesn't exist\r\n        if (!updateData.metadata) {\r\n          updateData.metadata = {};\r\n        }\r\n        \r\n        // Get existing user_recommended_compliances as an array or initialize as empty array\r\n        let existingCompliances = [];\r\n        \r\n        if (currentUser && \r\n            currentUser.metadata && \r\n            currentUser.metadata.user_recommended_compliances) {\r\n          // If it's already an array, use it\r\n          if (Array.isArray(currentUser.metadata.user_recommended_compliances)) {\r\n            existingCompliances = currentUser.metadata.user_recommended_compliances;\r\n          } \r\n          // If it's a string, convert to array with one item\r\n          else if (typeof currentUser.metadata.user_recommended_compliances === 'string') {\r\n            existingCompliances = [currentUser.metadata.user_recommended_compliances];\r\n          }\r\n        }\r\n        \r\n        // Add the new compliance if it's not already in the array\r\n        if (!existingCompliances.includes(sanitizedFormData.standards.trim())) {\r\n          existingCompliances.push(sanitizedFormData.standards.trim());\r\n        }\r\n        \r\n        // Update the metadata with the combined array\r\n        updateData.metadata.user_recommended_compliances = existingCompliances;\r\n      }\r\n      \r\n      console.log('Updating user profile with data:', updateData);\r\n      \r\n      // Update user profile\r\n      const response = await userService.updateUserProfile(updateData, userId);\r\n      \r\n      // Handle regulation changes\r\n      const regulationsToAdd = selectedRegulations.filter(id => !originalRegulations.includes(id));\r\n      const regulationsToRemove = originalRegulations.filter(id => !selectedRegulations.includes(id));\r\n      \r\n      // Process regulation additions\r\n      for (const regulationId of regulationsToAdd) {\r\n        await apiService.post(API_URLS.REGULATIONS.ORGANIZATION + `/${regulationId}`);\r\n      }\r\n      \r\n      // Process regulation removals\r\n      for (const regulationId of regulationsToRemove) {\r\n        await apiService.delete(API_URLS.REGULATIONS.ORGANIZATION + `/${regulationId}`);\r\n      }\r\n      \r\n      console.log('Profile update response:', response);\r\n      \r\n      // Show success message with more details\r\n      toast.success('Profile updated successfully!', {\r\n        position: \"top-right\",\r\n        autoClose: 5000,\r\n        hideProgressBar: false,\r\n        closeOnClick: true,\r\n        pauseOnHover: true,\r\n        draggable: true,\r\n      });\r\n      \r\n      // Update the original form data and regulations to reflect the saved state\r\n      setOriginalFormData(JSON.stringify(sanitizedFormData));\r\n      setOriginalRegulations([...selectedRegulations]);\r\n      \r\n      // Clear the standards input after successful update\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        standards: ''\r\n      }));\r\n      \r\n    } catch (err) {\r\n      console.error('Error updating profile:', err);\r\n      \r\n      let errorMessage = 'Failed to update profile. Please try again.';\r\n      if (err?.response?.data?.detail) {\r\n        errorMessage = err?.response?.data?.detail;\r\n      }\r\n      \r\n      setError(errorMessage);\r\n      \r\n      // Show detailed error message\r\n      toast.error(`Failed to update profile: ${errorMessage}`, {\r\n        position: \"top-right\",\r\n        autoClose: 7000,\r\n        hideProgressBar: false,\r\n        closeOnClick: true,\r\n        pauseOnHover: true,\r\n        draggable: true,\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  if (loading && !formData.email) {\r\n    return <div className=\"loading-indicator\">Loading profile data...</div>;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <Navigation />\r\n     <ToastContainer />\r\n      <div className=\"profile-settings\">\r\n        {error && <div className=\"error-message\">{error}</div>}\r\n\r\n        <section className=\"settings-section\">\r\n          <h1>User Profile Settings</h1>\r\n          <h2>Basic Information</h2>\r\n          <div className=\"form-group\">\r\n            <label>Full Name</label>\r\n            <input\r\n              type=\"text\"\r\n              name=\"fullName\"\r\n              placeholder=\"Enter your full name\"\r\n              value={formData.fullName}\r\n              onChange={handleInputChange}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"form-group\">\r\n            <label>Email</label>\r\n            <input\r\n              type=\"email\"\r\n              name=\"email\"\r\n              placeholder=\"Enter user's email address\"\r\n              value={formData.email}\r\n              onChange={handleInputChange}\r\n              disabled\r\n            />\r\n          </div>\r\n\r\n          <div className=\"form-row\">\r\n            <div className=\"form-group\">\r\n              <label>Department</label>\r\n              <input\r\n                type=\"text\"\r\n                name=\"department\"\r\n                placeholder=\"Select department\"\r\n                value={formData.department}\r\n                onChange={handleInputChange}\r\n              />\r\n            </div>\r\n            <div className=\"form-group\">\r\n              <label>Designation/Role</label>\r\n              <input\r\n                type=\"text\"\r\n                name=\"designation\"\r\n                placeholder=\"Enter user's role\"\r\n                value={formData.designation}\r\n                onChange={handleInputChange}\r\n                disabled\r\n              />\r\n            </div>\r\n          </div>\r\n        </section>\r\n\r\n        <section className=\"settings-section\">\r\n          <h2>Organization Details</h2>\r\n          <div className=\"form-group\">\r\n            <label>Organization Name</label>\r\n            <input\r\n              type=\"text\"\r\n              name=\"organizationName\"\r\n              placeholder=\"Enter organization name\"\r\n              value={formData.organizationName}\r\n              onChange={handleInputChange}\r\n              disabled\r\n            />\r\n          </div>\r\n\r\n          <div className=\"form-row\">\r\n            <div className=\"form-group\">\r\n              <label>Industry</label>\r\n              <input\r\n                type=\"text\"\r\n                name=\"industry\"\r\n                placeholder=\"Enter industry\"\r\n                value={formData.industry}\r\n                onChange={handleInputChange}\r\n              />\r\n            </div>\r\n            <div className=\"form-group\">\r\n              <label>Sub-Industry</label>\r\n              <input\r\n                type=\"text\"\r\n                name=\"subIndustry\"\r\n                placeholder=\"Enter sub-industry\"\r\n                value={formData.subIndustry}\r\n                onChange={handleInputChange}\r\n              />\r\n            </div>\r\n          </div>\r\n        </section>\r\n\r\n        <section className=\"settings-section\">\r\n          <div className=\"section-header\">\r\n            <h3 className=\"regulations-heading\">\r\n              Regulations List\r\n              {!isAdmin && <span className=\"admin-only-badge\">Admin only</span>}\r\n            </h3>\r\n            {regulationsLoading && <LoadingSpinner size=\"small\" />}\r\n          </div>\r\n          \r\n          {!isAdmin && (\r\n            <div className=\"admin-notice\">\r\n              <p>Only administrators can modify organization regulations. Contact your administrator for changes.</p>\r\n            </div>\r\n          )}\r\n          \r\n          {regulationsError ? (\r\n            <div className=\"error-message\">{regulationsError}</div>\r\n          ) : !regulationsDataReady ? (\r\n            <div className=\"loading-message\">Loading regulations data...</div>\r\n          ) : (\r\n            <div className=\"regulations-list\">\r\n              {regulations.length === 0 ? (\r\n                <p>No regulations available.</p>\r\n              ) : (\r\n                regulations.map(regulation => (\r\n                  <div \r\n                    key={regulation.id} \r\n                    className={`regulation-item ${!isAdmin ? 'disabled-regulation' : ''}`}\r\n                  >\r\n                    <label className={`checkbox-container ${!isAdmin ? 'disabled-checkbox' : ''}`}>\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        checked={selectedRegulations.includes(regulation.id)}\r\n                        onChange={() => handleRegulationToggle(regulation.id)}\r\n                        disabled={!isAdmin}\r\n                      />\r\n                      <span className={`checkmark ${!isAdmin ? 'disabled' : ''}`}></span>\r\n                      <span className=\"regulation-name\">{regulation.name}</span>\r\n                    </label>\r\n                  </div>\r\n                ))\r\n              )}\r\n            </div>\r\n          )}\r\n        </section>\r\n\r\n        <section className=\"settings-section\">\r\n          <h2>Recommend Compliances To Be Added</h2>\r\n          <div className=\"form-group\">\r\n            {/* <label>GXP Compliance Standards</label> */}\r\n            <input\r\n              type=\"text\"\r\n              name=\"standards\"\r\n              placeholder=\"Add applicable standards\"\r\n              value={formData.standards}\r\n              onChange={handleInputChange}\r\n            />\r\n          </div>\r\n       \r\n          <div className=\"form-actions\">\r\n            <button \r\n              className=\"btn-cancel\"\r\n              onClick={() => window.history.back()}\r\n              disabled={loading}\r\n            >\r\n              Cancel\r\n            </button>\r\n            <button \r\n              className=\"btn-save\" \r\n              onClick={handleSaveChanges}\r\n              disabled={loading || !hasChanges}\r\n            >\r\n              {loading ? (\r\n                <span className=\"button-loading\">\r\n                  <LoadingSpinner size=\"small\" />\r\n                  Saving...\r\n                </span>\r\n              ) : (\r\n                'Save Changes'\r\n              )}\r\n            </button>\r\n          </div>\r\n        </section>\r\n\r\n        \r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ProfileSettings; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,WAAW,MAAM,+BAA+B;AACvD,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAO,uBAAuB;AAC9B,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,SAASC,KAAK,EAAEC,cAAc,QAAQ,gBAAgB;AACtD,OAAO,uCAAuC;AAC9C,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,OAAOC,cAAc,MAAM,4CAA4C;AACvE,SAASC,SAAS,QAAQ,8BAA8B;AACxD,SAASC,YAAY,QAAQ,yBAAyB;AACtD,OAAOC,QAAQ,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzC,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC;IACvCoB,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,EAAE;IACfC,gBAAgB,EAAE,EAAE;IACpBC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAC;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8B,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACkC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACoC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACsC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACwC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC0C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC8C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAMkD,WAAW,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAK;IAC5B,IAAID,CAAC,CAACE,MAAM,KAAKD,CAAC,CAACC,MAAM,EAAE,OAAO,KAAK;IACvC,MAAMC,OAAO,GAAG,CAAC,GAAGH,CAAC,CAAC,CAACI,IAAI,CAAC,CAAC;IAC7B,MAAMC,OAAO,GAAG,CAAC,GAAGJ,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC;IAC7B,OAAOD,OAAO,CAACG,KAAK,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,KAAKF,OAAO,CAACG,GAAG,CAAC,CAAC;EAC1D,CAAC;EAED1D,SAAS,CAAC,MAAM;IACd,MAAM2D,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF/B,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMgC,QAAQ,GAAG,MAAM3D,WAAW,CAAC4D,cAAc,CAAC,CAAC;QAEnD,IAAID,QAAQ,IAAIA,QAAQ,CAACR,MAAM,GAAG,CAAC,EAAE;UACnC,MAAMU,IAAI,GAAGF,QAAQ,CAAC,CAAC,CAAC;UACxB,MAAMG,SAAS,GAAG;YAChB1C,UAAU,EAAC,EAAE;YACbK,SAAS,EAAC,EAAE;YACZH,gBAAgB,EAAC,EAAE;YACnBC,QAAQ,EAAC,EAAE;YACXC,WAAW,EAAC;UACd,CAAC;;UAED;UACA,MAAMuC,WAAW,GAAG;YAClB7C,QAAQ,EAAE,GAAG2C,IAAI,CAACG,UAAU,IAAI,EAAE,IAAIH,IAAI,CAACI,SAAS,IAAI,EAAE,EAAE,CAACC,IAAI,CAAC,CAAC;YACnE/C,KAAK,EAAE0C,IAAI,CAAC1C,KAAK,IAAI,EAAE;YACvBC,UAAU,EAAEyC,IAAI,CAACzC,UAAU,IAAI0C,SAAS,CAAC1C,UAAU;YACnDC,WAAW,EAAEwC,IAAI,CAACM,QAAQ,IAAI,EAAE;YAChC7C,gBAAgB,EAAEuC,IAAI,CAACO,YAAY,CAACC,IAAI,IAAIP,SAAS,CAACxC,gBAAgB;YACtEC,QAAQ,EAAEsC,IAAI,CAACtC,QAAQ,IAAIuC,SAAS,CAACvC,QAAQ;YAC7CC,WAAW,EAAEqC,IAAI,CAACS,YAAY,IAAIR,SAAS,CAACtC,WAAW;YACvDC,SAAS,EAAEoC,IAAI,CAACpC,SAAS,IAAIqC,SAAS,CAACrC;UACzC,CAAC;;UAED;UACAR,WAAW,CAAC8C,WAAW,CAAC;;UAExB;UACAxB,mBAAmB,CAACgC,IAAI,CAACC,SAAS,CAACT,WAAW,CAAC,CAAC;QAClD;MACF,CAAC,CAAC,OAAOU,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,kBAAA;QACZC,OAAO,CAAChD,KAAK,CAAC,8BAA8B,EAAE6C,GAAG,CAAC;QAElD,IAAII,YAAY,GAAG,sDAAsD;QACzE,IAAIJ,GAAG,aAAHA,GAAG,gBAAAC,aAAA,GAAHD,GAAG,CAAEK,QAAQ,cAAAJ,aAAA,gBAAAC,kBAAA,GAAbD,aAAA,CAAeK,IAAI,cAAAJ,kBAAA,eAAnBA,kBAAA,CAAqBK,MAAM,EAAE;UAAA,IAAAC,cAAA,EAAAC,mBAAA;UAC/BL,YAAY,GAAGJ,GAAG,aAAHA,GAAG,wBAAAQ,cAAA,GAAHR,GAAG,CAAEK,QAAQ,cAAAG,cAAA,wBAAAC,mBAAA,GAAbD,cAAA,CAAeF,IAAI,cAAAG,mBAAA,uBAAnBA,mBAAA,CAAqBF,MAAM;QAC5C;QAEAnD,QAAQ,CAACgD,YAAY,CAAC;MACxB,CAAC,SAAS;QACRlD,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED+B,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN3D,SAAS,CAAC,MAAM;IACd,MAAMoF,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACFhD,qBAAqB,CAAC,IAAI,CAAC;QAC3BE,mBAAmB,CAAC,IAAI,CAAC;;QAEzB;QACA,MAAM,CAAC+C,cAAc,EAAEC,cAAc,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACzDrF,UAAU,CAACsF,GAAG,CAACnF,QAAQ,CAACoF,WAAW,CAACC,IAAI,CAAC,EACzCxF,UAAU,CAACsF,GAAG,CAACnF,QAAQ,CAACoF,WAAW,CAACE,YAAY,CAAC,CAClD,CAAC;;QAEF;QACA5D,cAAc,CAACqD,cAAc,IAAI,EAAE,CAAC;;QAEpC;QACA,MAAMQ,WAAW,GAAGP,cAAc,CAACQ,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,aAAa,CAAC;QAChE9D,sBAAsB,CAAC2D,WAAW,CAAC;;QAEnC;QACAnD,sBAAsB,CAAC,CAAC,GAAGmD,WAAW,CAAC,CAAC;;QAExC;QACA/C,uBAAuB,CAAC,IAAI,CAAC;MAE/B,CAAC,CAAC,OAAOjB,KAAK,EAAE;QAAA,IAAAoE,eAAA,EAAAC,oBAAA;QACdrB,OAAO,CAAChD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QAEnD,IAAIiD,YAAY,GAAG,+CAA+C;QAClE,IAAIjD,KAAK,aAALA,KAAK,gBAAAoE,eAAA,GAALpE,KAAK,CAAEkD,QAAQ,cAAAkB,eAAA,gBAAAC,oBAAA,GAAfD,eAAA,CAAiBjB,IAAI,cAAAkB,oBAAA,eAArBA,oBAAA,CAAuBjB,MAAM,EAAE;UAAA,IAAAkB,gBAAA,EAAAC,qBAAA;UACjCtB,YAAY,GAAGjD,KAAK,aAALA,KAAK,wBAAAsE,gBAAA,GAALtE,KAAK,CAAEkD,QAAQ,cAAAoB,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBnB,IAAI,cAAAoB,qBAAA,uBAArBA,qBAAA,CAAuBnB,MAAM;QAC9C;QAEA3C,mBAAmB,CAACwC,YAAY,CAAC;MACnC,CAAC,SAAS;QACR1C,qBAAqB,CAAC,KAAK,CAAC;MAC9B;IACF,CAAC;IAEDgD,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAENpF,SAAS,CAAC,MAAM;IACd;IACA,MAAMqG,eAAe,GAAG9D,gBAAgB,IAAIiC,IAAI,CAACC,SAAS,CAACxD,QAAQ,CAAC,KAAKsB,gBAAgB;;IAEzF;IACA,MAAM+D,kBAAkB,GAAG7D,mBAAmB,IAAI,CAACQ,WAAW,CAAChB,mBAAmB,EAAEQ,mBAAmB,CAAC;;IAExG;IACAG,aAAa,CAACyD,eAAe,IAAIC,kBAAkB,CAAC;EACtD,CAAC,EAAE,CAACrF,QAAQ,EAAEgB,mBAAmB,EAAEM,gBAAgB,EAAEE,mBAAmB,CAAC,CAAC;EAE1EzC,SAAS,CAAC,MAAM;IACd,MAAMuG,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI;QACF,MAAM3C,QAAQ,GAAG,MAAM3D,WAAW,CAAC4D,cAAc,CAAC,CAAC;QACnD,IAAID,QAAQ,IAAIA,QAAQ,CAACR,MAAM,GAAG,CAAC,EAAE;UACnC,MAAMU,IAAI,GAAGF,QAAQ,CAAC,CAAC,CAAC;UACxBZ,UAAU,CAACc,IAAI,CAACM,QAAQ,KAAK5D,SAAS,CAAC;QACzC;MACF,CAAC,CAAC,OAAOkE,GAAG,EAAE;QAAA,IAAA8B,cAAA,EAAAC,mBAAA;QACZ5B,OAAO,CAAChD,KAAK,CAAC,8BAA8B,EAAE6C,GAAG,CAAC;QAElD,IAAII,YAAY,GAAG,uDAAuD;QAC1E,IAAIJ,GAAG,aAAHA,GAAG,gBAAA8B,cAAA,GAAH9B,GAAG,CAAEK,QAAQ,cAAAyB,cAAA,gBAAAC,mBAAA,GAAbD,cAAA,CAAexB,IAAI,cAAAyB,mBAAA,eAAnBA,mBAAA,CAAqBxB,MAAM,EAAE;UAAA,IAAAyB,cAAA,EAAAC,mBAAA;UAC/B7B,YAAY,GAAGJ,GAAG,aAAHA,GAAG,wBAAAgC,cAAA,GAAHhC,GAAG,CAAEK,QAAQ,cAAA2B,cAAA,wBAAAC,mBAAA,GAAbD,cAAA,CAAe1B,IAAI,cAAA2B,mBAAA,uBAAnBA,mBAAA,CAAqB1B,MAAM;QAC5C;QAEAnD,QAAQ,CAACgD,YAAY,CAAC;MACxB;IACF,CAAC;IAEDyB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEvC,IAAI;MAAEwC;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChC7F,WAAW,CAAC8F,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAAC1C,IAAI,GAAGwC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,sBAAsB,GAAIC,YAAY,IAAK;IAC/C;IACA,IAAI,CAACnE,OAAO,EAAE;IAEdb,sBAAsB,CAAC8E,IAAI,IAAI;MAC7B,IAAIA,IAAI,CAACG,QAAQ,CAACD,YAAY,CAAC,EAAE;QAC/B,OAAOF,IAAI,CAACI,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKH,YAAY,CAAC;MAC/C,CAAC,MAAM;QACL,OAAO,CAAC,GAAGF,IAAI,EAAEE,YAAY,CAAC;MAChC;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF1F,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM2F,iBAAiB,GAAG;QACxB,GAAGtG,QAAQ;QACXE,QAAQ,EAAEV,YAAY,CAACQ,QAAQ,CAACE,QAAQ,CAAC;QACzCE,UAAU,EAAEZ,YAAY,CAACQ,QAAQ,CAACI,UAAU,CAAC;QAC7CC,WAAW,EAAEb,YAAY,CAACQ,QAAQ,CAACK,WAAW,CAAC;QAC/CE,QAAQ,EAAEf,YAAY,CAACQ,QAAQ,CAACO,QAAQ,CAAC;QACzCC,WAAW,EAAEhB,YAAY,CAACQ,QAAQ,CAACQ,WAAW;QAC9C;MACF,CAAC;;MAED;MACA,MAAM;QAAEuD,IAAI,EAAE;UAAElB;QAAK;MAAE,CAAC,GAAG,MAAMpD,QAAQ,CAAC8G,IAAI,CAACC,OAAO,CAAC,CAAC;MACxD,MAAMC,MAAM,GAAG5D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuD,EAAE;MAEvB,IAAI,CAACK,MAAM,EAAE;QACX,MAAM,IAAIC,KAAK,CAAC,mBAAmB,CAAC;MACtC;;MAEA;MACA,MAAMC,SAAS,GAAGL,iBAAiB,CAACpG,QAAQ,CAACgD,IAAI,CAAC,CAAC,CAAC0D,KAAK,CAAC,KAAK,CAAC;MAChE,MAAMC,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;MACpC,MAAMG,QAAQ,GAAGH,SAAS,CAACI,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;;MAEnD;MACA,MAAMC,UAAU,GAAG;QACjBjE,UAAU,EAAE6D,SAAS;QACrB5D,SAAS,EAAE6D;MACb,CAAC;;MAED;MACA,MAAMI,eAAe,GAAG,MAAMlI,WAAW,CAAC4D,cAAc,CAAC,CAAC;MAC1D,MAAMuE,WAAW,GAAGD,eAAe,IAAIA,eAAe,CAAC/E,MAAM,GAAG,CAAC,GAAG+E,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI;;MAE7F;MACA,IAAIZ,iBAAiB,CAAC7F,SAAS,IAAI6F,iBAAiB,CAAC7F,SAAS,CAACyC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAC5E;QACA,IAAI,CAAC+D,UAAU,CAACG,QAAQ,EAAE;UACxBH,UAAU,CAACG,QAAQ,GAAG,CAAC,CAAC;QAC1B;;QAEA;QACA,IAAIC,mBAAmB,GAAG,EAAE;QAE5B,IAAIF,WAAW,IACXA,WAAW,CAACC,QAAQ,IACpBD,WAAW,CAACC,QAAQ,CAACE,4BAA4B,EAAE;UACrD;UACA,IAAIC,KAAK,CAACC,OAAO,CAACL,WAAW,CAACC,QAAQ,CAACE,4BAA4B,CAAC,EAAE;YACpED,mBAAmB,GAAGF,WAAW,CAACC,QAAQ,CAACE,4BAA4B;UACzE;UACA;UAAA,KACK,IAAI,OAAOH,WAAW,CAACC,QAAQ,CAACE,4BAA4B,KAAK,QAAQ,EAAE;YAC9ED,mBAAmB,GAAG,CAACF,WAAW,CAACC,QAAQ,CAACE,4BAA4B,CAAC;UAC3E;QACF;;QAEA;QACA,IAAI,CAACD,mBAAmB,CAACnB,QAAQ,CAACI,iBAAiB,CAAC7F,SAAS,CAACyC,IAAI,CAAC,CAAC,CAAC,EAAE;UACrEmE,mBAAmB,CAACI,IAAI,CAACnB,iBAAiB,CAAC7F,SAAS,CAACyC,IAAI,CAAC,CAAC,CAAC;QAC9D;;QAEA;QACA+D,UAAU,CAACG,QAAQ,CAACE,4BAA4B,GAAGD,mBAAmB;MACxE;MAEAzD,OAAO,CAAC8D,GAAG,CAAC,kCAAkC,EAAET,UAAU,CAAC;;MAE3D;MACA,MAAMnD,QAAQ,GAAG,MAAM9E,WAAW,CAAC2I,iBAAiB,CAACV,UAAU,EAAER,MAAM,CAAC;;MAExE;MACA,MAAMmB,gBAAgB,GAAG5G,mBAAmB,CAACmF,MAAM,CAACC,EAAE,IAAI,CAAC5E,mBAAmB,CAAC0E,QAAQ,CAACE,EAAE,CAAC,CAAC;MAC5F,MAAMyB,mBAAmB,GAAGrG,mBAAmB,CAAC2E,MAAM,CAACC,EAAE,IAAI,CAACpF,mBAAmB,CAACkF,QAAQ,CAACE,EAAE,CAAC,CAAC;;MAE/F;MACA,KAAK,MAAMH,YAAY,IAAI2B,gBAAgB,EAAE;QAC3C,MAAM1I,UAAU,CAAC4I,IAAI,CAACzI,QAAQ,CAACoF,WAAW,CAACE,YAAY,GAAG,IAAIsB,YAAY,EAAE,CAAC;MAC/E;;MAEA;MACA,KAAK,MAAMA,YAAY,IAAI4B,mBAAmB,EAAE;QAC9C,MAAM3I,UAAU,CAAC6I,MAAM,CAAC1I,QAAQ,CAACoF,WAAW,CAACE,YAAY,GAAG,IAAIsB,YAAY,EAAE,CAAC;MACjF;MAEArC,OAAO,CAAC8D,GAAG,CAAC,0BAA0B,EAAE5D,QAAQ,CAAC;;MAEjD;MACA3E,KAAK,CAAC6I,OAAO,CAAC,+BAA+B,EAAE;QAC7CC,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE,IAAI;QACfC,eAAe,EAAE,KAAK;QACtBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,IAAI;QAClBC,SAAS,EAAE;MACb,CAAC,CAAC;;MAEF;MACA/G,mBAAmB,CAACgC,IAAI,CAACC,SAAS,CAAC8C,iBAAiB,CAAC,CAAC;MACtD7E,sBAAsB,CAAC,CAAC,GAAGT,mBAAmB,CAAC,CAAC;;MAEhD;MACAf,WAAW,CAAC8F,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPtF,SAAS,EAAE;MACb,CAAC,CAAC,CAAC;IAEL,CAAC,CAAC,OAAOgD,GAAG,EAAE;MAAA,IAAA8E,cAAA,EAAAC,mBAAA;MACZ5E,OAAO,CAAChD,KAAK,CAAC,yBAAyB,EAAE6C,GAAG,CAAC;MAE7C,IAAII,YAAY,GAAG,6CAA6C;MAChE,IAAIJ,GAAG,aAAHA,GAAG,gBAAA8E,cAAA,GAAH9E,GAAG,CAAEK,QAAQ,cAAAyE,cAAA,gBAAAC,mBAAA,GAAbD,cAAA,CAAexE,IAAI,cAAAyE,mBAAA,eAAnBA,mBAAA,CAAqBxE,MAAM,EAAE;QAAA,IAAAyE,cAAA,EAAAC,mBAAA;QAC/B7E,YAAY,GAAGJ,GAAG,aAAHA,GAAG,wBAAAgF,cAAA,GAAHhF,GAAG,CAAEK,QAAQ,cAAA2E,cAAA,wBAAAC,mBAAA,GAAbD,cAAA,CAAe1E,IAAI,cAAA2E,mBAAA,uBAAnBA,mBAAA,CAAqB1E,MAAM;MAC5C;MAEAnD,QAAQ,CAACgD,YAAY,CAAC;;MAEtB;MACA1E,KAAK,CAACyB,KAAK,CAAC,6BAA6BiD,YAAY,EAAE,EAAE;QACvDoE,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE,IAAI;QACfC,eAAe,EAAE,KAAK;QACtBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,IAAI;QAClBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC,SAAS;MACR3H,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,IAAI,CAACV,QAAQ,CAACG,KAAK,EAAE;IAC9B,oBAAOR,OAAA;MAAKgJ,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAC;IAAuB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACzE;EAEA,oBACErJ,OAAA,CAAAE,SAAA;IAAA+I,QAAA,gBACEjJ,OAAA,CAACV,UAAU;MAAA4J,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfrJ,OAAA,CAACP,cAAc;MAAAyJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjBrJ,OAAA;MAAKgJ,SAAS,EAAC,kBAAkB;MAAAC,QAAA,GAC9BhI,KAAK,iBAAIjB,OAAA;QAAKgJ,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEhI;MAAK;QAAAiI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEtDrJ,OAAA;QAASgJ,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBACnCjJ,OAAA;UAAAiJ,QAAA,EAAI;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9BrJ,OAAA;UAAAiJ,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BrJ,OAAA;UAAKgJ,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBjJ,OAAA;YAAAiJ,QAAA,EAAO;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxBrJ,OAAA;YACEsJ,IAAI,EAAC,MAAM;YACX5F,IAAI,EAAC,UAAU;YACf6F,WAAW,EAAC,sBAAsB;YAClCrD,KAAK,EAAE7F,QAAQ,CAACE,QAAS;YACzBiJ,QAAQ,EAAExD;UAAkB;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENrJ,OAAA;UAAKgJ,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBjJ,OAAA;YAAAiJ,QAAA,EAAO;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpBrJ,OAAA;YACEsJ,IAAI,EAAC,OAAO;YACZ5F,IAAI,EAAC,OAAO;YACZ6F,WAAW,EAAC,4BAA4B;YACxCrD,KAAK,EAAE7F,QAAQ,CAACG,KAAM;YACtBgJ,QAAQ,EAAExD,iBAAkB;YAC5ByD,QAAQ;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENrJ,OAAA;UAAKgJ,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBjJ,OAAA;YAAKgJ,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBjJ,OAAA;cAAAiJ,QAAA,EAAO;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzBrJ,OAAA;cACEsJ,IAAI,EAAC,MAAM;cACX5F,IAAI,EAAC,YAAY;cACjB6F,WAAW,EAAC,mBAAmB;cAC/BrD,KAAK,EAAE7F,QAAQ,CAACI,UAAW;cAC3B+I,QAAQ,EAAExD;YAAkB;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNrJ,OAAA;YAAKgJ,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBjJ,OAAA;cAAAiJ,QAAA,EAAO;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/BrJ,OAAA;cACEsJ,IAAI,EAAC,MAAM;cACX5F,IAAI,EAAC,aAAa;cAClB6F,WAAW,EAAC,mBAAmB;cAC/BrD,KAAK,EAAE7F,QAAQ,CAACK,WAAY;cAC5B8I,QAAQ,EAAExD,iBAAkB;cAC5ByD,QAAQ;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEVrJ,OAAA;QAASgJ,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBACnCjJ,OAAA;UAAAiJ,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BrJ,OAAA;UAAKgJ,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBjJ,OAAA;YAAAiJ,QAAA,EAAO;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChCrJ,OAAA;YACEsJ,IAAI,EAAC,MAAM;YACX5F,IAAI,EAAC,kBAAkB;YACvB6F,WAAW,EAAC,yBAAyB;YACrCrD,KAAK,EAAE7F,QAAQ,CAACM,gBAAiB;YACjC6I,QAAQ,EAAExD,iBAAkB;YAC5ByD,QAAQ;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENrJ,OAAA;UAAKgJ,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBjJ,OAAA;YAAKgJ,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBjJ,OAAA;cAAAiJ,QAAA,EAAO;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvBrJ,OAAA;cACEsJ,IAAI,EAAC,MAAM;cACX5F,IAAI,EAAC,UAAU;cACf6F,WAAW,EAAC,gBAAgB;cAC5BrD,KAAK,EAAE7F,QAAQ,CAACO,QAAS;cACzB4I,QAAQ,EAAExD;YAAkB;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNrJ,OAAA;YAAKgJ,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBjJ,OAAA;cAAAiJ,QAAA,EAAO;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3BrJ,OAAA;cACEsJ,IAAI,EAAC,MAAM;cACX5F,IAAI,EAAC,aAAa;cAClB6F,WAAW,EAAC,oBAAoB;cAChCrD,KAAK,EAAE7F,QAAQ,CAACQ,WAAY;cAC5B2I,QAAQ,EAAExD;YAAkB;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEVrJ,OAAA;QAASgJ,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBACnCjJ,OAAA;UAAKgJ,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BjJ,OAAA;YAAIgJ,SAAS,EAAC,qBAAqB;YAAAC,QAAA,GAAC,kBAElC,EAAC,CAAC9G,OAAO,iBAAInC,OAAA;cAAMgJ,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,EACJ9H,kBAAkB,iBAAIvB,OAAA,CAACL,cAAc;YAAC+J,IAAI,EAAC;UAAO;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,EAEL,CAAClH,OAAO,iBACPnC,OAAA;UAAKgJ,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BjJ,OAAA;YAAAiJ,QAAA,EAAG;UAAgG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpG,CACN,EAEA5H,gBAAgB,gBACfzB,OAAA;UAAKgJ,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAExH;QAAgB;UAAAyH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,GACrD,CAACpH,oBAAoB,gBACvBjC,OAAA;UAAKgJ,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,gBAElErJ,OAAA;UAAKgJ,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC9B9H,WAAW,CAACqB,MAAM,KAAK,CAAC,gBACvBxC,OAAA;YAAAiJ,QAAA,EAAG;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,GAEhClI,WAAW,CAAC+D,GAAG,CAACyE,UAAU,iBACxB3J,OAAA;YAEEgJ,SAAS,EAAE,mBAAmB,CAAC7G,OAAO,GAAG,qBAAqB,GAAG,EAAE,EAAG;YAAA8G,QAAA,eAEtEjJ,OAAA;cAAOgJ,SAAS,EAAE,sBAAsB,CAAC7G,OAAO,GAAG,mBAAmB,GAAG,EAAE,EAAG;cAAA8G,QAAA,gBAC5EjJ,OAAA;gBACEsJ,IAAI,EAAC,UAAU;gBACfM,OAAO,EAAEvI,mBAAmB,CAACkF,QAAQ,CAACoD,UAAU,CAAClD,EAAE,CAAE;gBACrD+C,QAAQ,EAAEA,CAAA,KAAMnD,sBAAsB,CAACsD,UAAU,CAAClD,EAAE,CAAE;gBACtDgD,QAAQ,EAAE,CAACtH;cAAQ;gBAAA+G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACFrJ,OAAA;gBAAMgJ,SAAS,EAAE,aAAa,CAAC7G,OAAO,GAAG,UAAU,GAAG,EAAE;cAAG;gBAAA+G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnErJ,OAAA;gBAAMgJ,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAEU,UAAU,CAACjG;cAAI;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD;UAAC,GAZHM,UAAU,CAAClD,EAAE;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaf,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEVrJ,OAAA;QAASgJ,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBACnCjJ,OAAA;UAAAiJ,QAAA,EAAI;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1CrJ,OAAA;UAAKgJ,SAAS,EAAC,YAAY;UAAAC,QAAA,eAEzBjJ,OAAA;YACEsJ,IAAI,EAAC,MAAM;YACX5F,IAAI,EAAC,WAAW;YAChB6F,WAAW,EAAC,0BAA0B;YACtCrD,KAAK,EAAE7F,QAAQ,CAACS,SAAU;YAC1B0I,QAAQ,EAAExD;UAAkB;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENrJ,OAAA;UAAKgJ,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjJ,OAAA;YACEgJ,SAAS,EAAC,YAAY;YACtBa,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;YACrCP,QAAQ,EAAE1I,OAAQ;YAAAkI,QAAA,EACnB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrJ,OAAA;YACEgJ,SAAS,EAAC,UAAU;YACpBa,OAAO,EAAEnD,iBAAkB;YAC3B+C,QAAQ,EAAE1I,OAAO,IAAI,CAACgB,UAAW;YAAAkH,QAAA,EAEhClI,OAAO,gBACNf,OAAA;cAAMgJ,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC9BjJ,OAAA,CAACL,cAAc;gBAAC+J,IAAI,EAAC;cAAO;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,aAEjC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,GAEP;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGP,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACjJ,EAAA,CAtfID,eAAe;AAAA8J,EAAA,GAAf9J,eAAe;AAwfrB,eAAeA,eAAe;AAAC,IAAA8J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}